import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listAlarm(query) {
  return request({
    url: '/system/centrealarm/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getAlarm(id) {
  return request({
    url: '/system/centrealarm/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addAlarm(data) {
  return request({
    url: '/system/centrealarm',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateAlarm(data) {
  return request({
    url: '/system/centrealarm',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delAlarm(id) {
  return request({
    url: '/system/centrealarm/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportAlarm(query) {
  return request({
    url: '/system/centrealarm/export',
    method: 'get',
    params: query
  })
}
