<template>
  <div class="app-container">
    <el-col :span="24" class="card-box">
      <el-row style="padding-right: 5px; padding-left: 5px">
        <el-card>
          <el-col :xs="24" :sm="24" :md="12" :lg="14">
            <el-form
              :model="queryParams"
              ref="queryForm"
              :inline="true"
              label-width="68px"
              style="margin-left: -10px"
            >
              <el-form-item
                :label="$t('huanKong.pigHouseUnit')"
                class="card-box-re"
              >
                <el-cascader
                  :required="true"
                  v-model="value"
                  :options="options"
                  @change="handleNodeClick"
                  :placeholder="$t('huanKong.choosePigHouse')"
                ></el-cascader>
              </el-form-item>
              <el-form-item
                :label="$t('huanKong.nowStage')"
                class="card-box-re"
              >
                <el-tag effect="dark">阶段一</el-tag>
              </el-form-item>
              <el-form-item
                :label="$t('huanKong.envWorkStatus')"
                class="card-box-re"
              >
                <!-- <el-tag
                  :type="scope.row.slavenetwork === 'ok' ? 'success' : 'danger'"
                  disable-transitions
                  >{{
                    scope.row.slavenetwork === "ok"
                      ? $t("liaota.online")
                      : $t("liaota.offline")
                  }}</el-tag
                > -->

                <el-tag
                  effect="dark"
                  :type="
                    !this._.isEmpty(slaveList)
                      ? slaveList.slavenetwork === 'ok' &&
                        handleOnline(slaveList.uptime)
                        ? ''
                        : 'danger'
                      : 'danger'
                  "
                  >{{
                    !this._.isEmpty(slaveList)
                      ? slaveList.slavenetwork === "ok" &&
                        handleOnline(slaveList.uptime)
                        ? $t("liaota.online")
                        : $t("liaota.offline")
                      : $t("liaota.offline")
                  }}</el-tag
                >
              </el-form-item>

              <!-- <a @click="handleLink">{{
                  $t("menu.RingControlParameterAdjustment")
                }}</a> -->
            </el-form>
          </el-col>
          <el-col :offset="4" :xs="24" :sm="24" :md="12" :lg="6">
            <!-- <span style="color: #5a5e66" class="font_comom">
              {{ $t("huanKong.updateTime") }} : {{ slaveList.uptime }}</span
            > -->
            <el-form :inline="true" label-width="68px">
              <el-form-item label="" class="card-box-re">
                <svg-icon
                  icon-class="system"
                  class="system_svg"
                  @click="handleLink"
                />
              </el-form-item>
              <el-form-item
                :label="$t('huanKong.updateTime')"
                class="card-box-re"
              >
                <span> {{ slaveList.uptime }}</span>
              </el-form-item>
            </el-form>
          </el-col>
        </el-card>
      </el-row>
    </el-col>
    <el-col :span="24" class="card-box">
      <!-- <el-card> -->
      <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane
          :label="$t('huanKong.viewMonitoring')"
          name="first"
          :lazy="true"
        > -->
      <el-row>
        <el-col :xs="24" :sm="24" :md="6" :lg="6" class="card-box-re">
          <el-card>
            <el-row style="display: flex">
              <el-col :span="8" style="text-align: center">
                <span class="demonstration card font_comom">{{
                  $t("huanKong.targetTem")
                }}</span>

                <el-image
                  style="height: 80px"
                  :src="require('@/assets/icons/environment_svg/temper.png')"
                ></el-image>
              </el-col>
              <el-col :span="1">
                <el-divider
                  style="height: 100%"
                  direction="vertical"
                ></el-divider>
              </el-col>
              <!-- <el-col :span="15">
                <el-row class="spanTem">
                  <span class="spanDu">{{ $t("huanKong.targetTem") }}</span>
                  <span class="spanText">{{
                    slaveList.temperature ? slaveList.temperature : "---"
                  }}</span>
                  <span class="spanDu" style="font-family: Helvetica">℃</span>
                </el-row>
                <el-row class="spanTem">
                  <span class="spanDu">{{ $t("huanKong.nowTem") }}</span>
                  <span class="spanText">{{
                    slaveList.temperature ? slaveList.temperature : "---"
                  }}</span>
                  <span class="spanDu" style="font-family: Helvetica">℃</span>
                </el-row>
              </el-col> -->
              <el-col :span="9" class="spanS">
                <span>{{ "---" }}</span>
              </el-col>
              <el-col :span="6" class="rowStyle">
                <span>℃</span>
              </el-col>
            </el-row>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="6" :lg="6" class="card-box-re">
          <el-card>
            <el-row style="display: flex">
              <el-col :span="8" style="text-align: center">
                <span class="demonstration card font_comom">{{
                  $t("huanKong.nowTem")
                }}</span>

                <el-image
                  style="height: 80px"
                  :src="require('@/assets/icons/environment_svg/temper1.png')"
                ></el-image>
              </el-col>
              <el-col :span="1">
                <el-divider
                  style="height: 100%"
                  direction="vertical"
                ></el-divider>
              </el-col>
              <el-col :span="9" class="spanS">
                <span>{{
                  slaveList.temperature ? slaveList.temperature : "---"
                }}</span>
              </el-col>
              <el-col :span="6" class="rowStyle">
                <span>℃</span>
              </el-col>
            </el-row>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="6" :lg="6" class="card-box-re">
          <el-card>
            <el-row style="display: flex">
              <el-col :span="8" style="text-align: center">
                <span class="demonstration card font_comom">{{
                  $t("huanKong.humidity")
                }}</span>

                <el-image
                  style="height: 80px"
                  :src="require('@/assets/icons/environment_svg/humindity.png')"
                ></el-image>
              </el-col>
              <el-col :span="1">
                <el-divider
                  style="height: 100%"
                  direction="vertical"
                ></el-divider>
              </el-col>
              <el-col :span="9" class="spanS">
                <span>{{
                  !this._.isNil(slaveList.humidity) ? slaveList.humidity : "---"
                }}</span>
              </el-col>
              <el-col :span="6" class="rowStyle">
                <span>%</span>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
        <!-- 
        <el-col :xs="24" :sm="24" :md="6" :lg="6" class="card-box-re">
          <el-card>
            <el-row style="display: flex">
              <el-col :span="8" style="text-align: center">
                <span class="demonstration card font_comom">{{
                  $t("huanKong.waterLevel")
                }}</span>
                <el-image
                  style="height: 80px"
                  :src="
                    require('@/assets/icons/environment_svg/water_level.png')
                  "
                ></el-image>
              </el-col>
              <el-col :span="1">
                <el-divider
                  style="height: 100%"
                  direction="vertical"
                ></el-divider>
              </el-col>
              <el-col :span="9" class="spanS">
                <span
                  :style="{
                    'font-size':
                      slaveList.water && slaveList.water.toString().length > 4
                        ? '35px'
                        : '45px',
                  }"
                  >{{
                    !this._.isNil(slaveList.water) ? slaveList.water : "---"
                  }}</span
                >
              </el-col>
              <el-col :span="6" class="rowStyle">
                <span>L</span>
              </el-col>
            </el-row>
          </el-card>
        </el-col> -->
        <el-col :xs="24" :sm="24" :md="6" :lg="6" class="card-box-re">
          <el-card>
            <el-row style="display: flex">
              <el-col :span="8" style="text-align: center">
                <span class="demonstration card font_comom">{{
                  $t("huanKong.alarmInformation")
                }}</span>
                <el-image
                  style="height: 80px"
                  :src="require('@/assets/icons/environment_svg/alarm1.png')"
                ></el-image>
              </el-col>
              <el-col :span="1">
                <el-divider
                  style="height: 100%"
                  direction="vertical"
                ></el-divider>
              </el-col>
              <el-col :span="9" class="spanAlarm">
                <span
                  :style="
                    !this._.isEmpty(slaveList.alarmmsg) ? '' : 'font-size:50px'
                  "
                  >{{
                    !this._.isEmpty(slaveList.alarmmsg)
                      ? slaveList.alarmmsg
                      : "---"
                  }}</span
                >
              </el-col>
              <!-- <el-col :span="6" class="rowStyle">
                    <span>L</span>
                  </el-col> -->
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="24" :md="24" :lg="12" class="card-box-re">
          <el-card>
            <div slot="header">
              <span class="font_comom">{{
                $t("huanKong.temperatureCurve")
              }}</span>
            </div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <div ref="category13" style="height: 426px" />
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="12">
          <el-row class="card-box-re">
            <el-card>
              <div slot="header">
                <span class="font_comom">{{
                  $t("huanKong.fixedFrequencyFan")
                }}</span>
              </div>
              <el-row>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">1</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan1 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">2</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan2 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">3</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan3 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">4</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan4 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">5</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan5 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">6</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan6 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">7</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan7 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">8</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan8 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">9</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan9 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">10</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan10 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">11</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan11 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="4" :sm="4" :md="4" :lg="2">
                  <el-col><span class="demonstration">12</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.dpfan12 === 1"
                      :src="dpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                      class="gif"
                    ></el-image>
                    <el-image
                      v-else
                      :src="dpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
              </el-row>
            </el-card>
          </el-row>
          <el-row class="card-box-re">
            <el-card>
              <div slot="header">
                <span class="font_comom">{{ $t("huanKong.inverterFan") }}</span>
              </div>
              <el-row>
                <el-col :xs="6" :sm="6" :md="6" :lg="2">
                  <el-col><span class="demonstration">1</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.bpfan1 === 1"
                      :src="bpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                    <el-image
                      v-else
                      :src="bpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="6" :sm="6" :md="6" :lg="2">
                  <el-col><span class="demonstration">2</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.bpfan2 === 1"
                      :src="bpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                    <el-image
                      v-else
                      :src="bpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="6" :sm="6" :md="6" :lg="2">
                  <el-col><span class="demonstration">3</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.bpfan3 === 1"
                      :src="bpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                    <el-image
                      v-else
                      :src="bpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>
                <el-col :xs="6" :sm="6" :md="6" :lg="2">
                  <el-col><span class="demonstration">4</span></el-col>
                  <el-col style="text-align: center">
                    <el-image
                      v-if="slaveList.bpfan4 === 1"
                      :src="bpfanurlOn"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                    <el-image
                      v-else
                      :src="bpfanurlOff"
                      style="width: 60%; height: 60%; align-content: center"
                    ></el-image>
                  </el-col>
                </el-col>

                <el-col
                  :xs="12"
                  :sm="12"
                  :md="12"
                  :lg="4"
                  style="min-height: 10px"
                >
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="12">
                  <el-progress
                    style="margin-top: 30px"
                    :text-inside="true"
                    :stroke-width="26"
                    :percentage="
                      !this._.isNil(slaveList.bpfan1) &&
                      0 <=
                        ((slaveList.bpfan1 +
                          slaveList.bpfan2 +
                          slaveList.bpfan3 +
                          slaveList.bpfan4) /
                          4) *
                          100 <=
                        100
                        ? ((slaveList.bpfan1 +
                            slaveList.bpfan2 +
                            slaveList.bpfan3 +
                            slaveList.bpfan4) /
                            4) *
                          100
                        : 0
                    "
                  ></el-progress>
                </el-col>
              </el-row>
            </el-card>
          </el-row>
          <el-row>
            <el-col :xs="12" :sm="12" :md="12" :lg="6" class="card-box-re">
              <el-card style="text-align: center">
                <span slot="header" class="font_comom">{{
                  $t("huanKong.curtain1")
                }}</span>
                <el-progress
                  type="dashboard"
                  :width="100"
                  color="#00833e"
                  :percentage="
                    !this._.isNil(slaveList.curtain1)
                      ? slaveList.curtain > 100
                        ? 100
                        : slaveList.curtain1
                      : 0
                  "
                ></el-progress>
              </el-card>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="6" class="card-box-re">
              <el-card style="text-align: center">
                <span slot="header" class="font_comom">{{
                  $t("huanKong.curtain2")
                }}</span>
                <el-progress
                  type="dashboard"
                  color="#00833e"
                  :width="100"
                  :percentage="
                    !this._.isNil(slaveList.curtain2)
                      ? slaveList.curtain2 > 100
                        ? 100
                        : slaveList.curtain2
                      : 0
                  "
                ></el-progress>
              </el-card>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="6" class="card-box-re">
              <el-card style="text-align: center">
                <span slot="header" class="font_comom">{{
                  $t("huanKong.curtain3")
                }}</span>
                <el-progress
                  type="dashboard"
                  color="#00833e"
                  :width="100"
                  :percentage="
                    !this._.isNil(slaveList.curtain3)
                      ? slaveList.curtain3 > 100
                        ? 100
                        : slaveList.curtain3
                      : 0
                  "
                ></el-progress>
              </el-card>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="6" class="card-box-re">
              <el-card style="text-align: center">
                <span slot="header" class="font_comom">{{
                  $t("huanKong.curtain4")
                }}</span>
                <el-progress
                  type="dashboard"
                  color="#00833e"
                  :width="100"
                  :percentage="
                    !this._.isNil(slaveList.curtain4)
                      ? slaveList.curtain4 > 100
                        ? 100
                        : slaveList.curtain4
                      : 0
                  "
                ></el-progress>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <!-- </el-tab-pane> -->
      <!-- <template v-if="checkPermi(['huanKong:homeData:control'])">
          <el-tab-pane
            :label="$t('huanKong.parameterRealTimeIssued')"
            name="second"
            :lazy="true"
          >
            <template>
              <div>
                <el-row :gutter="10" class="mb8">
                  <el-col :span="1.5">
                    <el-button
                      size="mini"
                      type="primary"
                      @click="submitFormSet"
                      :loading="loadingSet"
                      v-hasPermi="['system:param:list']"
                      >{{ $t("huanKong.refresh") }}</el-button
                    >
                  </el-col>
                  <el-col :span="1.5" class="warning">
                    <span style="color: #f44d03; margin-right: 5px">{{
                      $t("huanKong.editTip")
                    }}</span>
                  </el-col>
                </el-row>
                <el-table :data="paramList" v-loading="loading || loadingSet">
                  <el-table-column
                    :label="$t('common.serialNumber')"
                    align="center"
                    prop="id"
                  />
                  <el-table-column
                    :label="$t('huanKong.parameterName')"
                    align="center"
                    prop="paraname"
                  >
                    <template slot-scope="scope">
                      <span v-if="language == 'ru'">
                        {{ scope.row.rusParaname }}
                      </span>
                      <span v-else-if="language == 'en'">
                        {{ scope.row.engParaname }}
                      </span>
                      <span v-else>
                        {{ scope.row.paraname }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('huanKong.point')"
                    align="center"
                    prop="sitecode"
                  />
                  <el-table-column
                    :label="$t('huanKong.parameterValue')"
                    prop="paravalue"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <span v-if="scope.row.type == 1">
                          <el-input-number
                            size="mini"
                            :placeholder="$t('huanKong.enterParameterValue')"
                            v-model="scope.row.paravalue"
                            :precision="1"
                            :step="0.1"
                            :min="0"
                          ></el-input-number>
                        </span>
                        <span v-else>
                          <el-input-number
                            size="mini"
                            :placeholder="$t('huanKong.enterParameterValue')"
                            v-model="scope.row.paravalue"
                            :precision="0"
                            :step="1"
                            :min="0"
                          ></el-input-number>
                        </span>
                      </span>
                      <span v-else>{{ scope.row.paravalue }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('common.operate')">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        :icon="scope.row.show ? '' : 'el-icon-edit'"
                        @click="edit(scope.row, scope.$index)"
                        v-hasPermi="['system:param:edit']"
                        >{{
                          scope.row.show
                            ? $t("common.cancel")
                            : $t("common.update")
                        }}</el-button
                      >
                      <el-button
                        size="mini"
                        type="text"
                        @click="
                          handleDataSave(scope.row, scope.$index, paramList)
                        "
                        v-hasPermi="['system:param:edit']"
                        >{{
                          scope.row.show ? $t("common.save") : ""
                        }}</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-tab-pane>

          <el-tab-pane
            v-for="(item, index) in jieduanList"
            v-bind:label="
              item.id == 0
                ? $t('huanKong.minimumVentilationPhase')
                : item.id == -1
                ? $t('huanKong.temperatureStage')
                : $t('huanKong.stage') +
                  `${item.id}` +
                  $t('huanKong.parameterIssuance')
            "
            v-bind:key="index"
            :lazy="true"
          >
            <template>
              <div>
                <el-row :gutter="10" class="mb8">
                  <el-col :span="1.5">
                    <el-button
                      size="mini"
                      type="primary"
                      @click="submitFormSet"
                      :loading="loadingSet"
                      v-hasPermi="['system:param:list']"
                      >{{ $t("huanKong.refresh") }}</el-button
                    >
                  </el-col>
                  <el-col :span="1.5" class="warning">
                    <span style="color: #f44d03; margin-right: 5px">{{
                      $t("huanKong.editTip")
                    }}</span>
                  </el-col>
                </el-row>
                <el-table
                  :data="item.data"
                  v-loading="jieduanloading || loadingSet"
                >
                  <el-table-column
                    :label="$t('huanKong.stage')"
                    align="center"
                    prop="jieduan"
                  />
                  <el-table-column
                    :label="$t('huanKong.stageOrdering')"
                    align="center"
                    prop="no"
                  />
                  <el-table-column
                    :label="$t('huanKong.parameterName')"
                    align="center"
                    prop="paraname"
                  >
                    <template slot-scope="scope">
                      <span v-if="language == 'ru'">
                        {{ scope.row.rusParaname }}
                      </span>
                      <span v-else-if="language == 'en'">
                        {{ scope.row.engParaname }}
                      </span>
                      <span v-else>
                        {{ scope.row.paraname }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('huanKong.point')"
                    align="center"
                    prop="sitecode"
                  />
                  <el-table-column
                    :label="$t('huanKong.parameterValue')"
                    prop="paravalue"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <span v-if="scope.row.type == 2 || scope.row.type == 4">
                          <el-input
                            readonly
                            size="mini"
                            :placeholder="$t('huanKong.enterParameterValue')"
                            v-model="scope.row.paravalue"
                            suffix-icon="el-icon-setting"
                            @focus="
                              handleFocus(
                                scope.row.paravalue,
                                scope.row,
                                scope.$index,
                                item.data
                              )
                            "
                          >
                          </el-input>
                        </span>
                        <span v-else-if="scope.row.type == 1">
                          <el-input-number
                            size="mini"
                            :placeholder="$t('huanKong.enterParameterValue')"
                            v-model="scope.row.paravalue"
                            :precision="1"
                            :step="0.1"
                            :min="0"
                          ></el-input-number>
                        </span>

                        <span v-else>
                          <el-input-number
                            size="mini"
                            :placeholder="$t('huanKong.enterParameterValue')"
                            v-model="scope.row.paravalue"
                            :precision="0"
                            :step="1"
                            :min="0"
                          ></el-input-number>
                        </span>
                      </span>
                      <span v-else>{{ scope.row.paravalue }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('common.operate')"
                    v-if="activeName != 2"
                  >
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        :icon="scope.row.show ? '' : 'el-icon-edit'"
                        @click="jieduanEdit(scope.row, scope.$index, item.data)"
                        v-hasPermi="['system:param:edit']"
                        >{{
                          scope.row.show
                            ? $t("common.cancel")
                            : $t("common.update")
                        }}</el-button
                      >
                      <el-button
                        size="mini"
                        type="text"
                        @click="
                          handleDataSave(scope.row, scope.$index, item.data)
                        "
                        v-hasPermi="['system:param:edit']"
                        >{{
                          scope.row.show ? $t("common.save") : ""
                        }}</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-tab-pane>
        </template> -->
      <!-- </el-tabs> -->
      <!-- </el-card> -->
    </el-col>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%">
      <el-row>
        <span v-for="(item, index) in switchList" :key="index">
          <el-col :xs="3" :sm="3" :md="3" :lg="3" v-if="item.type === 'dp'">
            <el-col
              ><span class="demonstration">{{ item.name }}</span>
            </el-col>
            <el-col style="text-align: center">
              <el-image
                v-if="item.value == 1"
                :src="dpfanurlOn"
                style="width: 60%; height: 60%; align-content: center"
                class="gif"
                @click="handleImage(item, index, switchList)"
              ></el-image>
              <el-image
                v-else
                :src="dpfanurlOff"
                style="width: 60%; height: 60%; align-content: center"
                @click="handleImage(item, index, switchList)"
              ></el-image>
            </el-col>
          </el-col>

          <el-col
            :xs="3"
            :sm="3"
            :md="3"
            :lg="3"
            v-else-if="item.type === 'bp'"
          >
            <el-col
              ><span class="demonstration">{{ item.name }}</span></el-col
            >
            <el-col style="text-align: center">
              <el-image
                v-if="item.value == 1"
                :src="bpfanurlOn"
                style="width: 60%; height: 60%; align-content: center"
                @click="handleImage(item, index, switchList)"
              ></el-image>
              <el-image
                v-else
                :src="bpfanurlOff"
                style="width: 60%; height: 60%; align-content: center"
                @click="handleImage(item, index, switchList)"
              ></el-image>
            </el-col>
          </el-col>
        </span>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{
          $t("common.cancel")
        }}</el-button>
        <el-button type="primary" @click="handleSaveBinary(switchList)">{{
          $t("common.determine")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { checkPermi, checkRole } from "@/utils/permission"; // 权限判断函数
// import {isNil }from "lodash";
import { listSlave, treeselectEnv } from "@/api/huanKong/slave";
import { listHistory } from "@/api/huanKong/history";
import {
  environParaMqtt,
  listParam,
  getParam,
  delParam,
  addParam,
  updateParam,
  exportParam,
} from "@/api/huanKong/param";
import {
  listParam as listParamStage,
  getParam as getParamStage,
  delParam as delParamStage,
  addParam as addParamStage,
  updateParam as updateParamStage,
  exportParam as exportParamStage,
} from "@/api/huanKong/stage";
import Vue from "vue";
import { uuid } from "@/utils";
import { isUndefined, isEmpty, isObject, isNil } from "lodash";
require("@/utils/walden"); // echarts theme
// let _ = require('lodash')
let source;
var timerQ = null; //首先可以全局定义一个定时器
export default {
  name: "homeData",
  data() {
    return {
      queryForm: {},
      language: this.$store.state.app.type,
      dialogVisible: false,
      jieduanList: [],
      jieduanloadingSet: false,
      loadingSet: false,
      // 选中数组
      jieduanIds: [],
      // 非单个禁用
      jieduanSingle: true,
      // 非多个禁用
      jieduanMultiple: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 【控制器参数字典】表格数据
      paramList: [],
      // 遮罩层
      loading: false,
      jieduanloading: false,
      multipleSelection: [],
      jieduanMultipleSelection: [],

      activeName: "first",
      dataInterval: 10 * 1000,
      updateInterval: null,
      value: [],
      category13: null,
      xAxisList: [],
      yAxisList: [],
      historyList: null,

      //结果集
      slaveList: {},

      //定频风机开
      dpfanurlOn: require("@/assets/icons/environment_svg/fengji2.png"),
      //定频风机1关
      dpfanurlOff: require("@/assets/icons/environment_svg/fengji2_off.png"),

      //变频风机1图片url
      bpfanurlOn: require("@/assets/icons/environment_svg/fengsgif.gif"),
      //变频风机1图片url
      bpfanurlOff: require("@/assets/icons/environment_svg/fengsoffgif.gif"),

      options: null,

      //后台数据
      oldtable: [],
      oldParamList: [],
      noEditRow: {},
      // title: this.$t("huanKong.fanSwitchSet"),
      queryParams: {},
      nowRow: {},
      nowIndex: "",
      nowItem: [],
      binary: null,
      switchList: [
        {
          name: this.$t("huanKong.fixedFan1"),
          value: 0,
          type: "dp",
        },
        {
          name: this.$t("huanKong.fixedFan2"),
          value: 0,
          type: "dp",
        },
        {
          name: this.$t("huanKong.fixedFan3"),
          value: 0,
          type: "dp",
        },
        {
          name: this.$t("huanKong.fixedFan4"),
          value: 0,
          type: "dp",
        },
        {
          name: this.$t("huanKong.fixedFan5"),
          value: 0,
          type: "dp",
        },
        {
          name: this.$t("huanKong.fixedFan6"),
          value: 0,
          type: "dp",
        },
        {
          name: this.$t("huanKong.fixedFan7"),
          value: 0,
          type: "dp",
        },
        {
          name: this.$t("huanKong.fixedFan8"),
          value: 0,
          type: "dp",
        },
        {
          name: this.$t("huanKong.fan1"),
          value: 0,
          type: "bp",
        },
        {
          name: this.$t("huanKong.fan2"),
          value: 0,
          type: "bp",
        },
        {
          name: this.$t("huanKong.fan3"),
          value: 0,
          type: "bp",
        },
        {
          name: this.$t("huanKong.fan4"),
          value: 0,
          type: "bp",
        },
        {
          name: this.$t("huanKong.fan5"),
          value: 0,
          type: "bp",
        },
        {
          name: this.$t("huanKong.fan6"),
          value: 0,
          type: "bp",
        },
        {
          name: this.$t("huanKong.fan7"),
          value: 0,
          type: "bp",
        },
        {
          name: this.$t("huanKong.fan8"),
          value: 0,
          type: "bp",
        },
      ],
    };
  },
  computed: {
    title() {
      return this.$t("huanKong.fanSwitchSet");
    },
    //   switchList(){
    //     return[
    //       {
    //         name: this.$t("huanKong.fixedFan1"),
    //         value: 0,
    //         type: "dp",
    //       },
    //       {
    //         name: this.$t("huanKong.fixedFan2"),
    //         value: 0,
    //         type: "dp",
    //       },
    //       {
    //         name: this.$t("huanKong.fixedFan3"),
    //         value: 0,
    //         type: "dp",
    //       },
    //       {
    //         name: this.$t("huanKong.fixedFan4"),
    //         value: 0,
    //         type: "dp",
    //       },
    //       {
    //         name: this.$t("huanKong.fixedFan5"),
    //         value: 0,
    //         type: "dp",
    //       },
    //       {
    //         name: this.$t("huanKong.fixedFan6"),
    //         value: 0,
    //         type: "dp",
    //       },
    //       {
    //         name: this.$t("huanKong.fixedFan7"),
    //         value: 0,
    //         type: "dp",
    //       },
    //       {
    //         name: this.$t("huanKong.fixedFan8"),
    //         value: 0,
    //         type: "dp",
    //       },
    //       {
    //         name: this.$t("huanKong.fan1"),
    //         value: 0,
    //         type: "bp",
    //       },
    //       {
    //         name: this.$t("huanKong.fan2"),
    //         value: 0,
    //         type: "bp",
    //       },
    //       {
    //         name: this.$t("huanKong.fan3"),
    //         value: 0,
    //         type: "bp",
    //       },
    //       {
    //         name: this.$t("huanKong.fan4"),
    //         value: 0,
    //         type: "bp",
    //       },
    //       {
    //         name: this.$t("huanKong.fan5"),
    //         value: 0,
    //         type: "bp",
    //       },
    //       {
    //         name: this.$t("huanKong.fan6"),
    //         value: 0,
    //         type: "bp",
    //       },
    //       {
    //         name: this.$t("huanKong.fan7"),
    //         value: 0,
    //         type: "bp",
    //       },
    //       {
    //         name: this.$t("huanKong.fan8"),
    //         value: 0,
    //         type: "bp",
    //       },
    //     ]
    //   }
  },
  created() {
    // this.paramList = [
    //   { title: "标题1", text: "s111sssa" },
    //   { title: "标题2", text: "ss222ssa" },
    // ];
    this.clearTimerQ();
    this.paramList.map((i) => {
      i.show = false;
      return i;
    });

    // this.getList();
    // this.getHistoryList();
    // this.getTree();

    //实时刷新
    if (this.queryParams) {
      timerQ = setInterval(() => {
        this.getList();
        this.getHistoryList(); //echarts图
      }, this.dataInterval);
    }
  },
  mounted() {
    // this.getTree();
    // if (this.queryParams) {
    //   // this.updateInterval = setInterval(() => {
    //   this.getList();
    //   // }, this.dataInterval);
    // }
    // this.getValueList();
    // this.getValueListStage();
    // if (this.queryParams) {
    //   // this.updateInterval = setInterval(() => {
    //   this.handleEventSource();
    //   // }, this.dataInterval);
    // }
    // this.$nextTick(() => {
    //   this.handleEventSource();
    // });
    // const self=this;
    var _this = this;
    this
      .getTree
      //   () => {
      //   _this.$nextTick((queryParams) => {
      //     //mounted的同步语句放在回调函数内，渲染语句套上$nextTick
      //     if (queryParams) {
      //       _this.updateInterval = setInterval(() => {
      //         _this.getList();
      //       }, _this.dataInterval);
      //     }
      //     // _this.getList();
      //     _this.getValueList();
      //     _this.getValueListStage();
      //     _this.submitFormSet();
      //   });
      // }
      ();

    // this.getTree();
    // if (this.queryParams) {
    //   // this.updateInterval = setInterval(() => {
    //   this.getList();
    //   // }, this.dataInterval);
    // }
    // this.getValueList();
    // this.getValueListStage();
    // this.handleEventSource();

    // this.getHistoryList();

    // window.onresize = () => {
    //   //alert("sss");
    //   this.category13.resize(); //重新初始化echarts
    // };
    // this.updateChartView();
    window.addEventListener("resize", this.handleWindowResize);
    // this.addChartResizeListener();
  },
  watch: {
    // "$i18n.locale"(newValue) {
    //   // 不需要重新设置配置项，只需要手动触发一下setOption()
    //   this.myChart.setOption(this.option);
    // },
    "$store.state.settings.nowPigFarm": {
      handler: function () {
        this.getTree();
      },
    },
    "$store.state.app.type": {
      handler: function () {
        this.language = this.$store.state.app.type;
        this.$forceUpdate();
      },
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleWindowResize);
    clearInterval(timerQ);
    // this.clearTimerQ();
    if (
      // !(isUndefined(source) || isEmpty(source))
      !(typeof source == "undefined" || source == undefined || source == null)
    ) {
      source.close();
      source = null;
    }
  },
  // destoryed() {
  //   this.clearTimerQ();
  // },
  methods: {
    checkPermi,
    checkRole,
    handleLink() {
      this.$router.replace({
        path: "/huanKong/env_control",
        query: {
          value: this.value,
          id: this.queryParams.id,
          homeid: this.queryParams.homeid,
        },
      });
    },
    handleOnline(uptime) {
      var dateBegin = new Date(uptime.replace(/-/g, "/")); //replace方法将-转为/苹果兼容性
      var dateEnd = new Date(); //当前时间数据

      var startTime = dateBegin.getTime();
      var stopTime = dateEnd.getTime();
      var cTime = Number(stopTime) - Number(startTime);
      var secondTime = cTime / 1000 / 60;
      if (parseInt(secondTime) > 30) {
        return false;
      } else {
        return true;
      }
    },
    clearTimerQ() {
      try {
        window.clearInterval(timerQ);
      } catch (error) {}
      window.timerQ = null;
    },
    handleSaveBinary(switchList) {
      let nowBinary = [];
      switchList.map((item, index) => {
        nowBinary.push(item.value);
      });
      let nowParavalue = nowBinary.reverse().join("").replace(/^0+/, "");
      this.nowRow.paravalue = isEmpty(nowParavalue) ? "0" : nowParavalue;
      Vue.set(this.nowItem, this.nowIndex, this.nowRow);
      this.dialogVisible = false;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    handleImage(item, index, switchList) {
      switchList.map((i, ind) => {
        if (ind === index) {
          i.value = item.value == 1 ? 0 : 1;
        }
      });
    },
    buling(a, length) {
      return a.padStart(length, 0);
    },

    bulingAfter(a, length) {
      return a.padEnd(length, 0);
    },
    handleInput(value) {
      // 将输入值转换为数字类型
      const parsedValue = parseFloat(value);
      // 判断是否为有效数字
      if (!isNaN(parsedValue)) {
        // 将输入值修正为有效数字
        this.value = parsedValue;
      } else {
        // 如果输入不是有效数字，则将值设为空
        this.value = null;
      }
    },

    handleFocus(value, nowRow, nowIndex, nowItem) {
      this.nowRow = nowRow;
      this.nowIndex = nowIndex;
      this.nowItem = nowItem;
      //针对不同的开关 switchList不一样
      // this.binary = this.buling(
      //   paravalue.split("").reverse().join(""),
      //   16
      // ).split("");
      let paravalue = value || "0"; //paravalue为null时有bug
      // console.log("paravalue",paravalue)
      this.binary = this.buling(paravalue, 16).split("").reverse();
      if (nowRow.type == 2) {
        // this.title="风机开关设置";
        this.switchList = [
          {
            name: this.$t("huanKong.fixedFan1"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan2"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan3"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan4"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan5"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan6"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan7"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan8"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fan1"),
            value: 0,
            type: "bp",
          },
          {
            name: this.$t("huanKong.fan2"),
            value: 0,
            type: "bp",
          },
          {
            name: this.$t("huanKong.fan3"),
            value: 0,
            type: "bp",
          },
          {
            name: this.$t("huanKong.fan4"),
            value: 0,
            type: "bp",
          },
          {
            name: this.$t("huanKong.fan5"),
            value: 0,
            type: "bp",
          },
          {
            name: this.$t("huanKong.fan6"),
            value: 0,
            type: "bp",
          },
          {
            name: this.$t("huanKong.fan7"),
            value: 0,
            type: "bp",
          },
          {
            name: this.$t("huanKong.fan8"),
            value: 0,
            type: "bp",
          },
        ];
      } else if (nowRow.type == 4) {
        // this.title="定时风机开关设置";
        this.switchList = [
          {
            name: this.$t("huanKong.fixedFan9"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan10"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan11"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan12"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan13"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan14"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan15"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan16"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan17"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan18"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan19"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan20"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan21"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan22"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan23"),
            value: 0,
            type: "dp",
          },
          {
            name: this.$t("huanKong.fixedFan24"),
            value: 0,
            type: "dp",
          },
        ];
      }
      this.switchList.map((item, index) => {
        item.value = this.binary[index];
      });
      this.dialogVisible = true;
    },
    //参数下发单个保存
    handleDataSave(scope, index, item) {
      let value = scope.paravalue;
      switch (scope.type) {
        case 1:
          value = value * 10;
          break; //停止执行，跳出switch
        case 2:
        case 4:
          value = parseInt(value, 2);
          break; //停止执行，跳出switch
        default:
        //上述条件都不满足时，默认执行的代码
      }
      // /envmqtt/envParamDown
      environParaMqtt({
        // jieduan: scope.jieduan || -1,
        jieduan: -1,
        id: this.queryParams.id,
        mfactory: this.$store.state.settings.nowPigFarm,
        point: scope.sitecode,
        value,
        // raw: [
        //   {
        //     [scope.sitecode]: value,
        //   },
        // ],
      }).then((response) => {
        this.msgSuccess(this.$t("common.modifiedSuccess"));
        scope.show = scope.show ? false : true;
        this.submitFormSet();
        //Vue.set(item ? item : this.paramList, index, scope);
      });
    },
    submitFormSet() {
      this.loadingSet = true;
      if (
        // !(isUndefined(source) || isEmpty(source))
        !(typeof source == "undefined" || source == undefined || source == null)
      ) {
        source.close();
        source = null;
        // delete source;
      }
      this.handleEventSource(this.handleGrop(this.activeName));
    },
    handleEventSource(group) {
      //创建SSE对象 查询
      let dataArr = [];
      let timer = null;
      if (window.EventSource) {
        // 创建 EventSource 对象连接服务器
        source = new EventSource(
          process.env.VUE_APP_BASE_API +
            `/system/envsse/subscribe?uuid=${uuid(12, 10)}&id=${
              this.queryParams.id
            }&mfactory=${this.$store.state.settings.nowPigFarm}&homeid=${
              this.queryParams.homeid
            }&group=${group}`
        );
        // source = new EventSource(
        //   `http://172.16.31.94:8089/system/envsse/subscribe?uuid=${uuid(
        //     12,
        //     10
        //   )}&id=${this.queryParams.id}&mfactory=${
        //     this.$store.state.settings.nowPigFarm
        //   }&homeid=${this.queryParams.homeid}`
        // );

        // 连接成功后会触发 open 事件
        source.addEventListener("open", (e) => {}, false);

        // 服务器发送信息到客户端时，如果没有 event 字段，默认会触发 message 事件
        source.addEventListener(
          "message",
          (e) => {
            dataArr.push(e.data);
            console.log("handleEventSource");
            // this.loadingSet = true;
            if (e.data === "查询超时") {
              // this.loadingSet = true;
              // const that = this;
              // timer = setTimeout(function () {
              //   if (e.data === "查询超时") {
              //     that.msgError("Request timed out! ! !");
              //     source.close();
              //     source = null;
              //     that.loadingSet = false;
              //   } else {
              //     that.loadingSet = false;
              //   }
              // }, 30000);
              this.msgError("Request timed out! ! !");
              source.close();
              source = null;
              this.loadingSet = false;
            } else if (e.data === "查询异常") {
              this.msgError("Query exception! ! !");
              source.close();
              source = null;
              this.loadingSet = false;
            } else if (
              JSON.parse(e.data) &&
              isObject(JSON.parse(e.data)) &&
              JSON.stringify(JSON.parse(e.data)) !== "{}"
            ) {
              if (JSON.parse(e.data).slavenetwork == "ok") {
                clearTimeout(timer);
                let aa = JSON.parse(e.data).raw[0];
                for (var index in aa) {
                  this.paramList.map((item, ind) => {
                    if (Number(item.sitecode) == index) {
                      item.show = false;
                      switch (item.type) {
                        //数值型*10
                        case 1:
                          item.paravalue = aa[index] / 10;
                          break; //停止执行，跳出switch
                        //二进制 开关量
                        case 2:
                        case 4:
                          item.paravalue = Number(aa[index]).toString(2);
                          break; //停止执行，跳出switch
                        case 3:
                          item.paravalue = aa[index];
                          break; //停止执行，跳出switch
                        default:
                          item.paravalue = aa[index];
                        //上述条件都不满足时，默认执行的代码
                      }
                      Vue.set(this.paramList, ind, item);
                      // item.paravalue =
                      //   item.type === 1 ? aa[index] / 10 : aa[index];
                    }
                  });
                  this.jieduanList.map((data) => {
                    data.data.map((item, ind) => {
                      // forEach适合于你并不打算改变数据的时候
                      //forEach()会改变原始的数组的值
                      if (Number(item.sitecode) == index) {
                        item.show = false;
                        // item.paravalue =
                        //   item.type === 1 ? aa[index] / 10 : aa[index];
                        switch (item.type) {
                          case 1:
                            item.paravalue = aa[index] / 10;
                            break; //停止执行，跳出switch
                          case 2:
                          case 4:
                            item.paravalue = Number(aa[index]).toString(2);
                            break; //停止执行，跳出switch
                          case 3:
                            item.paravalue = aa[index];
                            break; //停止执行，跳出switch
                          default:
                            item.paravalue = aa[index];
                          //上述条件都不满足时，默认执行的代码
                        }
                        Vue.set(data.data, ind, item);
                      }
                    });
                  });
                }
              } else {
                this.msgError(this.$t("huanKong.connectErr"));
              }

              this.loadingSet = false;
              // source.close();
              // source = null;
            }
          },
          false
        );

        // 自定义 EventHandler，在收到 event 字段为 slide 的消息时触发
        source.addEventListener("slide", (e) => {}, false);

        // 连接异常时会触发 error 事件并自动重连
        source.addEventListener(
          "error",
          (e) => {
            if (e.target.readyState === EventSource.CLOSED) {
              console.log("Disconnected");
            } else if (e.target.readyState === EventSource.CONNECTING) {
              source.close();
              console.log("Connecting...");
            }
          },
          false
        );
        // }
      } else {
        console.error("Your browser doesn't support SSE");
      }
    },

    sortIdAsc(a, b) {
      return a.id - b.id;
    },

    /** 查询【控制器参数字典】列表 */
    getValueList() {
      this.loading = true;
      const { id, ...otherQueryParams } = this.queryParams;
      listParam(otherQueryParams).then((response) => {
        this.paramList = response.rows;
        this.paramList.map((i) => {
          // map()适用于你要改变数据值的时候。不仅仅在于它更快，而且返回一个新的数组。这样的优点在于你可以使用复合(composition)(map(), filter(), reduce()等组合使用)来玩出更多的花样
          i.show = false;
          return i;
        });
        this.total = response.total;
        this.loading = false;
      });
      this.loading = false;
    },
    getValueListStage() {
      this.jieduanloading = true;
      const { id, ...otherQueryParams } = this.queryParams;
      listParamStage(otherQueryParams).then((response) => {
        // response.rows.map(item=>{
        //   if(item.indexOf(item.jieduan))
        // })
        var a = []; //合成之后的对象数组
        //遍历数据
        for (let i = 0; i < response.rows.length; i++) {
          //赋值给obj给下面for进行比对
          var obj = {
            id: response.rows[i].jieduan,
            data: [response.rows[i]],
          };
          //遍历找出相同ID值对象
          for (let j = i + 1; j < response.rows.length; j++) {
            if (obj.id == response.rows[j].jieduan) {
              //追加到obj.data
              obj.data.push(response.rows.splice(j, 1)[0]);
              j--;
            }
          }
          a.push(obj);
        }

        a.sort(this.sortIdAsc);
        this.jieduanList = a;
        this.jieduanList.map((i) => {
          i.data.map((j) => {
            j.show = false;
            return j;
          });
          return i;
        });
        this.total = response.total;
        this.jieduanloading = false;
      });
      this.loading = false;
    },
    edit(row, index) {
      // row.show = true;
      // row.show = row.show ? false : true;
      // Vue.set(this.paramList, index, row);
      if (row.show == true) {
        let olds = Object.assign(this.oldParamList[index], { show: false });
        this.$set(this.paramList, index, olds);
      } else if (row.show == false) {
        // row.show = true;
        // this.oldParamList[index] = JSON.parse(JSON.stringify(row)); // 复制旧的数据，取消时用到
        // this.$set(this.paramList, index, row);
        this.$set(this.paramList, index, { ...row, show: true });
        this.oldParamList[index] = { ...row }; // 使用对象解构来复制对象
      }
      // 修改后保存
    },
    jieduanEdit(row, index, item) {
      if (row.show) {
        // 当 row.show 为真时
        let olds = { ...this.oldtable[index], show: false }; // 使用对象解构来创建新对象
        this.$set(item, index, olds);
      } else {
        // 当 row.show 为假时
        // row.show = true;
        this.$set(item, index, { ...row, show: true });
        this.oldtable[index] = { ...row }; // 使用对象解构来复制对象
      }

      // 修改后保存
    },

    // jieduanEdit(row, index, item) {
    //   //Vue.set 可以设置实例创建之后添加的新的属性，（在data里未声明的属性），而。this.$set只能设置实例创建后存在的属性。
    //   if (row.show) {
    //     // 第三种方式使用的Vue的特性，对根节点下的数据变更的自动检测。
    //     //Object.assign进行的拷贝是浅拷贝
    //     let olds = Object.assign(this.oldtable[index], { show: false });
    //     Vue.set(item, index, olds);
    //   } else {
    //     row.show = true;
    //     this.oldtable[index] = JSON.parse(JSON.stringify(row)); // 复制旧的数据，取消时用到
    //     Vue.set(item, index, row);
    //   }

    //   // Vue.set(item, index, row);
    //   // 修改后保存
    // },
    editAll() {
      this.paramList.map((i, index) => {
        i.show = true;
        this.oldParamList[index] = this.oldParamList[index]
          ? this.oldParamList[index]
          : JSON.parse(JSON.stringify(i)); // 复制旧的数据，取消时用到
        Vue.set(this.paramList, index, i);
      });
    },
    saveAll() {
      let rawArr = {};
      let num = 0;
      this.paramList.map((i, index) => {
        if (i.show === true) {
          num++;
        }
        let paravalue = i.paravalue;
        switch (i.type) {
          case 1:
            paravalue = paravalue * 10;
            break; //停止执行，跳出switch
          case 2:
          case 4:
            paravalue = parseInt(paravalue, 2);
            break; //停止执行，跳出switch
          default:
          //上述条件都不满足时，默认执行的代码
        }
        Object.assign(rawArr, {
          [i.sitecode]: paravalue,
        });
        // rawArr.push({
        //   [i.sitecode]: paravalue,
        // });
      });
      if (num > 0) {
        environParaMqtt({
          jieduan: -1,
          id: this.queryParams.id,
          mfactory: this.$store.state.settings.nowPigFarm,
          raw: [rawArr],
        }).then((response) => {
          this.msgSuccess(this.$t("common.modifiedSuccess"));
        });

        this.paramList.map((i, index) => {
          i.show = false;
          Vue.set(this.paramList, index, i);
        });
      }
    },
    jieduanEditAll(item) {
      item.map((i, index) => {
        i.show = true;
        this.oldtable[index] = this.oldtable[index]
          ? this.oldtable[index]
          : JSON.parse(JSON.stringify(i)); // 复制旧的数据，取消时用到
        Vue.set(item, index, i);
      });
    },
    jieduanSaveAll(item) {
      // let rawArr = [];
      let rawArr = {};
      let num = 0;
      item.data.map((i, index) => {
        if (i.show === true) {
          num++;
        }
        let paravalue = i.paravalue;
        switch (i.type) {
          case 1:
            paravalue = paravalue * 10;
            break; //停止执行，跳出switch
          case 2:
          case 4:
            paravalue = parseInt(paravalue, 2);
            break; //停止执行，跳出switch
          default:
          //上述条件都不满足时，默认执行的代码
        }
        Object.assign(rawArr, {
          [i.sitecode]: paravalue,
        });
        // rawArr.push({
        //   [i.sitecode]: paravalue,
        // });
      });
      if (num > 0) {
        environParaMqtt({
          jieduan: item.id,
          id: this.queryParams.id,
          mfactory: this.$store.state.settings.nowPigFarm,
          // raw: rawArr,
          raw: [rawArr],
        }).then((response) => {
          this.msgSuccess(this.$t("common.modifiedSuccess"));
        });

        item.data.map((i, index) => {
          i.show = false;
          Vue.set(item.data, index, i);
        });
      }
    },
    // 单个复制
    cope(val, index) {
      this.paramList.splice(index, 0, JSON.parse(JSON.stringify(val)));
    },
    // 单个删除
    delect(index) {
      this.paramList.splice(index, 1);
    },
    //批量新增
    addAll() {
      if (this.multipleSelection.length == 0) {
        let list = {
          title: "",
          text: "",
        };
        this.paramList.push(list);
      } else {
        this.multipleSelection.forEach((val, index) => {
          this.paramList.splice(index, 0, JSON.parse(JSON.stringify(val)));
        });
      }
    },
    //批量删除
    delectAll() {
      for (let i = 0; i < this.paramList.length; i++) {
        const element = this.paramList[i];
        element.id = i;
      }
      if (this.multipleSelection.length == 0)
        this.$message.error(this.$t("huanKong.atLeastOne"));
      this.multipleSelection.forEach((element) => {
        this.paramList.forEach((e, i) => {
          if (element.id == e.id) {
            this.paramList.splice(i, 1);
          }
        });
      });
    },
    // //选
    // handleSelectionChange(val) {
    //   this.multipleSelection = val;
    // },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
      this.multipleSelection = selection;
    },
    handleJieduanSelectionChange(selection) {
      this.jieduanIds = selection.map((item) => item.id);
      this.jieduanSingle = selection.length !== 1;
      this.jieduanMultiple = !selection.length;
      this.jieduanMultipleSelection = selection;
    },

    /**
     * 对chart元素尺寸进行监听，当发生变化时同步更新echart视图
     */
    // addChartResizeListener() {
    //   const instance = ResizeListener({
    //     strategy: "scroll",
    //     callOnAdd: true,
    //   });

    //   instance.listenTo(this.$refs.category13, () => {
    //     if (!this.category13) return;
    //     this.category13.resize();
    //   });
    // },

    /**
     * 更新echart视图
     */
    // updateChartView() {
    //   if (!this.category13) return;

    //   const fullOption = this.assembleDataToOption();
    //   this.category13.setOption(fullOption, true);
    // },
    handleGrop(activeName) {
      let group;
      switch (activeName) {
        case "second":
          group = "-2";
          break; //停止执行，跳出switch
        case "2" || 2:
          group = "-1";
          break; //停止执行，跳出switch
        case "3" || 3:
          group = "0";
          break; //停止执行，跳出switch
        case "4" || 4:
          group = "1";
          break; //停止执行，跳出switch
        case "5" || 5:
          group = "2";
          break; //停止执行，跳出switch
        case "6" || 6:
          group = "3";
          break; //停止执行，跳出switch
        case "7" || 7:
          group = "4";
          break; //停止执行，跳出switch
        case "8" || 8:
          group = "5";
          break; //停止执行，跳出switch
        case "9" || 9:
          group = "6";
          break; //停止执行，跳出switch
        case "10" || 10:
          group = "7";
          break; //停止执行，跳出switch
        case "11" || 11:
          group = "8";
          break; //停止执行，跳出switch
        case "12" || 12:
          group = "9";
          break; //停止执行，跳出switch
        case "13" || 13:
          group = "10";
          break; //停止执行，跳出switch
        case "14" || 14:
          group = "11";
          break; //停止执行，跳出switch
        case "15" || 15:
          group = "12";
          break; //停止执行，跳出switch

        default:
        //上述条件都不满足时，默认执行的代码
      }
      return group;
    },
    handleClick(tab, event) {
      // 单个   second  -2
      // 温度   2       -1
      // 最小   3        0
      // 一     4        1
      // 二     5        2
      // 。。。。。。
      // 十二   15      12
      if (tab.name == "first") {
      } else {
        this.loadingSet = true;
        if (
          // !(isUndefined(source) || isEmpty(source))
          !(
            typeof source == "undefined" ||
            source == undefined ||
            source == null
          )
        ) {
          source.close();
          source = null;
          // delete source;
        }
        this.handleEventSource(this.handleGrop(this.activeName));
      }
    },

    /**
     * 当窗口缩放时，echart动态调整自身大小
     */
    handleWindowResize() {
      if (!this.category13) return;
      this.category13.resize();
    },
    /** 查询【请填写功能名称】列表 */
    getTree() {
      treeselectEnv({ facid: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.options = response.data;
          this.value = [
            this.options[0].value,
            this.options[0].children[0].value,
          ];
          this.queryParams.id = this.options[0].children[0].value;
          this.queryParams.homeid = this.options[0].value;
          this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
          // if (this.queryParams) {
          //   this.timerQ = setInterval(() => {
          //     this.getList();
          //   }, this.dataInterval);
          // }
          // _this.getList();
          this.getList(); //界面基本信息
          this.getHistoryList(); //echarts图
          // this.getValueList();
          // this.getValueListStage();
          // this.submitFormSet();
        }
      );
    },
    // 节点单击事件
    handleNodeClick(data) {
      if (this.queryParams) {
        this.queryParams.id = data[1];
        this.queryParams.homeid = data[0];
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        // source.close();
        // source = null;
        this.getList(); //界面基本信息
        this.getHistoryList(); //echarts图
        // this.getValueList(); //单个参数下发
        // this.getValueListStage(); //阶段参数下发
        // this.submitFormSet(); //mqtt查询
      }
    },

    getList() {
      if (this.queryParams) {
        listSlave(this.queryParams).then((response) => {
          if (
            response &&
            response.rows[0] &&
            response.rows[0].slavenetwork === "ok"
          ) {
            this.slaveList = response.rows[0];
            // this.total = response.total;
            // this.queryParams.slaveid = this.slaveList.slaveid;
            // this.queryParams.homeid = this.slaveList.homeid;
          } else {
            this.slaveList = { slavenetwork: "bad" };
          }
        });
      }
    },
    getHistoryList() {
      // this.category13 = echarts.init(this.$refs.category13, "wonderland");
      // if (!this.category13) return;
      // pageNum:1   pageSize  100
      // this.queryParams.pageNum = 1;
      // this.queryParams.pageSize = 100;
      // this.queryParams.slaveid = this.queryParams.id;
      listHistory({
        pageNum: 1,
        // pageSize: 100,
        dayLine: 7,
        slaveid: this.queryParams.id,
        homeid: this.queryParams.homeid,
        facid: this.queryParams.mfactory,
      }).then((response) => {
        if (response && response.rows) {
          this.historyList = response.rows;
          this.xAxisList = [];
          this.yAxisList = [];
          this.historyList.forEach((element) => {
            this.xAxisList.push(element.uptime);
            this.yAxisList.push(element.temperature);
          });
        } else {
          this.historyList = [];
          this.xAxisList = [];
          this.yAxisList = [];
        }
        this.getDatas();
      });
      // this.getDatas();
    },
    getDatas() {
      // this.category13 =
      //   //  echarts.init(this.$refs.category13, "wonderland");
      //   this.$refs.category13
      //     ? echarts.init(this.$refs.category13, "wonderland")
      //     : "";
      //alert(this.document.getElementById('#category1'));
      //有的话就获取已有echarts实例的DOM节点。
      let myChart = echarts.getInstanceByDom(this.$refs.category13);
      if (myChart == null) {
        // 如果不存在，就进行初始化
        myChart = echarts.init(this.$refs.category13, "walden");
      }

      myChart.setOption({
        // title: {
        //   text: "",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: this.$t("huanKong.temperature"),
        },
        grid: {
          left: "5%",
          right: "5%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },

        xAxis: {
          type: "category",
          nameLocation: "end", //坐标轴名称显示位置。
          // axisLabel: {
          //   //坐标轴刻度标签的相关设置。
          //   interval: 8,
          //   rotate: "70",
          // },
          // axisLine: {
          //   symbol: 'none',
          // },
          // boundaryGap: false,
          data: this.xAxisList.reverse(),
        },
        yAxis: {
          type: "value",
          min: "0",
          max: "40",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },

            smooth: true,
            name: this.$t("huanKong.temperature"),
            type: "line",
            stack: "总量",
            data: this.yAxisList.reverse(),
            color: "#0bd2cb",
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,

                colorStops: [
                  {
                    offset: 0,
                    // color: "#507E32",
                    color: "#0bd2cb", // 100% 处的颜色acf598
                  },
                  {
                    offset: 1,
                    // color: "#C9DBC1",
                    color: "#96e6e8", //   0% 处的颜色0a861e
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
      });
    },
  },
};
</script>

<style scoped lang="scss">
.warning {
  padding: 4px 16px;
  background-color: #fff6f7;
  border-radius: 4px;
  /* border-left: 5px solid #fe6c6f; */
  /* margin: 20px 0; */
}
.gif {
  /* -webkit-transform: rotate (360deg); */
  animation: rotation 1s linear infinite;
  -moz-animation: rotation 1s linear infinite;
  -webkit-animation: rotation 1s linear infinite;
  -o-animation: rotation 1s linear infinite;
}
@keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

@-webkit-keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}
.tagLine {
  border-radius: 100px;
  width: 100%;
  text-align: center;
  height: 40px;
  line-height: 40px;
  background-color: #79bbff;
  border: none;
  font-size: 14px;
}
.rowStyle {
  line-height: 70px;
  font-size: 30px;
  font-family: Helvetica;
  margin: auto;
  text-align: center;
  color: #82848a;
}
.spanS {
  line-height: 70px;
  font-size: 45px;
  font-family: Helvetica;
  margin: auto;
  text-align: center;
  color: #26d8e0e0;
}

.spanTem {
  font-family: Helvetica;
  height: 50%;
  align-items: center;
  display: flex;
}

.spanText {
  line-height: 40px;
  font-size: 30px;
  font-family: Helvetica;
  margin: auto;
  text-align: center;
  color: #26d8e0e0;
}

.spanDu {
  color: #606266;
  font-size: 14px;
}

.spanAlarm {
  line-height: 25px;
  /* font-size: 25px; */
  font-family: Helvetica;
  margin: auto;
  text-align: center;
  color: red;
}
.lineS {
  margin: 5px 0px;
  height: 0.3px;
}
.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 5em;
  margin: 0 8px;
  vertical-align: middle;
  position: relative;
}
.font_comom {
  /* font-family: Helvetica;
  color: #8492a6; */
}
.bkpng {
  background-image: url("../../../assets/icons/environment_svg/lianzi.png");
}

.demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin-bottom: 20px;
  text-align: center;
}
.demonstration.card {
  height: 20px;
}

.el-divider--vertical {
  height: 100%;
}

/* 未访问时的样式 */
a {
  color: #1890ff !important; /* 文字颜色 */
  text-decoration: underline; /* 下划线 */
}

/* 鼠标悬停时的样式 */
a:hover {
  /* color: red; //文字颜色 */
  cursor: pointer;
  text-decoration: none; /* 移除下划线 */
}

.system_svg {
  width: 1.5em;
  height: 1.5em;
  fill: #606266;
  vertical-align: -0.4em;
}

.system_svg:hover {
  /* color: red; //文字颜色 */
  cursor: pointer;
  fill: #101010;
}

.font_comom {
  font-weight: 600;
  font-size: 14px;
  color: #606266;
}

// data-picture >>> svg path:first-child {
//   stroke: #14da25;
// }

// 当前状态颜色
// .progressBar >>> .el-progress-bar__outer {
//   background-color: rgba(255, 255, 255, 0.2);
// }
// 进度条的背景色
// .progressBar >>> .el-progress-bar__inner {
//   background-color: #fff;
// }

// /deep/ .el-progress path:first-child {
//   // 修改进度条背景色
//   stroke: #14da25;
// }

// /deep/ .el-progress__text {
//   // 修改进度条文字提示颜色
//   color: #e6451c;
// }
</style>