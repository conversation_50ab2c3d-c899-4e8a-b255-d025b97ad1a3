import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listPeizhi(query) {
  return request({
    url: '/system/peizhi/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getPeizhi(id) {
  return request({
    url: '/system/peizhi/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addPeizhi(data) {
  return request({
    url: '/system/peizhi',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updatePeizhi(data) {
  return request({
    url: '/system/peizhi',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delPeizhi(id) {
  return request({
    url: '/system/peizhi/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportPeizhi(query) {
  return request({
    url: '/system/peizhi/export',
    method: 'get',
    params: query
  })
}