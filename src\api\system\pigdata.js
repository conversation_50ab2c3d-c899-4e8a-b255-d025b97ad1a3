import request from '@/utils/request'
//出栏 /system/pigdata/chulan

export function outLan(data) {
  return request({
    url: '/system/pigdata/chulan',
    method: 'put',
    data: data
  })
}

// 查询【请填写功能名称】列表
export function listPigdata(query) {
  return request({
    url: '/system/pigdata/list',
    method: 'get',
    params: query
  })
}

//猪场总览查询
export function listPigdataOverview(query) {
  return request({
    url: '/system/pigdata/overview',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getPigdata(mID) {
  return request({
    url: '/system/pigdata/' + mID,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addPigdata(data) {
  return request({
    url: '/system/pigdata',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updatePigdata(data) {
  return request({
    url: '/system/pigdata',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delPigdata(indexn) {
  return request({
    url: '/system/pigdata/' + indexn,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportPigdata(query) {
  return request({
    url: '/system/pigdata/export',
    method: 'get',
    params: query
  })
}

// 导出【请填写功能名称】
export function getdaterangebyrl(query) {
  return request({
    url: '/system/pigdata/getdaterangebyrl',
    method: 'get',
    params: query
  })
}

///system/pigdata/changemrfid     更换耳牌
//参数：indexn，mid，newMrfid

export function changemrfid(data) {
  return request({
    url: '/system/pigdata/changemrfid',
    method: 'put',
    data: data
  })
}
