import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listFeeddata(query) {
  return request({
    url: '/system/feeddata/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getFeeddata(id) {
  return request({
    url: '/system/feeddata/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addFeeddata(data) {
  return request({
    url: '/system/feeddata',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateFeeddata(data) {
  return request({
    url: '/system/feeddata',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delFeeddata(id) {
  return request({
    url: '/system/feeddata/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportFeeddata(query) {
  return request({
    url: '/system/feeddata/export',
    method: 'get',
    params: query
  })
}
