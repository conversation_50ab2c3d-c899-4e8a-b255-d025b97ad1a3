import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listSlave(query) {
  return request({
    url: '/system/liaotaslave/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getSlave(id) {
  return request({
    url: '/system/liaotaslave/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addSlave(data) {
  return request({
    url: '/system/liaotaslave',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateSlave(data) {
  return request({
    url: '/system/liaotaslave',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delSlave(id) {
  return request({
    url: '/system/liaotaslave/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportSlave(query) {
  return request({
    url: '/system/liaotaslave/export',
    method: 'get',
    params: query
  })
}

