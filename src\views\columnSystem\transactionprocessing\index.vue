<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('columnSystem.launchDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.ninkjet')" prop="ninkjet">
        <el-select
          v-model="queryParams.ninkjet"
          :placeholder="$t('common.pleaseChoose')"
        >
          <el-option
            v-for="dict in inkjetStatusList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.ndepart')" prop="ndepart">
        <el-select
          v-model="queryParams.ndepart"
          :placeholder="$t('common.pleaseChoose')"
        >
          <el-option
            v-for="dict in departStatusList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['columnSystem:transactionprocessing:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['columnSystem:transactionprocessing:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['columnSystem:transactionprocessing:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['columnSystem:transactionprocessing:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="inkjetList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('common.serialNumber')"
        align="center"
        prop="indexn"
      />

      <el-table-column
        :label="$t('columnSystem.mrfid')"
        align="center"
        prop="mrfid"
        width="180"
      />
      <el-table-column
        :label="$t('columnSystem.mid')"
        align="center"
        prop="mid"
        width="180"
      />
      <el-table-column
        :label="$t('columnSystem.mdorm')"
        align="center"
        prop="mdorm"
      />
      <el-table-column
        :label="$t('columnSystem.nindex')"
        align="center"
        prop="nindex"
      />

      <el-table-column
        :label="$t('columnSystem.launchDate')"
        align="center"
        prop="nregdate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.nregdate, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('columnSystem.ninkjet')"
        align="center"
        prop="ninkjet"
        :formatter="ninkjetFormat"
      />

      <el-table-column
        :label="$t('columnSystem.ndepart')"
        align="center"
        prop="ndepart"
        :formatter="ndepartFormat"
      />

      <el-table-column
        :label="$t('columnSystem.mreasoninkjet')"
        align="center"
        prop="mreasoninkjet"
        :formatter="reasonFormat"
      />

      <el-table-column
        :label="$t('columnSystem.nexecdate')"
        align="center"
        prop="nexecdate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.nexecdate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('columnSystem.ntype')"
        align="center"
        prop="ntype"
        :formatter="ntypeFormat"
      />

      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['columnSystem:transactionprocessing:edit']"
            >{{ $t("common.update") }}</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['columnSystem:transactionprocessing:remove']"
            >{{ $t("common.delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【事务处理】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
          <el-input
            v-model="form.mrfid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
          <el-input
            v-model="form.mdorm"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
          <el-input
            v-model="form.nindex"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.launchDate')" prop="nregdate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.nregdate"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.ninkjet')" prop="ninkjet">
          <el-select
            v-model="form.ninkjet"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in inkjetStatusList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.ndepart')" prop="ndepart">
          <el-select
            v-model="form.ndepart"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in departStatusList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('columnSystem.mreasoninkjet')"
          prop="mreasoninkjet"
        >
          <el-select
            v-model="form.mreasoninkjet"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in transactionReasonsList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('columnSystem.nexecdate')" prop="nexecdate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.nexecdate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item :label="$t('columnSystem.ntype')" prop="ntype">
          <el-select
            v-model="form.ntype"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in transactionStatusList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProcess,
  getProcess,
  delProcess,
  addProcess,
  updateProcess,
  exportProcess,
} from "@/api/columnSystem/transactionprocessing";

export default {
  name: "TransactionProcessing",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【事务处理】表格数据
      inkjetList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        uuid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nregdate: null,
        nflag: null,
        ninkjet: null,
        ndepart: null,
        mreasoninkjet: null,
        mreasondepart: null,
        nexecdate: null,
        ntype: null,
        mfactory: null,
        naddressup: null,
        ncol1: null,
        ncol2: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      inkjetStatusList: [],
      departStatusList: [],
      transactionReasonsList: [],
      transactionStatusList: [],
    };
  },
  created() {
    this.getList();
    //是否喷墨
    this.getDicts("fl_inkjet_type").then((response) => {
      this.inkjetStatusList = response.data;
    });
    //是否分离
    this.getDicts("fl_depart_type").then((response) => {
      this.departStatusList = response.data;
    });
    //处理原因
    this.getDicts("fl_transaction_reason").then((response) => {
      this.transactionReasonsList = response.data;
    });
    //处理状态
    this.getDicts("fl_transaction_status").then((response) => {
      this.transactionStatusList = response.data;
    });
  },
  methods: {
    // 字典状态字典翻译
    ninkjetFormat(row, column) {
      return this.selectDictLabel(this.inkjetStatusList, row.ninkjet);
    },
    ndepartFormat(row, column) {
      return this.selectDictLabel(this.departStatusList, row.ndepart);
    },
    reasonFormat(row, column) {
      return this.selectDictLabel(
        this.transactionReasonsList,
        row.mreasoninkjet
      );
    },
    ntypeFormat(row, column) {
      return this.selectDictLabel(this.transactionStatusList, row.ntype);
    },

    /** 查询【事务处理】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listProcess(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        this.inkjetList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        uuid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nregdate: null,
        nflag: null,
        ninkjet: null,
        ndepart: null,
        mreasoninkjet: null,
        mreasondepart: null,
        nexecdate: null,
        ntype: null,
        mfactory: null,
        naddressup: null,
        ncol1: null,
        ncol2: null,
      };

      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("columnSystem.addTransactionProcessing");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.indexn || this.ids;
      getProcess(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("columnSystem.updateTransactionProcessing");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.indexn != null) {
            updateProcess(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addProcess(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.indexn || this.ids;
      this.$confirm(
        this.$t("columnSystem.sureCancelTransactionProcessing") +
          `"` +
          ids +
          `"` +
          this.$t("common.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delProcess(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("columnSystem.sureExportTransactionProcessing"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportProcess(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>