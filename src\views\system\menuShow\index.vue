<template>
  <div class="menu-container">
    <el-row :gutter="20">
      <el-col
        :span="4"
        :xs="24"
        :sm="24"
        :md="4"
        :lg="4"
        v-for="(item, index) in sidebarRouters"
        :key="index"
      >
        <el-card class="menu-content">
          <div class="icon-container" @click="handleItemClick(index, item)">
            <svg-icon
              class="icon"
              :style="item.hidden ? 'fill:#909399 !important' : ''"
              :icon-class="item.meta && item.meta.icon"
            />
          </div>
          <!-- <div class="menu"> -->
          <div class="menu-text" :style="item.hidden ? 'color:#909399' : ''">
            {{ $t(item.meta.title) }}
          </div>
          <!-- </div> -->
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "index",
  data() {
    return {
      menuItems: [
        {
          name: "菜单1",
          subMenu: [
            { name: "子菜单1" },
            { name: "子菜单2" },
            // 可以继续添加更多子菜单项
          ],
        },
        {
          name: "菜单2",
          subMenu: [
            { name: "子菜单3" },
            { name: "子菜单4" },
            // 可以继续添加更多子菜单项
          ],
        },
        // 可以继续添加更多菜单项
      ],
    };
  },
  computed: {
    sidebarRouters() {
      return this.$store.getters.sidebarRouters;
    },
  },
  methods: {
    handleItemClick(index, item) {
      this.$router.replace({
        path: "/module",
        query: {
          id: index,
          name: item.name,
          path: item.path,
        },
      });
    },
  },
};
</script>

<style scoped>
.menu-container {
  padding: 20px;
}

.el-card {
  margin-bottom: 20px;
  width: 100%; /* 设置宽度为容器的 100% */
}

.icon:hover {
  width: 80%; /* 设置宽度为容器的 100% */
  height: auto; /* 设置高度自适应 */
  fill: #00833e !important; /* 使用当前文本颜色填充 SVG 图标 */
}
.menu-text {
  font-size: 22px;
  /* text-align: center; 将内联元素居中 */
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  height: 30px;
  /* overflow: visible;
  overflow-wrap: break-word; */
  /* width: 90%; 设置宽度为容器的 100% */
}

.icon-container {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  width: 100%; /* 设置宽度为容器的 100% */
  height: auto; /* 设置高度自适应 */
}

.icon {
  width: 60%; /* 设置宽度为容器的 100% */
  height: auto; /* 设置高度自适应 */
  fill: #4bd7ad !important; /* 使用当前文本颜色填充 SVG 图标 */
}
</style>