<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
        <span>
          <el-select
            v-model="queryParams.nindex"
            :placeholder="$t('common.pleaseChoose')"
            size="small"
          >
            <el-option
              v-for="item in nIndexOptions"
              :key="item.nindex"
              :label="item.nindex"
              :value="item.nindex"
              @keyup.enter.native="handleQuery"
            ></el-option>
          </el-select>

          <div
            style="
              position: relative;
              display: inline-block;
              width: 80px;
              height: 10px;
            "
          >
            <i
              :class="{ icon: iconShowSmall }"
              class="el-icon-caret-top"
              style="
                font-size: 22px;
                position: absolute;
                top: -16px;
                color: #c0c4cc;
              "
              @click="handleSmall"
            ></i>
            <i
              :class="{ icon: iconShowBig }"
              class="el-icon-caret-bottom"
              style="font-size: 22px; position: absolute; color: #c0c4cc"
              @click="handleBig"
            ></i>
          </div>
        </span>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.queryDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          @click="handleGroupSet"
          v-hasPermi="['columnSystem:dailyweightday:groupset']"
          >{{ $t("columnSystem.groupSet") }}</el-button
        >
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <span style="margin-right: 100px"></span>
      </el-form-item>

      <el-form-item :label="$t('columnSystem.weightDate')" prop="ndate">
        <el-select
          v-model="queryParams.ndate"
          :placeholder="$t('common.pleaseChoose')"
          size="small"
        >
          <el-option
            v-for="item in dateList"
            :key="item"
            :label="item"
            :value="item"
            @keyup.enter.native="getFirst"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
      ></right-toolbar>
    </el-row> -->
    <el-tabs v-model="activeName" @tab-click="handleClick" tab-position="right">
      <el-tab-pane
        :label="$t('columnSystem.npignums')"
        name="first"
        :lazy="true"
      >
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category22" id="category22" style="height: 400px" />
        </div>
      </el-tab-pane>
      <el-tab-pane
        :label="$t('columnSystem.percentage')"
        name="second"
        :lazy="true"
      >
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category23" id="category23" style="height: 400px" />
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('columnSystem.visits')" name="third" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category24" id="category24" style="height: 400px" />
        </div>
      </el-tab-pane>
      <el-tab-pane
        :label="$t('columnSystem.dailySummaryData')"
        name="fourth"
        :lazy="true"
        style="padding-bottom: 20px"
      >
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['columnSystem:dailyweightday:add']"
              >{{ $t("common.add") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['columnSystem:dailyweightday:edit']"
              >{{ $t("common.update") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['columnSystem:dailyweightday:remove']"
              >{{ $t("common.delete") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['columnSystem:dailyweightday:export']"
              >{{ $t("common.export") }}</el-button
            >
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="dailyweightdayList"
          @selection-change="handleSelectionChange"
          :cell-style="{ padding: '0' }"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            :label="$t('columnSystem.mid')"
            align="center"
            prop="mid"
            width="180"
          />
          <el-table-column
            :label="$t('columnSystem.mrfid')"
            align="center"
            prop="mrfid"
            width="180"
          />
          <el-table-column
            :label="$t('columnSystem.mdorm')"
            align="center"
            prop="mdorm"
          />
          <el-table-column
            :label="$t('columnSystem.nindex')"
            align="center"
            prop="nindex"
          />
          <el-table-column
            :label="$t('columnSystem.mname')"
            align="center"
            prop="mname"
          />
          <el-table-column
            :label="$t('columnSystem.naddress')"
            align="center"
            prop="naddress"
          />
          <el-table-column
            :label="$t('columnSystem.weightDate')"
            align="center"
            prop="ndate"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.ndate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('columnSystem.weight')"
            align="center"
            prop="nweight"
          />
          <el-table-column
            :label="$t('columnSystem.visits')"
            align="center"
            prop="nweightnum"
          />
          <!-- <el-table-column :label="$t('columnSystem.npignums')" align="center" prop="npignum" /> -->
          <el-table-column
            :label="$t('common.operate')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['columnSystem:dailyweightday:edit']"
                >{{ $t("common.update") }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['columnSystem:dailyweightday:remove']"
                >{{ $t("common.delete") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        /> -->
        <!-- 添加或修改【日汇总】对话框 -->
        <el-dialog
          :title="title"
          :visible.sync="open"
          width="500px"
          append-to-body
        >
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item :label="$t('columnSystem.mid')" prop="mid">
              <el-input
                v-model="form.mid"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
              <el-input
                v-model="form.mrfid"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
              <el-input
                v-model="form.mdorm"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
              <el-input
                v-model="form.nindex"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.mname')" prop="mname">
              <el-input
                v-model="form.mname"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
              <el-input
                v-model="form.naddress"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.weightDate')" prop="ndate">
              <el-date-picker
                clearable
                size="small"
                style="width: 200px"
                v-model="form.ndate"
                type="date"
                value-format="yyyy-MM-dd"
                :placeholder="$t('common.pleaseChoose')"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="$t('columnSystem.weight')" prop="nweight">
              <el-input
                v-model="form.nweight"
                :placeholder="$t('columnSystem.pleaseEnterWeight')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.visits')" prop="nweightnum">
              <el-input
                v-model="form.nweightnum"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <!-- <el-form-item :label="$t('columnSystem.npignums')" prop="npignum">
              <el-input v-model="form.npignum" :placeholder="$t('common.pleaseInput')" />
            </el-form-item> -->
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">{{
              $t("common.determine")
            }}</el-button>
            <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
          </div>
        </el-dialog>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      :title="titleGroupSet"
      :visible.sync="openGroupSet"
      width="500px"
      append-to-body
    >
      <el-form ref="form" :rules="rules" label-width="80px">
        <el-form-item label="Group1">
          <el-input style="margin-top: 30px" v-model="group2Min" placeholder="">
            <span slot="append">{{ $t("columnSystem.Group1") }}</span>
          </el-input>
        </el-form-item>
        <el-form-item style="margin-top: -20px" label="Group2">
          <el-input
            style="margin-top: 30px"
            v-model="group2Max"
            placeholder=""
            required
          >
            <span slot="append">{{ $t("columnSystem.Group3") }}</span>
          </el-input>
        </el-form-item>
        <el-form-item style="margin-top: -20px" label="Group3">
          <!-- <el-input v-model="form.naddress" :placeholder="$t('common.pleaseInput')" /> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGroupSetForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancelGroupSet">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDailyweightday,
  getDailyweightday,
  delDailyweightday,
  addDailyweightday,
  updateDailyweightday,
  exportDailyweightday,
  listDayByCunlan,
  listDayByRate,
  listDayByFangwen,
  listdays,
} from "@/api/columnSystem/dailyweightday";
import { listControldaily } from "@/api/columnSystem/controldaily";
import * as echarts from "echarts";

export default {
  name: "Dailyweightday",
  components: {},
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true,
    },
    iconShowBig: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeName: "first",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【日汇总】表格数据
      dailyweightdayList: [],
      titleGroupSet: "",
      openGroupSet: false,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        boarid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        nweight: null,
        nweightnum: null,
        npignum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      //采食echart
      category22: null,
      //体重echart
      category23: null,
      //日增重echart
      category24: null,
      cunLanList: {},
      rateList: {},

      fangWenList: {},
      dateList: [],
      //存栏量x轴数据
      xDataNpignum: [],
      //存栏量y轴数据
      yDataNpignum: [],
      //百分比x轴数据
      xWeightPercentage: [],
      //百分比y轴数据
      yWeightPercentage: [],
      //访问量x轴数据
      xDataNweightnum: [],
      //访问量y轴数据
      yDataNweightnum: [],
      group2Min: "",
      group2Max: "",
      //栏号数组
      nIndexOptions: [],
    };
  },
  watch: {
    "queryParams.nindex": {
      handler() {
        if (this.queryParams) {
          this.handleQuery();
        }
      },
      deep: true,
    },
    "queryParams.dateRange": {
      handler() {
        if (this.queryParams) {
          this.handleQuery();
        }
      },
      deep: true,
    },
    // group2Min() {
    //   this.getListAll();
    // },
    // group2Max() {
    //   this.getListAll();
    // },
    "queryParams.ndate"() {
      this.getListAll();
    },
  },
  created() {
    listControldaily({
      ntype: 2,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.nIndexOptions = response.rows;
      this.queryParams.nindex = this.nIndexOptions[0].nindex;
    });
    this.getDicts("fl_group2").then((response) => {
      this.group2Min = response.data && response.data[0].dictValue;
      this.group2Max = response.data && response.data[1].dictValue;
    });
  },
  mounted() {
    //渲染之后
    window.onresize = () => {
      //alert("sss");
      this.category22.resize(); //重新初始化echarts
      this.category23.resize(); //重新初始化echarts
      this.category24.resize(); //重新初始化echarts
    };
  },
  methods: {
    handleSmall() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index > 0) {
          this.queryParams.nindex = this.nIndexOptions[index - 1].nindex;
        }
      } else {
        this.queryParams.nindex = this.nIndexOptions[0].nindex;
      }
    },
    handleBig() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index < this.nIndexOptions.length - 1) {
          this.queryParams.nindex = this.nIndexOptions[index + 1].nindex;
        }
      } else {
        this.queryParams.nindex =
          this.nIndexOptions[this.nIndexOptions.length - 1].nindex;
      }
    },
    handleClick(tab, event) {
      if (tab.name == "first") {
        this.$nextTick(() => {
          this.getFirst();
          // this.category22.resize();
        });
      }
      if (tab.name == "second") {
        this.$nextTick(() => {
          this.getSecond();
          // this.category22.resize();
        });
      }
      if (tab.name == "third") {
        this.$nextTick(() => {
          this.getThird();
          // this.category23.resize();
        });
      }
      if (tab.name == "fourth") {
        this.getList();
      }
    },
    getSecond() {
      // if (this.queryParams.ndate) {
      listDayByRate({
        group1: this.group2Min,
        group2: this.group2Max,
        mfactory: this.$store.state.settings.nowPigFarm,
        nindex: this.queryParams.nindex,
        ndate: this.queryParams.ndate,
      }).then((response) => {
        this.rateList = response.data;
        this.yWeightPercentage = [
          `${this.rateList.group1}` * 100,
          `${this.rateList.group2}` * 100,
          `${this.rateList.group3}` * 100,
        ];
        this.xWeightPercentage = ["Group1", "Group2", "Group3"];
        this.getPercentage();
      });
      // } else {
      //   this.msgInfo("请先输入称重日期");
      // }
    },
    getThird() {
      // if (this.queryParams.ndate) {
      listDayByFangwen({
        group1: this.group2Min,
        group2: this.group2Max,
        mfactory: this.$store.state.settings.nowPigFarm,
        nindex: this.queryParams.nindex,
        ndate: this.queryParams.ndate,
      }).then((response) => {
        this.fangWenList = response.data;
        this.yDataNweightnum = [
          `${this.fangWenList.group1}`,
          `${this.fangWenList.group2}`,
          `${this.fangWenList.group3}`,
        ];
        this.xDataNweightnum = ["Group1", "Group2", "Group3"];
        this.getNweightnumDatas();
      });
      // } else {
      //   this.msgInfo("请先输入称重日期");
      // }
    },
    getNpignumDatas() {
      this.category22 = this.$refs.category22
        ? echarts.init(this.$refs.category22, "walden")
        : "";
      this.category22 &&
        this.category22.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: {
            data: this.$t("columnSystem.cohort"),
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: this.xDataNpignum,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              name: this.$t("columnSystem.cohort"),
              type: "bar",
              stack: this.$t("columnSystem.total"),
              barWidth: "40%",
              data: this.yDataNpignum,
            },
          ],
        });
    },
    getPercentage() {
      this.category23 = this.$refs.category23
        ? echarts.init(this.$refs.category23, "walden")
        : "";
      this.category23 &&
        this.category23.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: {
            data: this.$t("columnSystem.percentage"),
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: this.xWeightPercentage,
          },
          yAxis: {
            type: "value",
            axisLabel: {
              formatter: "{value} %",
            },
          },
          series: [
            {
              name: this.$t("columnSystem.percentageAnd"),
              type: "bar",
              stack: this.$t("columnSystem.total"),
              barWidth: "40%",
              data: this.yWeightPercentage,
            },
          ],
        });
    },
    getNweightnumDatas() {
      this.category24 = this.$refs.category24
        ? echarts.init(this.$refs.category24, "walden")
        : "";
      this.category24 &&
        this.category24.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: {
            data: this.$t("columnSystem.visits"),
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: this.xDataNweightnum,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              name: this.$t("columnSystem.visits"),
              type: "bar",
              stack: this.$t("columnSystem.total"),
              barWidth: "40%",
              data: this.yDataNweightnum,
            },
          ],
        });
    },
    /** 查询【日汇总】列表 */
    getFirst() {
      // if (this.queryParams.ndate) {
      listDayByCunlan({
        group1: this.group2Min,
        group2: this.group2Max,
        mfactory: this.$store.state.settings.nowPigFarm,
        nindex: this.queryParams.nindex,
        ndate: this.queryParams.ndate,
      }).then((response) => {
        this.cunLanList = response.data;
        this.yDataNpignum = [
          `${this.cunLanList.group1}`,
          `${this.cunLanList.group2}`,
          `${this.cunLanList.group3}`,
        ];
        this.xDataNpignum = ["Group1", "Group2", "Group3"];
        this.getNpignumDatas();
      });
      // } else {
      //   this.msgInfo("请先输入称重日期");
      // }
    },

    getListAll() {
      if (this.queryParams.ndate) {
        this.getFirst();
        this.getSecond();
        this.getThird();
      } else {
        this.msgInfo(this.$t("columnSystem.pleaseFirstEnterWeightDate"));
      }
      this.getList();
    },

    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listDailyweightday(this.queryParams).then((response) => {
        this.dailyweightdayList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelGroupSet() {
      this.openGroupSet = false;
      // this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        boarid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        nweight: null,
        nweightnum: null,
        npignum: null,
      };
      this.resetForm("form");
    },

    //时间转换(js将 “2021-07-06T06:23:57.000+00:00” 转换为年月日时分秒)
    transformTimestamp(timestamp) {
      let a = new Date(timestamp).getTime();
      const date = new Date(a);
      const Y = date.getFullYear() + "-";
      const M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      const D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "  ";
      const h =
        (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      const m =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      // const s = date.getSeconds(); // 秒
      const dateString = Y + M + D + h + m;
      // console.log('dateString', dateString); // > dateString 2021-07-06 14:23
      return dateString;
    },

    // let time = '2021-07-06T06:23:57.000+00:00' ;
    // transformTimestamp（time） // 2021-07-06 14:23

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.nindex && this.queryParams.dateRange) {
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        listdays(
          this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
        ).then((response) => {
          this.dateList =
            response.data &&
            response.data.map((item) =>
              this.parseTime(this.transformTimestamp(item), "{y}-{m}-{d}")
            );
          this.queryParams.ndate = this.dateList && this.dateList[0];
        });
      } else {
        this.msgInfo(this.$t("columnSystem.pleaseFirstEnterColumn"));
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("columnSystem.addDailyWeightDay");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDailyweightday(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("columnSystem.updateDailyWeightDay");
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDailyweightday(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addDailyweightday(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    handleGroupSet() {
      this.openGroupSet = true;
      this.titleGroupSet = this.$t("columnSystem.titleGroupSet");
      // });
    },
    /** 提交按钮 */
    submitGroupSetForm() {
      // this.updateData({
      //   searchValue: null,
      //   createBy: "admin",
      //   // createTime: "2021-12-15 09:53:19",
      //   updateBy: null,
      //   updateTime: null,
      //   remark: null,
      //   params: {},
      //   dayLine: null,
      //   datefrom: null,
      //   dateto: null,
      //   dictCode: 41,
      //   dictSort: 0,
      //   dictLabel: "min",
      //   dictValue: this.group2Min,
      //   dictType: "fl_group2",
      //   cssClass: null,
      //   listClass: null,
      //   isDefault: "N",
      //   status: "0",
      //   default: false,
      // }).then((response) => {});
      // this.updateData({
      //   searchValue: null,
      //   createBy: "admin",
      //   // createTime: "2021-12-15 10:26:10",
      //   updateBy: null,
      //   updateTime: null,
      //   remark: null,
      //   params: {},
      //   dayLine: null,
      //   datefrom: null,
      //   dateto: null,
      //   dictCode: 43,
      //   dictSort: 1,
      //   dictLabel: "max",
      //   dictValue: this.group2Max,
      //   dictType: "fl_group2",
      //   cssClass: null,
      //   listClass: null,
      //   isDefault: "N",
      //   status: "0",
      //   default: false,
      // }).then((response) => {});
      // this.msgSuccess(this.$t("common.modifiedSuccess"));
      this.getListAll();
      this.openGroupSet = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("columnSystem.sureCancelDailyWeightDay") +
          `"` +
          ids +
          `"` +
          this.$t("common.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delDailyweightday(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("columnSystem.sureExportDailyWeightDay"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportDailyweightday(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>