<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="猪场号" prop="mfactory">
        <el-input
          v-model="queryParams.mfactory"
          placeholder="请输入猪场号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['alarm:recharge:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['alarm:recharge:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['alarm:recharge:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['alarm:recharge:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="rechargeList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="猪场号" align="center" prop="mfactory" />
      <el-table-column
        label="续费时间"
        align="center"
        prop="addtime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.addtime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="续费额度" align="center" prop="addnum" />
      <el-table-column label="备注" align="center" prop="tag" />
      <el-table-column label="续费前额度" align="center" prop="addpre" />
      <el-table-column label="续费后额度" align="center" prop="addback" />
      <!-- <el-table-column label="${comment}" align="center" prop="colString1" />
      <el-table-column label="${comment}" align="center" prop="colString2" />
      <el-table-column label="${comment}" align="center" prop="colString3" /> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['alarm:recharge:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['alarm:recharge:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【报警充值】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="猪场号" prop="mfactory">
          <el-input v-model="form.mfactory" placeholder="请输入猪场号" />
        </el-form-item>
        <el-form-item label="新增时间" prop="addtime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.addtime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择新增时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="续费额度" prop="addnum">
          <el-input v-model="form.addnum" placeholder="请输入续费额度" />
        </el-form-item>
        <el-form-item label="备注" prop="tag">
          <el-input v-model="form.tag" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="续费前额度" prop="addpre">
          <el-input v-model="form.addpre" placeholder="请输入续费前额度" />
        </el-form-item>
        <el-form-item label="续费后额度" prop="addback">
          <el-input v-model="form.addback" placeholder="请输入续费后额度" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import {
  listRecharge,
  getRecharge,
  delRecharge,
  addRecharge,
  updateRecharge,
  exportRecharge,
} from "@/api/alarm/recharge";

export default {
  name: "Recharge",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【报警充值】表格数据
      rechargeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        addtime: null,
        addnum: null,
        tag: null,
        addpre: null,
        addback: null,
        colString1: null,
        colString2: null,
        colString3: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mfactory: [
          { required: true, message: "猪场号不能为空", trigger: "blur" },
        ],
        addtime: [
          { required: true, message: "新增时间不能为空", trigger: "blur" },
        ],
        addnum: [
          { required: true, message: "续费额度不能为空", trigger: "blur" },
        ],
        addpre: [
          { required: true, message: "续费前额度不能为空", trigger: "blur" },
        ],
        addback: [
          { required: true, message: "续费后额度不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【报警充值】列表 */
    getList() {
      this.loading = true;
      listRecharge(this.queryParams).then((response) => {
        this.rechargeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mfactory: null,
        addtime: null,
        addnum: null,
        tag: null,
        addpre: null,
        addback: null,
        colString1: null,
        colString2: null,
        colString3: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【报警充值】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRecharge(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【报警充值】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateRecharge(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRecharge(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【报警充值】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delRecharge(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【报警充值】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportRecharge(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>