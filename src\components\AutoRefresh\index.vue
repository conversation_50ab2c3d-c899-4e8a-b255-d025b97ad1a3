<template>
  <el-button
    :type="autoRefresh ? 'success' : 'info'"
    :icon="autoRefresh ? 'el-icon-video-pause' : 'el-icon-video-play'"
    :size="size"
    @click="toggleAutoRefresh"
  >
    {{ autoRefresh ? stopText : startText }}
  </el-button>
</template>

<script>
export default {
  name: 'AutoRefresh',
  props: {
    // 刷新间隔时间（毫秒）
    interval: {
      type: Number,
      default: 30000
    },
    // 按钮大小
    size: {
      type: String,
      default: 'mini'
    },
    // 开启刷新按钮文本
    startText: {
      type: String,
      default: '开启刷新'
    },
    // 停止刷新按钮文本
    stopText: {
      type: String,
      default: '停止刷新'
    },
    // 初始状态
    defaultActive: {
      type: Boolean,
      default: true
    },
    // 页面是否激活（用于控制是否在后台刷新）
    pageActive: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 自动刷新状态
      autoRefresh: this.defaultActive,
      // 定时器
      refreshTimer: null
    };
  },
  watch: {
    // 监听页面激活状态
    pageActive(newVal) {
      if (newVal && this.autoRefresh) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },
    // 监听刷新间隔变化
    interval() {
      if (this.autoRefresh) {
        this.startAutoRefresh();
      }
    }
  },
  mounted() {
    // 组件挂载时根据初始状态启动刷新
    if (this.autoRefresh && this.pageActive) {
      this.startAutoRefresh();
    }
  },
  beforeDestroy() {
    // 组件销毁时清除定时器
    this.stopAutoRefresh();
  },
  methods: {
    // 切换自动刷新状态
    toggleAutoRefresh() {
      this.autoRefresh = !this.autoRefresh;
      
      if (this.autoRefresh) {
        this.startAutoRefresh();
        this.$message.success(this.startText.replace('开启', '已开启'));
      } else {
        this.stopAutoRefresh();
        this.$message.success(this.stopText.replace('停止', '已停止'));
      }
      
      // 向父组件发送状态变化事件
      this.$emit('refresh-change', this.autoRefresh);
    },
    
    // 启动自动刷新
    startAutoRefresh() {
      // 先停止现有的定时器
      this.stopAutoRefresh();
      
      // 只有在自动刷新开启且页面激活时才启动新的定时器
      if (this.autoRefresh && this.pageActive) {
        this.refreshTimer = setInterval(() => {
          if (this.pageActive && this.autoRefresh) {
            // 向父组件发送刷新事件
            this.$emit('refresh');
          }
        }, this.interval);
      }
    },
    
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    },
    
    // 手动触发一次刷新
    manualRefresh() {
      this.$emit('refresh');
    },
    
    // 获取当前刷新状态
    getRefreshStatus() {
      return {
        autoRefresh: this.autoRefresh,
        interval: this.interval,
        isRunning: !!this.refreshTimer
      };
    },
    
    // 设置刷新状态
    setRefreshStatus(status) {
      this.autoRefresh = status;
      if (status) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    }
  }
};
</script>

<style scoped>
/* 可以添加一些自定义样式 */
.el-button {
  transition: all 0.3s ease;
}
</style>
