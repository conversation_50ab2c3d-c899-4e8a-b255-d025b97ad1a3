<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('huanKong.pigHouseUnit')" prop="value">
        <el-cascader
          :required="true"
          v-model="queryParams.value"
          :options="options"
          size="small"
          @change="handleNodeClick"
          :placeholder="$t('huanKong.choosePigHouse')"
        ></el-cascader>
      </el-form-item>
      <el-form-item :label="$t('huanKong.selectDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('huanKong.startDate')"
          :end-placeholder="$t('huanKong.endDate')"
          @change="handleDateClick"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('huanKong.period')" prop="dayLine">
        <el-select
          v-model="queryParams.dayLine"
          :placeholder="$t('huanKong.choosePeriod')"
          @change="handleDateClick"
        >
          <el-option
            v-for="dict in dateLineList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <!-- <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          > -->
      </el-form-item>
    </el-form>

    <div class="el-table el-table--enable-row-hover el-table--medium">
      <div ref="category3" id="category3" style="height: 500px" />
    </div>
  </div>
</template>
  
  <script>
import { listHistory } from "@/api/huanKong/history";
import { treeselectEnv } from "@/api/huanKong/slave";
import * as echarts from "echarts";
require("@/utils/walden"); // echarts theme

export default {
  name: "report",
  components: {},
  data() {
    return {
      dateLineList: [],
      options: null,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      //温度节点
      category1: null,
      //温度数据
      temperatureList: [],
      //温度x轴数据
      xAxisList: [],
      //温度y轴数据
      yAxisList: [],

      //湿度节点
      category2: null,
      //湿度数据
      humidityList: [],
      //湿度x轴数据
      xAxisList2: [],
      //湿度y轴数据
      yAxisList2: [],

      //水位节点
      category3: null,
      //水位数据
      waterList: [],
      //水位x轴数据
      xAxisList3: [],
      //水位y轴数据
      yAxisList3: [],

      //用电量节点
      category4: null,
      //用电量数据
      electricList: [],
      //用电量x轴数据
      xAxisList4: [],
      //用电量y轴数据
      yAxisList4: [],

      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 弹出层标题
      title: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: null,
        id: null,
        homeid: null,
        dayLine: null,
        value: [],
        dateRange: [],
      },
    };
  },
  created() {
    this.getDicts("hk_date_duan").then((response) => {
      this.dateLineList = response.data;
      this.queryParams.dayLine =
        this.dateLineList && this.dateLineList[0].dictValue;
    });
  },
  //created 钩子函数先于 mounted 钩子函数被调用。 created 适合执行一些初始化操作，而 mounted 适合进行DOM操作或者与外部库进行集成。
  mounted() {
    this.getTree();
    window.onresize = () => {
      this.category3.resize(); //重新初始化echarts
    };
  },

  watch: {
    "$i18n.locale"(newValue) {
      // 不需要重新设置配置项，只需要手动触发一下setOption()
      this.handleQuery();
    },
  },

  methods: {
    // 表单重置
    reset() {
      this.form = {
        nindex: null,
        date: null,
        pigNum: null,
        nIngestionSSum: null,
        feedSum: null,
        nSecondSSum: null,
        weightAvg: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList3();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询【请填写功能名称】列表 */
    getTree() {
      treeselectEnv({ facid: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.options = response.data;
          this.queryParams.value = [
            this.options[0].value,
            this.options[0].children[0].value,
          ];
          this.queryParams.slaveid = this.options[0].children[0].value;
          this.queryParams.homeid = this.options[0].value;
          this.queryParams.facid = this.$store.state.settings.nowPigFarm;
          this.getList3();
        }
      );
    },
    // 节点单击事件
    handleNodeClick(data) {
      if (this.queryParams) {
        this.queryParams.slaveid = data[1];
        this.queryParams.homeid = data[0];
        this.getList3();
      }
    },
    handleDateClick(data) {
      // Object.prototype.toString.call(a) === '[object Array]';//true
      if (Object.prototype.toString.call(data) === "[object Array]") {
        this.queryParams.dayLine = null;
      } else {
        this.queryParams.dateRange = null;
      }
      this.getList3();
    },

    getList3() {
      listHistory(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        if (response && response.rows) {
          this.waterList = response.rows;
          this.xAxisList3 = [];
          this.yAxisList3 = [];
          this.waterList.forEach((element) => {
            this.xAxisList3.push(element.uptime);
            this.yAxisList3.push(element.water);
          });
        } else {
          this.waterList = [];
          this.xAxisList3 = [];
          this.yAxisList3 = [];
        }
        this.getDatas3();
      });
    },

    getDatas3() {
      this.category3 = echarts.init(this.$refs.category3, "walden");
      this.category3.setOption({
        // title: {
        //   text: "",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [`${this.$t("huanKong.waterLevel")}`],
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },

        xAxis: {
          type: "category",
          nameLocation: "end", //坐标轴名称显示位置。
          // axisLabel: {
          //   //坐标轴刻度标签的相关设置。
          //   interval: 8,
          //   rotate: "70",
          // },
          // axisLine: {
          //   symbol: 'none',
          // },
          // boundaryGap: false,
          data: this.xAxisList3.reverse(),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },

            smooth: true,
            name: this.$t("huanKong.waterLevel"),
            type: "line",
            stack: "总量",
            data: this.yAxisList3.reverse(),
            // color: "#0bd2cb",
          },
        ],
      });
    },
  },
};
</script>
  