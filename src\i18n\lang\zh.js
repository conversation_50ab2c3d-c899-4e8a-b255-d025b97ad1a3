const zh = {
  common: {
    exception: '异常',
    systemInterface: '系统接口',
    timeout: '系统接口请求超时',
    networkError: '后端接口连接异常',
    reRegister: '重新登录',
    systemHint: '系统提示',
    logInAgain: '登录状态已过期，您可以继续留在该页面，或者重新登录',
    loadingResources: '正在加载系统资源，请耐心等待',
    refresh: '刷新',
    hideSearch: '隐藏搜索',
    showSearch: '显示搜索',
    previousPage: '上一页',
    nextPage: '下一页',
    saveAll: '批量保存',
    save: '保存',
    search: '搜索',
    reset: '重置',
    add: '新增',
    update: '修改',
    delete: '删除',
    export: '导出',
    import: '导入',
    batchUpdate: '批量更新',
    serialNumber: '序号',
    operate: '操作',
    uploadTime: '上传时间',
    chooseUploadTime: '选择上传时间',
    determine: '确定',
    cancel: '取消',
    modifiedSuccess: '修改成功',
    addSuccess: '新增成功',
    deleteSuccess: '删除成功',
    warn: '警告',
    close: '关闭',
    confirmClose: '确定关闭？',
    submit: '提交',
    pleaseInput: "请输入",
    pleaseChoose: "请选择",
    notNull: '不能为空',
    dataItem: '的数据项?',
    startDate: '开始日期',
    endDate: '结束日期',
  },
  huanKong: {
    nowTem: "当前温度",
    targetTem: "目标温度",
    updateTime: "更新时间",
    online: "在线",
    offline: '离线',
    pigHouseName: '猪舍名称',
    choosePigHouseName: '请输入猪舍名称',
    pigHouseID: '猪舍id',
    choosePigHouseID: '请输入猪舍名称',
    pigFarmID: '猪场id',
    choosePigFarmID: '请输入猪场id',
    pigHouseNameNotNull: "猪舍名称不能为空",
    pigHouseIDNotNull: "猪场id不能为空",
    addPigHouse: "添加【猪舍】",
    modifyPigHouse: "修改【猪舍】",
    sureCancelPigHouse: '是否确认删除【猪舍】编号为',
    sureExportPigHouse: '是否确认导出所有【猪舍】数据项?',
    fanSwitchSet: '风机开关设置',
    minimumVentilationPhase: '最小通风阶段',
    temperatureStage: '阶段-温度',
    connectErr: '未连接',
    editTip: '修改参数之前请执行刷新操作，保证当前页面数据是最新的!',
    environmentalControl: "环控",
    pigHousesReal: "猪舍实时监控",
    controlConfiguration: "环控设备配置",
    controlData: "环控历史数据",
    controlReport: "环控汇总报告",
    nowControlNotStarted: "当前环控设备未启动！",
    temperature: "温度",
    humidity: "湿度",
    waterLevel: "水位",
    energyUsed: "用电量",
    temperatureCurve: "温度曲线",
    fixedFrequencyFan: "定频风机",
    inverterFan: "变频风机",
    curtain1: "幕帘1",
    curtain2: "幕帘2",
    curtain3: "幕帘3",
    curtain4: "幕帘4",
    choosePigHouse: '请选择猪舍',
    pigHouseUnit: '猪舍单元',
    gatewayId: '网关id',
    chooseGatewayId: '请输入网关id',
    envControlId: '环控id',
    chooseEnvControlId: '请输入环控id',
    envControlName: '环控名',
    chooseEnvControlName: '请输入环控名',
    envWorkStatus: "设备状态",
    chooseEnvWorkStatus: '请选择设备状态',
    enterTemperature: '请输入温度',
    enterHumidity: '请输入湿度',
    water: '水量',
    enterWater: '请输入水量',
    water1: '幕帘1',
    water2: '幕帘2',
    water3: '幕帘3',
    water4: '幕帘4',
    enterWater1: "请输入幕帘1",
    enterWater2: '请输入幕帘2',
    enterWater3: '请输入幕帘3',
    enterWater4: '请输入幕帘4',
    fixedFan1: "定频风机1",
    fixedFan2: '定频风机2',
    fixedFan3: "定频风机3",
    fixedFan4: '定频风机4',
    fixedFan5: "定频风机5",
    fixedFan6: '定频风机6',
    fixedFan7: "定频风机7",
    fixedFan8: '定频风机8',
    fixedFan9: "定频风机9",
    fixedFan10: '定频风机10',
    fixedFan11: "定频风机11",
    fixedFan12: '定频风机12',
    fixedFan13: "定频风机13",
    fixedFan14: '定频风机14',
    fixedFan15: "定频风机15",
    fixedFan16: '定频风机16',
    fixedFan17: "定频风机17",
    fixedFan18: '定频风机18',
    fixedFan19: "定频风机19",
    fixedFan20: '定频风机20',
    fixedFan21: "定频风机21",
    fixedFan22: '定频风机22',
    fixedFan23: "定频风机23",
    fixedFan24: '定频风机24',
    enterFixedFan1: "请输入定频风机1",
    enterFixedFan2: '请输入定频风机2',
    enterFixedFan3: "请输入定频风机3",
    enterFixedFan4: '请输入定频风机4',
    enterFixedFan5: "请输入定频风机5",
    enterFixedFan6: '请输入定频风机6',
    enterFixedFan7: "请输入定频风机7",
    enterFixedFan8: '请输入定频风机8',
    enterFixedFan9: "请输入定频风机9",
    enterFixedFan10: '请输入定频风机10',
    enterFixedFan11: "请输入定频风机11",
    enterFixedFan12: '请输入定频风机12',
    enterFixedFan13: "请输入定频风机13",
    enterFixedFan14: '请输入定频风机14',
    enterFixedFan15: "请输入定频风机15",
    enterFixedFan16: '请输入定频风机16',
    fan1: "变频风机1",
    fan2: '变频风机2',
    fan3: "变频风机3",
    fan4: '变频风机4',
    fan5: "变频风机5",
    fan6: '变频风机6',
    fan7: "变频风机7",
    fan8: '变频风机8',
    enterFan1: "请输入变频风机1",
    enterFan2: '请输入变频风机2',
    enterFan3: "请输入变频风机3",
    enterFan4: '请输入变频风机4',
    alarmInformation: '报警信息',
    alarmNo: '暂无报警信息',
    enterAlarmInformation: '请输入报警信息',
    envControlIdNotNull: '环控id不能为空',
    envControlNameNotNull: '环控名不能为空',
    gatewayIdNotNull: '网关id不能为空',
    addEnvSet: '添加【环控设备配置】',
    modEnvSet: '修改【环控设备配置】',
    sureCancelEnvSet: '是否确认删除【环控设备配置】编号为',
    dataItem: '的数据项?',
    sureExportEnvSet: '是否确认导出所有【环控设备配置】数据项?',
    addHistoryData: "添加【历史数据】",
    modHistoryData: "修改【历史数据】",
    sureCancelHistoryData: '是否确认删除【历史数据】编号为',
    sureExportHistoryData: '是否确认导出所有【历史数据】数据项?',
    selectDate: '选择日期',
    startDate: '开始日期',
    endDate: '结束日期',
    period: '时间段',
    choosePeriod: '请选择时间段',
    viewMonitoring: '视图实时监控',
    parameterRealTimeIssued: '参数实时下发',
    parameterIssuance: '参数下发',
    parameterName: '参数名称',
    parameterNameENG: '参数名称(Eng)',
    parameterNameRUS: '参数名称(RUS)',
    enterParameterName: '请输入参数名称',
    parameterType: '参数类型',
    enterParameterType: '请输入参数类型',
    point: '点位',
    enterPoint: '请输入点位',
    parameterValue: '参数值',
    enterParameterValue: '请输入参数值',
    nowStage: '当前阶段',
    stage: '阶段',
    enterStage: '请输入阶段',
    stageOrdering: '阶段排序',
    enterStageOrdering: '请输入阶段排序',
    batchEditing: '批量编辑',
    refresh: '刷新',
    requestTimedOut: '请求超时！！！',
    stageNotNull: "阶段不能为空",
    stageOrderingNotNull: "阶段排序不能为空",
    parameterNameNotNull: "参数名称不能为空",
    pointNotNull: "点位不能为空",
    parameterTypeNotNull: "类型不能为空",
    addParameterDictionary: "添加【控制器参数】",
    modifyParameterDictionary: "修改【控制器参数】",
    sureDeleteParameterDictionary: '是否确认删除【控制器参数字典】编号为',
    sureExportParameterDictionary: "是否确认导出所有【控制器参数字典】数据项?",
    addStageParameters: "添加【阶段参数】",
    modifyStageParameters: "修改【阶段参数】",
    sureDeleteStageParameters: '是否确认删除【阶段参数字典】编号为',
    sureExportStageParameters: "是否确认导出所有【阶段参数字典】数据项?",
    querying: '查询中',
    atLeastOne: '请先至少选择一项',
  },
  electric: {
    metername: '电表名称',
    enterMetername: '请输入电表名称',
    meterNameNotNull: "电表名称不能为空",
    meterIdNotNull: "电表id不能为空",
    energy: '电量(KW·H)',
    enterEnergy: '请输入电量',
    meterid: '电表ID',
    meterWorkStatus: '设备状态',
    choosetMeterWorkingStatus: '请选择设备状态',
    addMeterConfig: '添加【电表配置信息】',
    modMeterConfig: '修改【电表配置信息】',
    sureCancelMeterConfig: '是否确认删除【电表配置信息】编号为',
    sureExportMeterConfig: '是否确认导出所有【电表配置信息】数据项?',
    addMeterHistoryConfig: '添加【电表历史数据】',
    modMeterHistoryConfig: '修改【电表历史数据】',
    sureCancelMeterHistoryConfig: '是否确认删除【电表历史数据】编号为',
    sureExportMeterHistoryConfig: '是否确认导出所有【电表历史数据】数据项?',
    materUse: '电量使用'
  },
  liaota: {
    time: "次",
    totalFeedAmount: "加料总量",
    totalDischargeAmount: "放料总量",
    numberOfFeedings: "加料次数",
    numberOfDischarges: "放料次数",
    floorDataStatistics: '楼层用料统计',
    floorDataTable: '楼层用料数据',
    addfloorTableData: '添加【楼层用料数据】',
    modfloorTableData: '修改【楼层用料数据】',
    sureCancelfloorTableData: '是否确认删除【楼层用料数据】编号为',
    sureExportfloorTableData: '是否确认导出所有【楼层用料数据】数据项?',
    addfloor: '添加【楼层】',
    modfloor: '修改【楼层】',
    sureCancelfloor: '是否确认删除【楼层】编号为',
    sureExportfloor: '是否确认导出所有【楼层】数据项?',
    inOutTable: '加放料数据',
    weightTable: '实时重量数据',
    inOutLine: '加放料曲线',
    weightLine: '实时重量曲线',
    uptimeNotNull: '上传时间不能为空',
    floorIdNotNull: '楼层id不能为空',
    floorNotNull: '楼层名不能为空',
    inoroutNotNull: '加放料不能为空',
    weightInOutNotNull: '重量不能为空',
    floorId: '楼层id',
    enterfloorId: '请输入楼层id',
    floor: '楼层名',
    enterFloor: '请输入楼层名',
    addLiaoTaInOut: '添加【料塔加放料数据】',
    modLiaoTaInOut: '修改【料塔加放料数据】',
    sureCancelLiaoTaInOut: '是否确认删除【料塔加放料数据】编号为',
    sureExportLiaoTaInOut: '是否确认导出所有【料塔加放料数据】数据项?',
    inFeed: "下料量",
    outFeed: '放料量',
    inorout: '料塔行为',
    choosetInorout: '请选择是加料还是放料',
    weightInOut: "重量(kg)",
    feedtypeNotNull: '饲料类型不能为空',
    volumeNotNull: '容量不能为空',
    feedType: '饲料类型',
    enterFeedType: '请输入饲料类型',
    volume: '容量(吨)',
    enterVolume: '请输入容量',
    online: "在线",
    offline: '离线',
    max: "最大值",
    min: '最小值',
    all: '总量',
    towerUse: '料塔实时重量',
    currentWeight: "当前重量(吨)",
    upperLimit: '上限',
    lowerLimit: '下限',
    notConnected: '通讯故障',
    towerName: "料塔名",
    enterTowerName: '请输入料塔名',
    gatewayId: '网关id',
    enterGatewayId: '请输入网关id',
    pigHouseId: '猪舍id',
    enterPigHouseId: '请输入猪舍id',
    towerWorkingStatus: '设备状态',
    choosetTowerWorkingStatus: '请选择设备状态',
    weight: '实时重量(吨)',
    enterWeight: '请输入重量',
    alarmInformation: '报警信息',
    enterAlarmInformation: '请输入报警信息',
    towerId: '料塔id',
    enterTowerId: '请输入料塔id',
    towerNameNotNull: '料塔名不能为空',
    gatewayIdNotNull: '网关id不能为空',
    pigHouseIDNotNull: '猪舍id不能为空',
    towerIdNotNull: '料塔id不能为空',
    addTowerConfig: '添加【料塔配置信息】',
    modTowerConfig: '修改【料塔配置信息】',
    sureCancelTowerConfig: '是否确认删除【料塔配置信息】编号为',
    sureExportTowerConfig: '是否确认导出所有【料塔配置信息】数据项?',
    dataItem: '的数据项?',
    addLiaoTaHistory: '添加【料塔历史数据】',
    modLiaoTaHistory: '修改【料塔历史数据】',
    sureCancelLiaoTaHistory: '是否确认删除【料塔历史数据】编号为',
    sureExportLiaoTaHistory: '是否确认导出所有【料塔历史数据】数据项?',
    selectDate: '选择日期',
    startDate: '开始日期',
    endDate: '结束日期',
    period: '时间段',
    choosePeriod: '请选择时间段',
  },
  content: {
    main: "这里是内容"
  },
  menu: {
    account: "账号",
    password: "密码",
    verificationCode: "验证码",
    rememberPassword: "记住密码",
    logIn: "登 录",
    loggingIn: "登 录 中...",
    register: "注册",
    registrationCode: "注册码",
    enterRegistrationCode: "请输入注册码",
    registrationCodeNotNull: "注册码不能为空",
    usernameNotNull: "用户名不能为空",
    passwordNotNull: "密码不能为空",
    verificationCodeNotNull: "验证码不能为空",
    smartPigRaisingCloudPlatform: '登录',
    remainingDays: "剩余注册使用天数",
    day: "天",
    currentFarm: "当前猪场",
    layoutSize: "布局大小",
    switchFarms: "切换猪场",
    switchLanguage: "切换语言",
    zh: "中文",
    en: "英文",
    ru: "俄文",
    personalCenter: '个人中心',
    layoutSettings: "布局设置",
    signOut: "退出登录",
    themeStyleSettings: '主题风格设置',
    themeColor: "主题颜色",
    systemLayoutConfiguration: "系统布局配置",
    turnOn: '开启',
    fixed: "固定",
    show: "显示",
    switchCurrentFarm: "切换当前猪场",
    selectFarmName: "请选择当前猪场",
    farmNameNotNull: "猪场名不能为空",
    nowFarmNameNotNull: '当前猪场不得为空，请选择当前猪场并确认!',
    addPigFarm: "添加【猪场】",
    increasePigFarm: "增加猪场",
    farmName: '猪场名',
    enterFarmName: '请输入猪场名',
    hint: "提示",
    logOut: '确定注销并退出系统吗?',
    personalInformation: '个人信息',
    userName: "用户名称",
    phoneNumber: '手机号码',
    userMailbox: '用户邮箱',
    department: "所属部门",
    ownRole: '所属角色',
    dateCreated: '创建日期',
    basicInformation: "基本资料",
    changePassword: '修改密码',
    userNickname: '用户昵称',
    gender: "性别",
    male: '男',
    female: '女',
    usernameCannotBeEmpty: "用户昵称不能为空",
    phoneNumberCannotBeBlank: '手机号码不能为空',
    emailAddressCannotBeEmpty: '邮箱地址不能为空',
    enterCorrectEmail: '请输入正确的邮箱地址',
    enterCorrectPhone: '请输入正确的手机号码',
    enterOldPassword: '请输入旧密码',
    enterNewPassword: '请输入新密码',
    enterConfirmPassword: '请确认密码',
    sureTwoEnterMatch: '两次输入的密码不一致',
    lengthBetween: '长度在 6 到 20 个字符',
    clickUploadAvatar: '点击上传头像',
    choose: '选择',
    modifyAvatar: '修改头像',
    fileFormatError: '文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。',
    oldPassword: "旧密码",
    newPassword: '新密码',
    confirmPassword: '确认密码',
    oldPasswordCannotBeEmpty: "旧密码不能为空",
    newPasswordCannotBeEmpty: '新密码不能为空',
    confirmPasswordCannotBeEmpty: '确认密码不能为空',
    address: "地址",
    scale: '规模',
    type: '类型',
    state: "状态",
    breedingPig: '种猪',
    sow: "母猪",
    columns: '分栏',
    precisionFeeding: '精准饲喂模块',
    offline: '离线',
    frontPage: "首页",
    environmentalControl: "猪场环境管理",
    pigHouseConfiguration: "猪舍配置",
    pigHouseRealtimeMonitoring: "猪舍实时监控",
    RingControlParameterAdjustment: "环控参数调整",
    envConDeviceConfiguration: "环控设备配置",
    envConHistoricalData: "环控历史数据",
    envConSummaryReport: "环控汇总报告",
    stageParameterConfiguration: "阶段参数配置",
    singleParameterConfiguration: "单个参数配置",
    videoRealtimeMonitoring: "视频实时监控",
    electricityMeter: "能源管理模块",
    realtimeMonitoringElectricityMeters: "电表读数监控",
    meterDeviceConfiguration: "电表设备配置",
    meterHistoryData: "用电数据采集",
    meterSummaryReport: "用电趋势汇总",
    waterSummaryReport: "用水趋势汇总",
    realtimeMonitoringWater: "水表读数监控",
    waterHistoryData: "用水数据采集",
    tower: "进料管理模块",
    realtimeMonitoringMaterialTower: "料塔实时监控",
    materialTowerEquipmentConfiguration: "料塔设备配置",
    materialTowerHistoricalData: "料塔历史数据",
    towerSummaryReport: "料塔汇总报告",
    boarMeasure: "种猪测定管理",
    breedPigdata: "猪只信息",
    breedOverview: "每日猪况",
    breedMeasureday: "个体测定报告",
    breedDayReport: "测定日报告",
    breedMeasuredaygather: "汇总报告",
    breedSocialAnimal: "群体测定报告",
    breedControl: "控制器配置",
    breedControlV2: "智能控制器配置",
    breedMeasure: "原始测定数据",
    breedFeeddata: "投料数据",
    measureError: "异常数据处理",

    columnSystem: "育肥分栏模块",
    columnSystemOverview: "猪场总览",
    columnSystemDailyweightday: "日汇总",
    columnSystemDailyWeight: "分栏称重记录",
    columnSystemPigdatadaily: "分栏猪管理",
    columnSystemWeightdistribution: "体重分布",
    columnSystemTransactionprocessing: "事务处理",
    columnSystemControldaily: "设备管理",
    columnSystemPrediction: "上市预测",
    columnSystemStationGrowthReport: "分栏站生长报告",
    columnSystemIndividualGrowthReport: "个体生长报告",
    columnSystemZeroprediction: '无耳牌上市预测',
    columnSystemSametime: "同时开启",
    columnSystemAlarm: "报警记录",
    columnSystemZerocunlaninout: "无耳牌模式存栏记录"
  },
  boarMeasure: {
    dragFile: "拖文件到此处，或",
    clickUpload: "点击上传",
    hintOnly: '提示：仅允许导入“xls”或“xlsx”格式文件！导入的文件必须是基于导出文件进行更新后的版本，不要修改"唯一标识"列！',
    batchUpdateInfo: "猪只信息批量更新导入",
    changeMrfid: "更换电子耳牌",
    newMrfid: "修改后的电子耳牌",
    zeroQuery: "零耳牌查询",
    pleaseEnterColumn: "请输入栏号",
    pleaseEnterColumnFirst: "请先输入栏号，再输入耳缺号或者电子耳牌号",
    noRecords: "的猪没有找到记录",
    noData: "未查到数据",
    feedingInterval: "采食间隔时间(s)",
    inFeed: "下料量",
    feedingDate: "采食日期",
    feedTime: "投料时间",
    unfed: "未饲喂",
    fed: "已饲喂",
    zeroearDistribution: "零耳牌分配",
    distribution: "分配",
    unpicked: "未采食",
    timeout: "请求超时!!!",
    successfulLaunch: "下发成功，修改结果，请间隔5S刷新",
    timingSuccess: "校时成功",
    pleaseEnterCorrectRange: "请输入正确的范围",
    inactive: "未激活",
    active: "激活",
    stop: "停用",
    normal: "正常",
    pleaseInputPigHouseName: "请输入猪舍名称",
    pleaseInput: "请输入",
    pleaseChoose: "请选择",
    mid: "耳缺号",
    mrfid: "电子耳牌",
    mvariety: "品种",
    bgender: "性别",
    dbirthdate: "出生日期",
    mdorm: "猪舍号",
    nindex: "栏号",
    mname: "测定站",
    naddress: "设备地址",
    dstartdate: "测定开始日期",
    denddate: "测定结束日期",
    nweightStart: "开始测定体重(kg)",
    nweightEnd: "结束测定体重(kg)",
    nweight: "测定猪体重(kg)",
    ntype: "测定状态",
    remarks: "备注",
    mensurationEnd: "测定结束",
    mensurationNow: "测定中",
    addPigdata: "添加【种猪信息】",
    updatePigdata: "修改【种猪信息】",
    sureCancelPigdata: '是否确认删除【种猪信息】编号为',
    dataItem: '的数据项?',
    cannotbeRecovered: '删除后将无法恢复,若想保留数据，可修改测定状态！！！',
    sureExportPigdata: '是否确认导出所有种猪数据项?',
    notNull: '不能为空',
    sureMensurationEnd: '是否确认测定结束?',
    mensurationEndSuccess: '测定结束操作成功',
    lessThanSomeGrams: '昨天采食量低于',
    pigIs: '克的猪有',
    heads: '头',
    midIs: '耳缺号为',
    nIngestionSToday: '当天采食量(g)',
    nIngestionSLastday: '昨日采食量(g)',
    nIngestionSLast2day: '前天采食量',
    nIngestionSLast3day: '大前天采食量',
    nweightLastday: '昨日称重(kg)',
    nweightlast2day: '前日称重(kg)',
    nweightlast3day: '大前日称重(kg)',
    basicInformation: '基本信息',
    measureDays: '测定天数',
    recordDays: '记录天数',
    allNIngestionS: '总采食量(kg)',
    weightGrow: '总增重(kg)',
    liaoRouBi: '料肉比',
    dailyData: '日数据',
    ningestion: '采食量',
    weight: '体重',
    dailyGainWeight: '日增重',
    dateOfDetermination: '测定日期',
    ningestions: '每天总采食量(g)',
    weightKg: '体重(kg)',
    nfeednum: '每天采食次数',
    nseconds: '每天采食总时间(s)',
    nsecond: '每天采食总时间(h)',
    ningestionG: '采食量(g)',
    ningestionKG: '采食量(kg)',
    dailyGainWeightKg: '日增重(kg)',
    weightMid: '体重(中位值)(kg)',
    addMeasureday: "添加【个体测定信息】",
    updateMeasureday: "修改【个体测定信息】",
    sureCancelMeasureday: '是否确认删除【个体测定报告】编号为',
    sureExportMeasureday: '是否确认导出所有【个体测定报告】数据项?',
    addDayReport: "添加【测定日报告】",
    updateDayReport: "修改【测定日报告】",
    sureCancelDayReport: '是否确认删除【测定日报告】编号为',
    sureExportDayReport: '是否确认导出所有【测定日报告】数据项?',
    sureCancelGatherReport: '是否确认删除【测定日报告】编号为',
    sureExportGatherReport: '是否确认导出所有【汇总报告】数据项?',
    dataList: '数据列表',
    feedConsumptionChart: '饲料消耗图',
    averageWeightGraph: '平均体重图',
    dataAggregation: '数据汇总',
    measurePigs: '测定猪数',
    allNIngestionG: '总采食量(g)',
    intakesNumber: '采食次数',
    intakesTimeH: '采食时间(h)',
    averageWeightKg: '平均体重(kg)',
    feedConsumptionG: '饲料消耗(g)',
    measureDaysT: '测定天数(天)',
    averageDailyFeedIntake: '平均日采食量(kg)',
    allIntakesTimeS: "总采食时间(s)",
    allIntakesTimeH: "总采食时间(h)",
    averageDailyFeedIntakeG: '平均日采食量(g)',
    feedConsumption: '饲料消耗',
    startAverageWeight: '开始平均体重(kg)',
    endAverageWeight: '结束平均体重(kg)',
    averageGrowth: '平均增重(kg)',
    averageDailyGrowth: '平均日增重(kg)',
    viewIndividualFeedIntakeData: '查看【个体采食数据】',
    manualSummary: '手动汇总',
    manualSummaryDate: '手动汇总日期',
    range: '范围',
    columnFrom: '栏号从',
    columnTo: '栏号至',
    totalFeedIntake: '采食量类总',
    totalWeightGain: '增重类总',
    productionPerformance: '生产性能',
    startDate: '开始日期',
    endDate: '结束日期',
    startWeight: '开始体重(kg)',
    endWeight: '结束体重(kg)',
    nearWeight: '最近体重(kg)',
    weightGain: '增重(kg)',
    sureExportAllData: '是否确认导出所有数据项?',
    parameterDistribution: '参数下发',
    topologyNodeType: '拓扑节点类型',
    gatewayID: '网关id',
    naddressup: '上传地址',
    nserial: '设备序列号',
    nstatus: '设备状态',
    nversion: '软件版本',
    ncorrect: '料槽校准值',
    ncorrectmin: '料槽校准值最小',
    ncorrectmax: '料槽校准值最大',
    DPCkg: 'DPC(kg)',
    DPCG: 'DPC(g)',
    DPC: 'DPC',
    DPCmin: 'DPC最小',
    DPCmax: 'DPC最大',
    nsurpluskg: '饲料最低剩余量(kg)',
    nsurplus: '饲料最低剩余量',
    npulse: '投料脉冲值',
    nindivkg: '个体秤校准值(kg)',
    nindiv: '个体秤校准值',
    nindivmin: '个体秤校准值最小',
    nindivmax: '个体秤校准值最大',
    nindivnull: '个体秤空重',
    ncorrectnull: '料槽空重',
    addControl: "添加【控制器配置】",
    updateControl: "修改【控制器配置】",
    sureCancelControl: '是否确认删除【控制器配置】编号为',
    sureExportControl: '是否确认导出所有【控制器配置】数据项?',
    onlyShowControl: '只显示控制器',
    refresh: '刷新',
    saveDetails: '一次修改一个参数，修改完点击保存√，即可完成下发。每次修改须间隔5s以上，改完参数后，请间隔5s刷新',
    addFeedData: "添加【投料数据】",
    updateFeedData: "修改【投料数据】",
    sureCancelFeedData: '是否确认删除【投料数据】编号为',
    sureExportFeedData: '是否确认导出所有【投料数据】数据项?',
    intakesTime: '采食时间',
    intakesStartTime: '采食开始时间',
    intakesEndTime: '采食结束时间',
    intakesTimesS: '采食时长(s)',
    nfriweight: '料槽初重(g)',
    nsecweight: '料槽末重(g)',
    tankInitialWeight: '料槽初重',
    tankEndWeight: '料槽末重',
    nnum: '下料次数',
    addMeasure: "添加【采食数据】",
    updateMeasure: "修改【采食数据】",
    sureCancelMeasure: '是否确认删除【采食数据】编号为',
    sureExportMeasure: '是否确认导出所有【采食数据】数据项?',
    sureExportBreedSocialAnimal: '是否确认导出所有【群体动物测定汇总报告】数据项?',
    tizhongfanwei: '体重范围',
    boarrlage: '入栏时日龄',
    rlrange: '日龄范围',
    startrl: '开始日龄',
    endrl: '结束日龄',
    weight3dayerror: '体重连续3天下降大于5kg',
    multiplechoose: '多选',
  },
  columnSystem: {
    remark: '备注',
    columnMode: "分栏中",
    outLan: "出栏",
    sureOutLan: "是否确认出栏?",
    outLanSuccess: "出栏操作成功",
    nindex: "栏号",
    mname: "分栏站",
    dalarmdate: "报警日期",
    nalarmid: "报警ID",
    nstate: "消警记录",
    notNull: '不能为空',
    addAlarm: '添加【设备报警记录】',
    updateAlarm: '修改【设备报警记录】',
    sureCancelAlarm: '是否确认删除【设备报警记录】编号为"',
    sureExportAlarm: '是否确认导出所有【设备报警记录】数据项?',
    mdorm: "猪舍号",
    pleaseEnterDeptName: "请输入猪舍名称",
    onlyShowControl: '只显示控制器',
    topologyNodeType: '拓扑节点类型',
    naddress: '设备地址',
    naddressup: '主机地址',
    nserial: '设备序列号',
    nstatus: '设备状态',
    nversion: '软件版本',
    nworktype: '工作模式',
    ndirect: '开门方向',
    nsceen: '上市筛选',
    ntrend: '挑选筛选',
    ntimeopen: '分栏门开启时间',
    ntimereopen: '入口门重开时间',
    ntimedelay: '入口门延迟时间',
    nweighttime: '称重时间',
    npignums: '存栏量',
    basicInform: '基本信息',
    switchid: '网关id',
    mmemo: '备注',
    timeParameter: '时间参数',
    pleaseEnterNtimeopen: '请输入分栏门开启时间(0.2-5秒，默认0.5秒)',
    ntimeclose: '分栏门关闭时间',
    pleaseEnterNtimeclose: '请输入分栏门关闭时间(0.2-5秒，默认0.5秒)',
    pleaseEnterNtimereopen: '请输入入口门重开时间(0.2-5秒，默认0.5秒)',
    pleaseEnterNtimedelay: '请输入入口门延迟时间(0-10秒默认1秒)',
    nweightdelay: '称重延时时间',
    pleaseEnterNweightdelay: '请输入称重延时时间(0-10秒，默认2秒)',
    pleaseEnterNweighttime: '请输入称重时间(0-60秒，默认30秒)',
    calibrationAndWeight: '校准及重量',
    nindivnull: '分栏秤空重',
    pleaseEnterNindivnull: '请输入分栏秤空重(单位0.1公斤，0-200)',
    nindivweight: '分栏秤校准重量',
    pleaseEnterNindivweight: '请输入分栏秤校准重量(单位公斤 10-100)',
    nindiv: '分栏秤校准值',
    pleaseEnterNindiv: '请输入分栏秤校准值(60-120)',
    nweightstart: '称重触发重量',
    pleaseEnterNweightstart: '请输入称重触发重量(5-200,默认10公斤)',
    columnProperties: '分栏属性',
    nworktypeValue: '分栏秤工作模式',
    ndirectValue: '分栏门开门方向',
    nsceenValue: '上市分栏筛选值',
    pleaseEnterNsceenValue: '请输入上市分栏筛选值(1-255默认10)',
    ntrendValue: '挑选分栏筛选值',
    columns: '分栏',
    ngroupweight1: '轻群体重',
    ngroupweight2: '重群体重',
    ncolumnpct: '轻群分栏重量百分比',
    ncolumnweight: '重群分栏重量百分比',
    fatteningClusters: '育肥群组',
    ngroupweight3: '上市体重',
    nmarketweight2: '挑选体重上限',
    nmarketweight1: '挑选体重下限',
    nlightpct: '轻群百分比',
    nmidweight: '中群百分比',
    nheavypct: '重群百分比',
    ncorrect: '料槽校准值',
    nsurplus: '饲料最低剩余量',
    npulse: '投料脉冲值',
    nindiv: '个体秤校准值',
    nindivnull: '个体秤空重',
    ncorrectnull: '料槽空重',
    pleaseEnterCorrectRange: "请输入正确的范围",
    timingSuccess: "校时成功",
    addControl: '添加【控制器配置】',
    parameterDistribution: '参数下发',
    requestTimedOut: '请求超时！！！',
    updateControl: '修改【控制器配置】',
    sureCancelControl: '是否确认删除【控制器配置】编号为"',
    sureExportControl: '是否确认导出所有【设备报警记录】数据项是否确认导出所有【控制器配置】数据项?',
    iszeromodel: '耳牌模式',
    zeromodel: '无耳牌模式',
    notzeromodel: '有耳牌模式',

    mid: '耳缺号',
    mrfid: "电子耳牌",
    weightDate: "称重日期",
    dataType: '数据类型',
    weight: "体重(kg)",
    ntemp: "温度",
    addDailyWeight: '添加【分栏称重记录】',
    updateDailyWeight: '修改【分栏称重记录】',
    sureCancelDailyWeight: '是否确认删除【分栏称重记录】编号为"',
    sureExportDailyWeight: '是否确认导出所有【分栏称重记录】数据项?',

    queryDate: "查询日期",
    groupSet: '分组设置',
    percentage: '百分比',
    percentageAnd: '百分比(%)',
    visits: '访问量',
    dailySummaryData: '日汇总数据',
    pleaseEnterWeight: '请输入体重：单位0.1公斤',
    Group1: '小于该值的为Group1',
    Group3: '大于该值的为Group3',
    cohort: '存栏量',
    total: '总量',
    pleaseFirstEnterWeightDate: '请先输入称重日期',
    pleaseFirstEnterColumn: '请先输入栏号和日期范围',
    addDailyWeightDay: '添加【日汇总】',
    updateDailyWeightDay: '修改【日汇总】',
    sureCancelDailyWeightDay: '是否确认删除【日汇总】编号为"',
    sureExportDailyWeightDay: '是否确认导出所有【日汇总】数据项?',
    titleGroupSet: '分组体重设置',

    measureDays: '测定天数',
    lowWeight: '最低体重',
    heightWeight: '最高体重',
    dailyGainWeight: '日增重',
    dailyData: '日数据',
    dateOfDetermination: '测定日期',
    weightTab: "体重",
    noPigsUnderThisPen: '该栏下没有分栏猪',
    addIndividualGrowthReport: '添加【个体生长报告】',
    updateIndividualGrowthReport: '修改【个体生长报告】',
    sureCancelIndividualGrowthReport: '是否确认删除【个体生长报告】编号为"',
    sureExportIndividualGrowthReport: '是否确认导出所有【个体生长报告】数据项?',
    noRecords: "的猪没有找到记录",
    pleaseEnterColumnFirst: "请先输入栏号，再输入耳缺号或者电子耳牌号",

    nowColumnCohort: "当前分栏站的存栏量为",
    lessThanFeedNum: '昨天采食次数低于',
    pigIs: '次的猪有',
    feedTimesToday: '今日进食次数',
    feedTimesYest: '昨日进食次数',
    nWeightToday: '今日称重(kg)',
    nWeightYest: '昨日称重(kg)',

    ntype: "分栏状态",
    ddateinkjet: "喷墨日期",
    nearWeight: '最近体重(kg)',
    transaction: "事务处理",
    mreasoninkjet: "处理原因",
    ninkjet: "是否喷墨",
    ndepart: "是否分离",
    addPigdataDaily: '添加【分栏猪数据】',
    updatePigdataDaily: '修改【分栏猪数据】',
    sureCancelPigdataDaily: '是否确认删除【分栏猪数据】编号为"',
    sureExportPigdataDaily: '是否确认导出所有【分栏猪数据】数据项?',
    transactionAddSuccess: "事务处理新增成功",

    targetWeight: '目标出栏体重',
    targetWeightLow: '最小体重',
    targetWeightHigh: '最大体重',
    filteringScope: '筛选范围',
    listingDate: '上市日',
    intervalDays: '间隔天数',
    growthCalculations: '生长计算依据',
    adgRelyDays: 'ADG值基于过去天数',
    isAdgStable: 'ADG固定值',
    digital: '数据',
    sure: '确认',
    targerDay: '上市日期',
    lowWeightNum: '较轻',
    targetNum: '上市',
    highWeightNum: '较重',
    lowWeightPct: '较轻(%)',
    targetWeightPct: '上市(%)',
    highWeightPct: '较重(%)',

    nsametimeFrom: '开始日期',
    nsametimeTo: '结束日期',
    recordList: '记录列表',
    nsametime: '开门时间',
    ntimes: '延续时间',
    accruedTime: '累积时间',
    addSameTime: '添加【同时开启】',
    updateSameTime: '修改【同时开启】',
    sureCancelSameTime: '是否确认删除【同时开启】编号为"',
    sureExportSameTime: '是否确认导出所有【同时开启】数据项?',

    date: '日期',
    dataList: '数据列表',
    midWeight: '中位值',
    lightWeight: '轻群',
    highWeight: '重群',
    report: '报告',
    firstInputColumnAndDate: '请先输入栏号和日期',
    midWeightKG: '中位值(kg)',
    lightWeightKG: '轻群(kg)',
    highWeightKG: '重群(kg)',

    launchDate: '启动日期',
    nexecdate: '处理日期',
    addTransactionProcessing: '添加【事务处理】',
    updateTransactionProcessing: '修改【事务处理】',
    sureCancelTransactionProcessing: '是否确认删除【事务处理】编号为"',
    sureExportTransactionProcessing: '是否确认导出所有【事务处理】数据项?',

    WeightDistributionData: '体重分布数据',
    enterWeighingDateFirst: '请先输入称重日期',
    addWeightdistribution: '添加【体重分布】',
    updateWeightdistribution: '修改【体重分布】',
    sureCancelWeightdistribution: '是否确认删除【体重分布】编号为"',
    sureExportWeightdistribution: '是否确认导出所有【体重分布】数据项?',
    noData: "未查到数据",
    enterDate: "入栏日期",
    enterWeight: "入栏体重",

    cunlanNum: '存栏数量',
    zeroyccunlan: '无耳牌预测存栏量',

    targetWeight2: '目标体重',
    targetDate: '目标出栏日期',
    gjdays: '预计天数',
    gjdate: '预计上市日期',
    rlage: '入栏时日龄',
    birage: '日龄',
    toushu: '预计数量(头)',
    avgvisit: '平均访问次数',
    hourfangwen: '每小时访问量'
  },
  warning: {
    distribution: '监控节点分配',
    username: '账号名称',
    nodeAssignment: '监控节点分配',
    operation: '操作',
    edit: '编辑',
    save: '保存',
    cancel: '取消',
    unassigned: '未分配',
    selectNodes: '请选择监控节点',
    saveSuccess: '保存成功',
    saveFailed: '保存失败'
  }
}
export default zh;
