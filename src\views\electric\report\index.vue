<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('electric.metername')" prop="metername">
        <el-select
          v-model="queryParams.metername"
          :placeholder="$t('electric.enterMetername')"
          size="small"
        >
          <el-option
            v-for="item in meterOptions"
            :key="item.id"
            :label="item.metername"
            :value="item.metername"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('liaota.selectDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('liaota.startDate')"
          :end-placeholder="$t('liaota.endDate')"
          @change="handleDateClick"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('liaota.period')" prop="dayLine">
        <el-select
          v-model="queryParams.dayLine"
          :placeholder="$t('liaota.choosePeriod')"
          @change="handleDateClick"
        >
          <el-option
            v-for="dict in dateLineList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
      </el-form-item>
    </el-form>

    <div class="el-table el-table--enable-row-hover el-table--medium">
      <div ref="category1" id="category1" style="height: 500px" />
    </div>
    <p v-for="(message, idx) in messages" :key="idx">{{ message }}</p>
  </div>
</template>

<script>
import { listElectricHistory } from "@/api/electric/history";
import { listMeter } from "@/api/electric/meter";
import * as echarts from "echarts";
require("@/utils/walden"); // echarts theme

// let sseClient;

export default {
  name: "electricReport",
  components: {},
  data() {
    return {
      messages: [],
      // sse: {
      //   cleanup: true,
      // },
      dateLineList: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      //饲料消耗
      category1: null,
      //电表Echart
      meterEchartList: [],
      //电表x轴数据
      xDataMeter: [],
      //电表y轴数据
      yDataMeter: [],
      //料塔值集
      meterOptions: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 弹出层标题
      title: "",
      // 查询参数
      queryParams: {
        metername: null,
        dayLine: null,
        pageNum: 1,
        pageSize: null,
        dateRange: [],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  created() {
    this.getDicts("hk_date_duan").then((response) => {
      this.dateLineList = response.data;
      this.queryParams.dayLine =
        this.dateLineList && this.dateLineList[0].dictValue;
    });
    this.getList();
  },
  mounted() {
    listMeter({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.meterOptions = response.rows;
        this.queryParams.metername =
          this.meterOptions && this.meterOptions[0].metername;
      }
    );
    this.getList();
    window.onresize = () => {
      this.category1.resize(); //重新初始化echarts
    };
  },
  beforeDestroy() {
    // Make sure to close the connection with the events server
    // when the component is destroyed, or we'll have ghost connections!
    // sseClient.disconnect();
    // Alternatively, we could have added the `sse: { cleanup: true }` option to our component,
    // and the SSEManager would have automatically disconnected during beforeDestroy.
  },
  methods: {
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      if (this.queryParams.metername) {
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        listElectricHistory(
          this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
        ).then((response) => {
          if (response && response.rows) {
            this.meterEchartList = response.rows;
            this.xDataMeter = [];
            this.yDataMeter = [];
            this.meterEchartList.forEach((element) => {
              this.xDataMeter.push(element.uptime);
              this.yDataMeter.push(element.energy);
            });
          } else {
            this.meterEchartList = [];
            this.xDataMeter = [];
            this.yDataMeter = [];
          }
          this.loading = false;
          this.getMeterConsumeData();
        });
      }
    },

    /** 搜索按钮操作 */
    handleQuery(data) {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleDateClick(data) {
      if (Object.prototype.toString.call(data) === "[object Array]") {
        this.queryParams.dayLine = null;
      } else {
        this.queryParams.dateRange = null;
      }
      this.getList();
    },

    getMeterConsumeData() {
      this.category1 = echarts.init(this.$refs.category1, "walden");
      this.category1.setOption({
        title: {
          text: "",
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [`${this.$t("electric.materUse")}`],
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.xDataMeter,
          // inverse: true,
          nameLocation: "end", //坐标轴名称显示位置。
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            name: this.$t("electric.materUse"),
            type: "line",
            stack: this.$t("liaota.all"),
            data: this.yDataMeter,
            inverse: true,
            markPoint: {
              data: [
                {
                  name: this.$t("liaota.max"),
                  type: "max",
                },
                {
                  name: this.$t("liaota.min"),
                  type: "min",
                },
              ],
            },
          },
        ],
      });
    },
  },
};
</script>
