import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listTongji(query) {
  return request({
    url: '/system/tongji/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getTongji(id) {
  return request({
    url: '/system/tongji/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addTongji(data) {
  return request({
    url: '/system/tongji',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateTongji(data) {
  return request({
    url: '/system/tongji',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delTongji(id) {
  return request({
    url: '/system/tongji/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportTongji(query) {
  return request({
    url: '/system/tongji/export',
    method: 'get',
    params: query
  })
}