import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listEstrousday(query) {
  return request({
    url: '/system/estrousday/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getEstrousday(id) {
  return request({
    url: '/system/estrousday/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addEstrousday(data) {
  return request({
    url: '/system/estrousday',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateEstrousday(data) {
  return request({
    url: '/system/estrousday',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delEstrousday(id) {
  return request({
    url: '/system/estrousday/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportEstrousday(query) {
  return request({
    url: '/system/estrousday/export',
    method: 'get',
    params: query
  })
}
