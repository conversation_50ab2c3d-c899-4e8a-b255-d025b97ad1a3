import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listPiginout(query) {
  return request({
    url: '/system/piginout/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getPiginout(id) {
  return request({
    url: '/system/piginout/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addPiginout(data) {
  return request({
    url: '/system/piginout',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updatePiginout(data) {
  return request({
    url: '/system/piginout',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delPiginout(id) {
  return request({
    url: '/system/piginout/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportPiginout(query) {
  return request({
    url: '/system/piginout/export',
    method: 'get',
    params: query
  })
}