<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" class="card-box">
        <!-- <el-card> -->
        <!-- <div slot="header"><span>搜索参数</span></div> -->
        <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
        >
          <el-form-item :label="$t('columnSystem.columnMode')" prop="checked">
            <el-checkbox v-model="queryParams.checked"/>
          </el-form-item>
          <!-- <el-form-item :label="$t('columnSystem.mname')" prop="mname">
            <span>
              <el-select
                v-model="queryParams.mname"
                :placeholder="$t('common.pleaseChoose')"
                size="small"
                @change="listPigdata"
              >
                <el-option
                  v-for="item in mNameOptions"
                  :key="item.mname"
                  :label="item.mname"
                  :value="item.mname"
                  @keyup.enter.native="handleQuery"
                ></el-option>
              </el-select>

              <div
                style="
                  position: relative;
                  display: inline-block;
                  width: 20px;
                  height: 10px;
                "
              >
                <i
                  :class="{ icon: iconShowSmall }"
                  class="el-icon-caret-top"
                  style="
                    font-size: 22px;
                    position: absolute;
                    top: -16px;
                    color: #c0c4cc;
                  "
                  @click="handleSmall"
                ></i>
                <i
                  :class="{ icon: iconShowBig }"
                  class="el-icon-caret-bottom"
                  style="font-size: 22px; position: absolute; color: #c0c4cc"
                  @click="handleBig"
                ></i>
              </div>
            </span>
          </el-form-item> -->
          <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
            <span>
              <el-select
                  v-model="queryParams.naddress"
                  :placeholder="$t('common.pleaseChoose')"
                  size="small"
                  @change="listPigdata"
              >
                <el-option
                    v-for="item in mNameOptions"
                    :key="item.naddress"
                    :label="item.naddress"
                    :value="item.naddress"
                    @click.native="listPigdata"
                ></el-option>
              </el-select>

              <div
                  style="
                  position: relative;
                  display: inline-block;
                  width: 80px;
                  height: 10px;
                "
              >
                <i
                    :class="{ icon: iconShowSmall }"
                    class="el-icon-caret-top"
                    style="
                    font-size: 22px;
                    position: absolute;
                    top: -16px;
                    color: #c0c4cc;
                  "
                    @click="handleSmall"
                ></i>
                <i
                    :class="{ icon: iconShowBig }"
                    class="el-icon-caret-bottom"
                    style="font-size: 22px; position: absolute; color: #c0c4cc"
                    @click="handleBig"
                ></i>
              </div>
            </span>
          </el-form-item>
          <el-form-item :label="$t('columnSystem.mid')" prop="mid">
            <!-- <el-input
              v-model="queryParams.mid"
              :placeholder="$t('common.pleaseInput')"
              clearable
              size="small"
              @change="handleMid"
            /> -->
            <span>
              <el-select
                  v-model="queryParams.mid"
                  :placeholder="$t('common.pleaseChoose')"
                  clearable
                  filterable
                  size="small"
              >
                <el-option
                    v-for="item in mrfidOptions"
                    :key="item.mid"
                    :label="item.mid"
                    :value="item.mid"
                    @click.native="handleMid"
                ></el-option>
              </el-select>
              <div
                  style="
                  position: relative;
                  display: inline-block;
                  width: 20px;
                  height: 10px;
                "
              >
                <i
                    :class="{ icon: iconShowSmall }"
                    class="el-icon-caret-top"
                    style="
                    font-size: 22px;
                    position: absolute;
                    top: -16px;
                    color: #c0c4cc;
                  "
                    @click="handleSmallMid"
                ></i>
                <i
                    :class="{ icon: iconShowBig }"
                    class="el-icon-caret-bottom"
                    style="font-size: 22px; position: absolute; color: #c0c4cc"
                    @click="handleBigMid"
                ></i>
              </div>
            </span>
          </el-form-item>
          <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
            <span>
              <el-select
                  v-model="queryParams.mrfid"
                  :placeholder="$t('common.pleaseChoose')"
                  clearable
                  filterable
                  size="small"
              >
                <el-option
                    v-for="item in mrfidOptions"
                    :key="item.mrfid"
                    :label="item.mrfid"
                    :value="item.mrfid"
                    @click.native="handleMrfid"
                ></el-option>
              </el-select>
              <div
                  style="
                  position: relative;
                  display: inline-block;
                  width: 20px;
                  height: 10px;
                "
              >
                <i
                    :class="{ icon: iconShowSmall }"
                    class="el-icon-caret-top"
                    style="
                    font-size: 22px;
                    position: absolute;
                    top: -16px;
                    color: #c0c4cc;
                  "
                    @click="handleSmallRfid"
                ></i>
                <i
                    :class="{ icon: iconShowBig }"
                    class="el-icon-caret-bottom"
                    style="font-size: 22px; position: absolute; color: #c0c4cc"
                    @click="handleBigRfid"
                ></i>
              </div>
            </span>
          </el-form-item>
          <el-form-item
              :label="$t('boarMeasure.dateOfDetermination')"
              prop="dateRange"
          >
            <el-date-picker
                v-model="queryParams.dateRange"
                size="small"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                :start-placeholder="$t('common.startDate')"
                :end-placeholder="$t('common.endDate')"
                @input="changeDate"
            ></el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button
                type="cyan"
                icon="el-icon-search"
                size="mini"
                @click="getPigDataByMid"
            >{{ $t('common.search') }}
            </el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
                $t('common.reset')
              }}
            </el-button>
          </el-form-item>
        </el-form>
        <div></div>
        <!-- </el-card> -->
        <el-card>
          <div slot="header">
            <span>{{ $t('columnSystem.basicInform') }}</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <tbody>
              <tr>
                <td>
                  <div class="cell">{{ $t('columnSystem.nindex') }}</div>
                </td>
                <td>
                  <div class="cell" v-if="pigdata">{{ pigdata.nindex }}</div>
                </td>
                <td>
                  <div class="cell">{{ $t('columnSystem.mid') }}</div>
                </td>
                <td>
                  <div class="cell" v-if="pigdata">{{ pigdata.mid }}</div>
                </td>
                <td>
                  <div class="cell">{{ $t('columnSystem.mrfid') }}</div>
                </td>
                <td>
                  <div class="cell" v-if="pigdata">{{ pigdata.mrfid }}</div>
                </td>
                <td>
                  <div class="cell">
                    {{ $t('columnSystem.dailyGainWeight') }}
                  </div>
                </td>
                <td>
                  <div
                      class="cell"
                      v-if="heightWeight && lowWeight && measureDays"
                  >
                    {{
                      ((heightWeight - lowWeight) / measureDays * 1000).toFixed(2)
                    }}g/day
                  </div>
                </td>
              </tr>

              <tr>
                <td>
                  <div class="cell">{{ $t('columnSystem.measureDays') }}</div>
                </td>
                <td>
                  <div class="cell" v-if="measureDays">{{ measureDays }}</div>
                </td>

                <td>
                  <div class="cell">{{ $t('columnSystem.lowWeight') }}</div>
                </td>
                <td>
                  <div class="cell">
                    {{ lowWeight }}
                  </div>
                </td>
                <td>
                  <div class="cell">
                    {{ $t('columnSystem.heightWeight') }}
                  </div>
                </td>
                <td>
                  <div class="cell">
                    {{ heightWeight }}
                  </div>
                </td>
                <td>
                  <div class="cell">
                    {{ $t('columnSystem.dailyGainWeight') }}
                  </div>
                </td>
                <td>
                  <div
                      class="cell"
                      v-if="heightWeight && lowWeight && measureDays"
                  >
                    {{
                      ((heightWeight - lowWeight) / measureDays).toFixed(2)
                    }}kg/day
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="cell">{{ $t('columnSystem.birage') }}</div>
                </td>
                <td>
                  <div class="cell" v-if="birage">{{ birage }}</div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane
                :label="$t('columnSystem.dailyData')"
                name="first"
                :lazy="true"
                style="padding-bottom: 10px"
            >
              <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                  <el-button
                      type="primary"
                      icon="el-icon-plus"
                      size="mini"
                      @click="handleAdd"
                      v-hasPermi="['columnSystem:individualgrowthreport:add']"
                  >{{ $t('common.add') }}
                  </el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                      type="success"
                      icon="el-icon-edit"
                      size="mini"
                      :disabled="single"
                      @click="handleUpdate"
                      v-hasPermi="['columnSystem:individualgrowthreport:edit']"
                  >{{ $t('common.update') }}
                  </el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                      type="danger"
                      icon="el-icon-delete"
                      size="mini"
                      :disabled="multiple"
                      @click="handleDelete"
                      v-hasPermi="['columnSystem:individualgrowthreport:remove']"
                  >{{ $t('common.delete') }}
                  </el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                      type="warning"
                      icon="el-icon-download"
                      size="mini"
                      @click="handleExport"
                      v-hasPermi="['columnSystem:individualgrowthreport:export']"
                  >{{ $t('common.export') }}
                  </el-button
                  >
                </el-col>
                <right-toolbar
                    :showSearch.sync="showSearch"
                    @queryTable="getList"
                ></right-toolbar>
              </el-row>
              <el-table
                  v-loading="loading"
                  :data="measuredayList"
                  @selection-change="handleSelectionChange"
                  :cell-style="{ padding: '0' }"
              >
                <el-table-column type="selection" width="55" align="center"/>
                <!-- <el-table-column :label="$t('common.serialNumber')" align="center" prop="indexn" /> -->
                <el-table-column
                    :label="$t('columnSystem.mrfid')"
                    align="center"
                    prop="mrfid"
                    width="180"
                    sortable
                />
                <!-- <el-table-column :label="$t('columnSystem.mdorm')" align="center" prop="mdorm" />
                <el-table-column  :label="$t('columnSystem.nindex')"  align="center" prop="nindex" />
                <el-table-column :label="$t('columnSystem.mname')" align="center" prop="mname" />
                <el-table-column
                  :label="$t('columnSystem.naddress')"
                  align="center"
                  prop="naddress"
                /> -->
                <el-table-column
                    :label="$t('columnSystem.dateOfDetermination')"
                    align="center"
                    prop="ndate"
                    width="180"
                    sortable
                >
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.ndate, '{y}-{m}-{d}') }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                    :label="$t('columnSystem.weight')"
                    align="center"
                    prop="nweight"
                    sortable
                />
                <el-table-column
                    :label="$t('columnSystem.ntemp')"
                    align="center"
                    prop="ntemp"
                    sortable
                />
              </el-table>
              <pagination
                  v-show="total > 0"
                  :total="total"
                  :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize"
                  @pagination="getPigDataByMid"
              />

              <!-- 添加或修改【个体测定信息】对话框 -->
              <el-dialog
                  :title="title"
                  :visible.sync="open"
                  width="500px"
                  append-to-body
              >
                <el-form
                    ref="form"
                    :model="form"
                    :rules="rules"
                    label-width="80px"
                >
                  <el-form-item :label="$t('columnSystem.mid')" prop="mid">
                    <el-input
                        v-model="form.mid"
                        :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
                    <el-input
                        v-model="form.mrfid"
                        :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
                    <el-input
                        v-model="form.mdorm"
                        :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item
                      :label="$t('columnSystem.nindex')"
                      prop="nindex"
                  >
                    <el-input
                        v-model="form.nindex"
                        :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('columnSystem.mname')" prop="mname">
                    <el-input
                        v-model="form.mname"
                        :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item
                      :label="$t('columnSystem.naddress')"
                      prop="naddress"
                  >
                    <el-input
                        v-model="form.naddress"
                        :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item
                      :label="$t('columnSystem.dateOfDetermination')"
                      prop="ndate"
                  >
                    <el-date-picker
                        clearable
                        size="small"
                        style="width: 200px"
                        v-model="form.ndate"
                        type="date"
                        value-format="yyyy-MM-dd"
                        :placeholder="$t('common.pleaseChoose')"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item
                      :label="$t('columnSystem.weight')"
                      prop="nweight"
                  >
                    <el-input
                        v-model="form.nweight"
                        :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                  <el-button type="primary" @click="submitForm">{{
                      $t('common.determine')
                    }}
                  </el-button>
                  <el-button @click="cancel">{{
                      $t('common.cancel')
                    }}
                  </el-button>
                </div>
              </el-dialog>
            </el-tab-pane>
            <el-tab-pane
                :label="$t('columnSystem.weightTab')"
                name="third"
                :lazy="true"
            >
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category23" id="category23" style="height: 400px"/>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
      <!-- <el-col :span="10" class="card-box">
        <el-card>
          <div slot="header">
            <span>采食量</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="category22" id="category22" style="height: 300px" />
          </div>
        </el-card>
        <el-card>
          <div slot="header">
            <span>体重</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="category23" id="category23" style="height: 300px" />
          </div>
        </el-card>
      </el-col> -->
    </el-row>
  </div>
</template>

<script>
import {
  listDailyweightday,
  getDailyweightday,
  delDailyweightday,
  addDailyweightday,
  updateDailyweightday,
  exportDailyweightday
} from '@/api/columnSystem/dailyweightday'
import * as echarts from 'echarts'

require('@/utils/walden') // echarts theme

import { listPigdatadaily } from '@/api/columnSystem/pigdatadaily'
import { formatDate, formatDay, getDaysDiffBetweenDates, getDateDifferenceInDays } from '@/utils'
import { listControldaily } from '@/api/columnSystem/controldaily'
import { isNil, isEmpty } from 'lodash'

export default {
  name: 'Measureday',
  components: {},
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true
    },
    iconShowBig: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      //电子耳牌数组
      mrfidOptions: [],
      //栏号数组
      mNameOptions: [],
      activeName: 'first',
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【测定日报告】表格数据
      measuredayList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      //测定天数
      measureDays: null,
      //记录天数
      recordDays: null,
      //最低体重
      lowWeight: null,
      //最高体重
      heightWeight: null,
      //日龄
      birage: null,

      pigdata: null,
      //采食echart
      category22: null,
      //体重echart
      category23: null,
      //日增重echart
      category24: null,
      //采食x轴数据
      xDataFood: [],
      //采食y轴数据
      yDataFood: [],
      //体重x轴数据
      xWeight: [],
      //体重y轴数据
      yWeight: [],
      //日增重x轴数据
      xWeightGrow: [],
      //日增重y轴数据
      yWeightGrow: [],
      //foodEchart
      foodEchartList: [],
      //weightEchart
      weightEchartList: [],
      weightGrowList: [],

      queryParams: {
        checked: true,
        pageNum: 1,
        pageSize: 100,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        ningestions: null,
        nfeednum: null,
        nseconds: null,
        nweight: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // mid: [{ required: true, message: "耳缺号不能为空", trigger: "blur" }],
        mrfid: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        mdorm: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        nindex: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        mname: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        naddress: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        ndate: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        ningestions: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        nfeednum: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        nseconds: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ],
        nweight: [
          {
            required: true,
            message: this.$t('common.notNull'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    // queryParams: {
    //   handler() {
    //     if (
    //       this.queryParams &&
    //       (this.queryParams.mid || this.queryParams.mrfid)
    //     ) {
    //       this.getPigDataByMid();
    //     }

    //     // this.listPigdata();
    //     // this.getPigDataByMid();
    //   },
    //   deep: true,
    // },
    'queryParams.checked'() {
      this.listPigdata()
    }
  },
  created() {
    listControldaily({
      ntype: 3,
      mfactory: this.$store.state.settings.nowPigFarm
    }).then((response) => {
      this.mNameOptions = response.rows
      this.queryParams.naddress = this.mNameOptions[0].naddress
      listPigdatadaily({
        naddress: this.queryParams.naddress,
        mfactory: this.$store.state.settings.nowPigFarm,
        ntype: this.queryParams.checked && 1
      }).then((response) => {
        if (response && !isEmpty(response.rows)) {
          this.mrfidOptions = response.rows
          this.queryParams.mrfid = this.mrfidOptions[0].mrfid
          this.queryParams.mid = this.mrfidOptions[0].mid
          this.getPigDataByMid()
        } else {
          this.msgInfo(this.$t('columnSystem.noPigsUnderThisPen'))
        }
      })
    })

    //this.getList();
  },
  mounted() {
    //渲染之后
    //this.getDatas();
    window.onresize = () => {
      //alert("sss");
      //this.category22.resize(); //重新初始化echarts
      this.category23.resize() //重新初始化echarts
      //this.category24.resize(); //重新初始化echarts
    }
  },
  methods: {
    changeDate() {
      this.$forceUpdate()
    },
    addDate(date, days) {
      var d = new Date(date)
      d.setDate(d.getDate() + days)
      var m = d.getMonth() + 1
      return d.getFullYear() + '-' + m + '-' + d.getDate()
    },


    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('columnSystem.addIndividualGrowthReport')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const indexn = row.id || this.ids
      getDailyweightday(indexn).then((response) => {
        this.form = response.data
        this.open = true
        this.title = this.$t('columnSystem.updateIndividualGrowthReport')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDailyweightday(this.form).then((response) => {
              this.msgSuccess(this.$t('common.modifiedSuccess'))
              this.open = false
              this.getList()
            })
          } else {
            addDailyweightday(this.form).then((response) => {
              this.msgSuccess(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      console.log('11', row.id, this.ids)
      const indexns = row.id || this.ids
      this.$confirm(
          this.$t('columnSystem.sureCancelIndividualGrowthReport') +
          `"` +
          indexns +
          `"` +
          this.$t('common.dataItem'),
          this.$t('common.warn'),
          {
            confirmButtonText: this.$t('common.determine'),
            cancelButtonText: this.$t('common.cancel'),
            type: 'warning'
          }
      )
          .then(function() {
            return delDailyweightday(indexns)
          })
          .then(() => {
            this.getList()
            this.msgSuccess(this.$t('common.delete'))
          })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = { ...this.queryParams }
      queryParams.naddress = null
      this.$confirm(
          this.$t('columnSystem.sureExportIndividualGrowthReport'),
          this.$t('common.warn'),
          {
            confirmButtonText: this.$t('common.determine'),
            cancelButtonText: this.$t('common.cancel'),
            type: 'warning'
          }
      )
          .then(function() {
            return exportDailyweightday(queryParams)
          })
          .then((response) => {
            this.download(response.msg)
          })
    },
    listPigdata() {
      listPigdatadaily({
        naddress: this.queryParams.naddress,
        mfactory: this.$store.state.settings.nowPigFarm,
        ntype: this.queryParams.checked && 1
      }).then((response) => {
        if (response && !isEmpty(response.rows)) {
          this.mrfidOptions = response.rows
          this.queryParams.mrfid = this.mrfidOptions[0].mrfid
          this.queryParams.mid = this.mrfidOptions[0].mid
          this.getPigDataByMid()
        } else {
          alert(this.$t('columnSystem.noData'))
          this.mrfidOptions = []
          this.queryParams.mrfid = null
          this.queryParams.mid = null
          this.pigdata = {}
          this.measureDays = null
          this.recordDays = null
          this.lowWeight = null
          this.heightWeight = null
          this.measuredayList = []
          this.birage = null
        }
      })
    },
    handleSmall() {
      if (this.queryParams.naddress) {
        let index = this.mNameOptions.findIndex(
            (item) => item.naddress == this.queryParams.naddress
        )
        if (index > 0) {
          this.queryParams.naddress = this.mNameOptions[index - 1].naddress
        }
      } else {
        this.queryParams.naddress = this.mNameOptions[0].naddress
      }
      this.listPigdata()
    },
    handleBig() {
      if (this.queryParams.naddress) {
        let index = this.mNameOptions.findIndex(
            (item) => item.naddress == this.queryParams.naddress
        )
        if (index < this.mNameOptions.length - 1) {
          this.queryParams.naddress = this.mNameOptions[index + 1].naddress
        }
      } else {
        this.queryParams.naddress =
            this.mNameOptions[this.mNameOptions.length - 1].naddress
      }
      this.listPigdata()
    },
    handleSmallRfid() {
      if (this.queryParams.mrfid) {
        let index = this.mrfidOptions.findIndex(
            (item) => item.mrfid == this.queryParams.mrfid
        )
        if (index > 0) {
          this.queryParams.mrfid = this.mrfidOptions[index - 1].mrfid
        }
      } else {
        this.queryParams.mrfid = this.mrfidOptions[0].mrfid
      }
      this.queryParams.mid = null
      this.getPigDataByMid()
    },
    handleBigRfid() {
      if (this.queryParams.mrfid) {
        let index = this.mrfidOptions.findIndex(
            (item) => item.mrfid == this.queryParams.mrfid
        )
        if (index < this.mrfidOptions.length - 1) {
          this.queryParams.mrfid = this.mrfidOptions[index + 1].mrfid
        }
      } else {
        this.queryParams.mrfid =
            this.mrfidOptions[this.mrfidOptions.length - 1].mrfid
      }
      this.queryParams.mid = null
      this.getPigDataByMid()
    },
    handleSmallMid() {
      if (this.queryParams.mid) {
        let index = this.mrfidOptions.findIndex(
            (item) => item.mid == this.queryParams.mid
        )
        if (index > 0) {
          this.queryParams.mid = this.mrfidOptions[index - 1].mid
        }
      } else {
        this.queryParams.mid = this.mrfidOptions[0].mid
      }
      this.queryParams.mrfid = null
      this.getPigDataByMid()
    },
    handleBigMid() {
      if (this.queryParams.mid) {
        let index = this.mrfidOptions.findIndex(
            (item) => item.mid == this.queryParams.mid
        )
        if (index < this.mrfidOptions.length - 1) {
          this.queryParams.mid = this.mrfidOptions[index + 1].mid
        }
      } else {
        this.queryParams.mid =
            this.mrfidOptions[this.mrfidOptions.length - 1].mid
      }
      this.queryParams.mrfid = null
      this.getPigDataByMid()
    },
    handleMrfid() {
      this.queryParams.pageNum = 1
      this.queryParams.mid = null
      this.getPigDataByMid()
    },
    handleMid() {
      this.queryParams.pageNum = 1
      this.queryParams.mrfid = null
      this.getPigDataByMid()
    },
    handleClick(tab, event) {
      this.getList()
      this.$nextTick(() => {
        this.getPigDataByMid()
        this.category23.resize()
      })
    },

    Subtr(arg1, arg2) {
      var r1, r2, m, n
      try {
        r1 = arg1.toString().split('.')[1].length
      } catch (e) {
        r1 = 0
      }
      try {
        r2 = arg2.toString().split('.')[1].length
      } catch (e) {
        r2 = 0
      }
      m = Math.pow(10, Math.max(r1, r2))
      n = r1 >= r2 ? r1 : r2
      return ((arg1 * m - arg2 * m) / m).toFixed(n)
    },

    /** 查询【个体数据】列表 */
    getPigDataByMid() {

      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm
      if (
          this.queryParams &&
          (this.queryParams.mid || this.queryParams.mrfid)
      ) {
        listPigdatadaily({
          mfactory: this.$store.state.settings.nowPigFarm,
          mid: this.queryParams.mid,
          mrfid: this.queryParams.mrfid,
          naddress: this.queryParams.naddress
          // nindex: this.queryParams.nindex,
        }).then((response) => {
          if (response.rows[0] == null) {
            alert(
                this.queryParams.mid
                    ? this.$t('columnSystem.mid') +
                    this.queryParams.mid +
                    this.$t('columnSystem.noRecords')
                    : this.$t('columnSystem.mrfid') +
                    this.queryParams.mrfid +
                    this.$t('columnSystem.noRecords')
            )
          } else {
            this.pigdata = response.rows[0]

            this.getList()

            this.xWeight = []
            this.yWeight = []
            listDailyweightday(
                this.addDateRangeRe(
                    {
                      mfactory: this.$store.state.settings.nowPigFarm,
                      mid: this.pigdata.mid,
                      naddress: this.queryParams.naddress
                    },
                    this.queryParams.dateRange
                )
            ).then((response) => {
              if (response && !isEmpty(response.rows)) {
                this.measuredayList = response.rows
                this.total = response.total

                response.rows.forEach((item) => {

                  this.xWeight.push(item.ndate)
                  this.yWeight.push(item.nweight)

                })
                this.xWeight.reverse()
                this.yWeight.reverse()
                this.heightWeight = this.yWeight[this.xWeight.length - 1] || 0
                this.lowWeight = this.yWeight[0] || 0
                const date1 = new Date(this.xWeight[0])
                const date2 = new Date(this.xWeight[this.xWeight.length - 1])
                this.measureDays = getDateDifferenceInDays(date1, date2)
                this.birage = getDateDifferenceInDays(new Date(this.pigdata.ddate), date2) + this.pigdata.nrlage  //日龄
                this.getWeightDatas()

              }
            })

          }
        })
      } else {
        this.msgInfo(this.$t('columnSystem.pleaseEnterColumnFirst'))
      }
    },
    /** 查询【测定日报告】列表 */
    getList() {
      this.loading = true
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm
      listDailyweightday(
          this.addDateRangeRe(
              {
                naddress: this.queryParams.naddress,
                mfactory: this.$store.state.settings.nowPigFarm,
                mid: this.pigdata.mid,
                pageNum: this.queryParams.pageNum,
                pageSize: this.queryParams.pageSize
              },
              this.queryParams.dateRange
          )
      ).then((response) => {
        if (response && !isEmpty(response.rows)) {
          this.measuredayList = response.rows
          this.total = response.total
          this.loading = false
          this.recordDays = response.total

          //计算记录天数
          // var totay = new Date();
          // var beginDay = new Date(this.queryParams.datefrom);
          // var endDay = null;
          // if (this.pigdata.ntype != 0) endDay = totay;
          // else endDay = new Date(this.pigdata.denddate);

          // this.measureDays = getDaysDiffBetweenDates(
          //   new Date(formatDay(beginDay)),
          //   new Date(formatDay(endDay))
          // );
          // this.measureDays = response.total - 1;
          // this.lowWeight = response.rows[response.total - 1].nweight;
          // this.heightWeight = response.rows[0].nweight;
        } else {
          this.msgInfo(this.$t('columnSystem.noData'))
          this.measuredayList = []
          this.recordDays = null
          this.measureDays = null
          this.lowWeight = null
          this.heightWeight = null
        }

        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        ningestions: null,
        nfeednum: null,
        nseconds: null,
        nweight: null,
        //测定天数
        measureDays: null,
        //记录天数
        recordDays: null,
        //总采食量
        lowWeight: null,
        //最高体重
        heightWeight: null,

        pigdata: null,
        //采食echart
        category22: null,
        //体重echart
        category23: null,
        category24: null,
        //采食x轴数据
        xDataFood: [],
        //采食y轴数据
        yDataFood: [],
        //体重x轴数据
        xWeight: [],
        //体重y轴数据
        yWeight: [],
        //foodEchart
        foodEchartList: [],
        //weightEchart
        weightEchartList: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      // this.getList();
      this.getPigDataByMid()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
      // listPigdata({
      //   mfactory: this.$store.state.settings.nowPigFarm,
      // }).then((response) => {
      //   this.mrfidOptions = response.rows;
      // });
    },
    getWeightDatas() {
      if (
          this.category23 != null &&
          this.category23 != '' &&
          this.category23 != undefined
      ) {
        this.category23.dispose() //销毁
      }
      this.category23 = this.$refs.category23
          ? echarts.init(this.$refs.category23, 'walden')
          : ''

      this.category23 &&
      this.category23.setOption({
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: [this.$t('columnSystem.weightTab')]
        },
        grid: {
          left: '4%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          axisTick: {
            alignWithLabel: true
          },
          boundaryGap: false,
          data: this.xWeight
        },
        yAxis: {
          type: 'value',
          maxInterval: 5
        },
        series: [
          {
            // symbol: "none", //取消折点圆圈
            name: this.$t('columnSystem.weight'),
            type: 'line',
            // stack: "体重",
            data: this.yWeight,
            label: { // 配置数值标签
              show: true, // 显示标签
              position: 'top', // 标签位置（top/inside/outside等）
              fontSize: 12, // 字体大小
              formatter: '{c}' // {c} 代表数据值
            }
          }
        ]
      })
    }
  }
}
</script>
