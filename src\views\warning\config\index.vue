<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item label="猪场id" prop="faid">
        <el-input
          v-model="queryParams.faid"
          placeholder="请输入猪场id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="监控节点id" prop="sta">
        <el-input
          v-model="queryParams.sta"
          placeholder="请输入监控节点id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监控节点名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入监控节点名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网关id" prop="swid">
        <el-input
          v-model="queryParams.swid"
          placeholder="请输入网关id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- <el-form-item label="通信状态" prop="net">
        <el-select
          v-model="queryParams.net"
          placeholder="请选择通信状态"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="dict in slavenetworkList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['warning:config:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['warning:config:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['warning:config:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['warning:config:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <auto-refresh
          :interval="dataInterval"
          :page-active="isPageActive"
          @refresh="getList"
          @refresh-change="handleRefreshChange"
        />
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- v-loading="loading" -->
    <el-table
      :data="alarmList"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="序号" width="55" align="center" prop="id" /> -->
      <el-table-column label="监控节点id" align="center" prop="sta" />
      <el-table-column
        label="监控节点名称"
        width="255"
        align="center"
        prop="name"
        sortable="custom"
      />
      <el-table-column label="网关id" align="center" prop="swid" />
      <el-table-column label="断电" align="center" prop="dd">
        <template slot-scope="scope">
          <el-tag
            v-if="handleIsOnline(scope.row.uptime) > 30"
            effect="dark"
            type="info"
            >离线</el-tag
          >
          <el-tag
            v-else
            effect="dark"
            :type="scope.row.dd == 0 ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.dd == 0 ? "正常" : "异常" }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="缺项" align="center" prop="qx">
        <template slot-scope="scope">
          <el-tag
            v-if="handleIsOnline(scope.row.uptime) > 30"
            effect="dark"
            type="info"
            >离线</el-tag
          >
          <el-tag
            v-else
            effect="dark"
            :type="scope.row.qx == 0 ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.qx == 0 ? "正常" : "异常" }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="环境异常" align="center" prop="hj">
        <template slot-scope="scope">
          <el-tag
            v-if="handleIsOnline(scope.row.uptime) > 30"
            effect="dark"
            type="info"
            >离线</el-tag
          >
          <el-tag
            v-else
            effect="dark"
            :type="scope.row.hj == 0 ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.hj == 0 ? "正常" : "异常" }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="通信状态" align="center" prop="net">
        <template slot-scope="scope">
          <el-tag
            v-if="handleIsOnline(scope.row.uptime) > 30"
            effect="dark"
            type="info"
            >离线</el-tag
          >
          <el-tag
            v-else
            effect="dark"
            :type="scope.row.net === 'ok' ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.net === "ok" ? "在线" : "离线" }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        label="上传时间"
        align="center"
        prop="uptime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.uptime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['warning:config:edit']"
            >修改</el-button
          >

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['warning:config:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->

    <!-- 添加或修改【配置】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="98px">
        <!-- <el-form-item label="猪场id" prop="faid">
          <el-input v-model="form.faid" placeholder="请输入猪场id" />
        </el-form-item> -->
        <el-form-item label="监控节点id" prop="sta">
          <el-input v-model="form.sta" placeholder="请输入监控节点id" />
        </el-form-item>
        <el-form-item label="监控节点名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入监控节点名称" />
        </el-form-item>
        <el-form-item label="网关id" prop="swid">
          <el-input v-model="form.swid" placeholder="请网关id" />
        </el-form-item>
        <!-- <el-form-item label="断电" prop="dd">
          <el-input v-model="form.dd" placeholder="请输入断电" />
        </el-form-item>
        <el-form-item label="缺项" prop="qx">
          <el-input v-model="form.qx" placeholder="请输入缺项" />
        </el-form-item>
        <el-form-item label="环境异常" prop="hj">
          <el-input v-model="form.hj" placeholder="请输入环境异常" />
        </el-form-item> -->
        <!-- <el-form-item label="缺相位置id" prop="colStr1">
          <el-input v-model="form.colStr1" placeholder="请输入缺相位置id" />
        </el-form-item>
        <el-form-item label="缺相位置id" prop="colStr2">
          <el-input v-model="form.colStr2" placeholder="请输入缺相位置id" />
        </el-form-item> -->
        <!-- <el-form-item label="通信状态" prop="net">
          <el-select
            v-model="form.net"
            placeholder="请选择通信状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in slavenetworkList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传时间" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.uptime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择上传时间"
          >
          </el-date-picker>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAlarm,
  getAlarm,
  delAlarm,
  addAlarm,
  updateAlarm,
  exportAlarm,
} from "@/api/warning/config";

import { handleTimeErrorLimit } from "@/utils";
import AutoRefresh from "@/components/AutoRefresh";

export default {
  name: "Config",
  components: {
    AutoRefresh,
  },
  data() {
    return {
      //设备状态
      slavenetworkList: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      //轮询时间
      dataInterval: 10 * 1000,
      // 页面是否激活
      isPageActive: true,
      // 总条数
      total: 0,
      // 【配置】表格数据
      alarmList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        orderByColumn: "",
        isAsc: "",
        faid: null,
        sta: null,
        name: null,
        swid: null,
        dd: null,
        qx: null,
        hj: null,
        colStr1: null,
        colStr2: null,
        net: null,
      },
      sortParams: {
        field: "",
        order: "",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  watch: {
    queryParams: {
      handler(newVal, oldVal) {
        // 避免初始化时触发，只有在真正变化时才查询
        if (oldVal && this.isPageActive) {
          this.getList();
        }
      },
      deep: true,
    },
  },
  created() {
    // 设置页面激活状态
    this.isPageActive = true;

    // 获取字典数据
    this.getDicts("hk_work_status").then((response) => {
      this.slavenetworkList = response.data;
    });
  },
  mounted() {
    // 页面挂载完成后进行首次查询
    this.getList();
  },
  beforeDestroy() {
    // 页面销毁时清除通知
    this.isPageActive = false;
    this.$notify.closeAll();
  },
  activated() {
    // 页面激活时
    this.isPageActive = true;
  },
  deactivated() {
    // 页面失活时清除通知
    this.isPageActive = false;
    this.$notify.closeAll();
  },
  methods: {
    handleIsOnline(a) {
      return handleTimeErrorLimit(a);
    },
    // getList() { }

    /** 查询【配置】列表 */
    getList() {
      // 防止重复请求
      if (this.loading) {
        console.log("正在加载中，跳过重复请求");
        return;
      }

      console.log("开始查询列表");
      this.loading = true;
      this.queryParams.faid = this.$store.state.settings.nowPigFarm;
      listAlarm(this.queryParams)
        .then((response) => {
          this.alarmList = response.rows;
          this.total = response.total;
          this.loading = false;
          // 只在页面激活时显示异常通知
          if (Object.keys(response.rows).length !== 0 && this.isPageActive) {
            this.$notify.closeAll(); // 清除所有旧通知

            // 筛选异常设备
            const errorDevices = response.rows.filter(
              (device) =>
                device.dd !== 0 ||
                device.qx !== 0 ||
                device.hj !== 0 ||
                device.net !== "ok" ||
                this.handleIsOnline(device.uptime) > 30
            );

            if (errorDevices.length > 0) {
              // 拼接所有异常设备名称（换行显示）
              const errorMessage = `
  <div style="max-height: 600px; overflow-y: auto;">
    ${errorDevices.map((device) => device.name).join("<br>")}
  </div>
`;

              this.$notify({
                title: `共有 ${errorDevices.length} 个监控结点异常`,
                message: errorMessage,
                dangerouslyUseHTMLString: true, // 允许 HTML
                type: "warning",
                duration: 300000, // 5分钟后关闭
                offset: 70, // 调整位置
                customClass: "custom-notification",
                // customClass: "my-notify", // 自定义样式（可选）
              });
            }
          }
          console.log("查询完成");
        })
        .catch((error) => {
          console.error("查询失败:", error);
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        faid: null,
        sta: null,
        name: null,
        swid: null,
        dd: null,
        qx: null,
        hj: null,
        colStr1: null,
        colStr2: null,
        net: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【配置】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAlarm(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【配置】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAlarm(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.faid = this.$store.state.settings.nowPigFarm;
            addAlarm(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【配置】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delAlarm(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【配置】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportAlarm(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    // 处理自动刷新状态变化
    handleRefreshChange(isActive) {
      console.log("自动刷新状态变化:", isActive);
    },
    // 处理排序变化
    handleSortChange({ column, prop, order }) {
      // 转换排序参数格式
      this.queryParams.orderByColumn = prop;
      this.queryParams.isAsc = order === "ascending" ? "asc" : "desc";

      // 重新获取数据
      this.getList();
    },
  },
};
</script>
<style lang="scss">
.custom-notification.el-notification__content {
  padding-right: 16px !important;
}

.custom-notification.el-scrollbar__bar.is-vertical {
  right: 6px !important;
  background-color: #909399 !important;
}

.el-notification.custom-notification {
  width: 200px;
}
</style>
