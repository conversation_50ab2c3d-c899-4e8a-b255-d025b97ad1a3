<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="猪场名" prop="facname">
        <el-input
          v-model="queryParams.facname"
          placeholder="请输入猪场名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="厂名" prop="colName1">
        <el-input
          v-model="queryParams.colName1"
          placeholder="请输入厂名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="经度" prop="lng">
        <el-input
          v-model="queryParams.lng"
          placeholder="请输入经度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="纬度" prop="lat">
        <el-input
          v-model="queryParams.lat"
          placeholder="请输入纬度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="厂名" prop="routeurl">
        <el-input
          v-model="queryParams.routeurl"
          placeholder="请输入厂名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="地址" prop="text">
        <el-input
          v-model="queryParams.text"
          placeholder="请输入地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择猪场状态">
          <el-option
            v-for="dict in facWorkStatus"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="规模" prop="scale">
        <el-input
          v-model="queryParams.scale"
          placeholder="请输入规模"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- 1种猪，2母猪，3分栏4环控5料塔，6精准饲喂 -->
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择猪场类型">
          <el-option
            v-for="dict in facTypeList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:factory:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:factory:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:factory:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:factory:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="factoryList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="猪场id" align="center" prop="id" />
      <el-table-column label="猪场名" align="center" prop="facname" />
      <!-- <el-table-column label="厂名" align="center" prop="colName1" /> -->
      <el-table-column label="经度" align="center" prop="lng" />
      <el-table-column label="纬度" align="center" prop="lat" />
      <!-- <el-table-column label="厂名" align="center" prop="routeurl" /> -->
      <el-table-column label="地址" align="center" prop="text" />
      <el-table-column
        label="状态"
        align="center"
        prop="status"
        :formatter="statusFormat"
      />
      <el-table-column label="规模" align="center" prop="scale" />
      <el-table-column
        label="类型"
        align="center"
        prop="type"
        :formatter="typeFormat"
      />
      <el-table-column label="当前额度" align="center" prop="phonenum" />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:factory:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:factory:remove']"
            >删除</el-button
          >

          <el-button
            size="mini"
            type="text"
            icon="el-icon-shopping-cart-2"
            @click="handleRecharge(scope.row)"
            v-hasPermi="['system:factory:recharge']"
            >充值</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【猪场】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="猪场名" prop="facname">
          <el-input v-model="form.facname" placeholder="请输入猪场名" />
        </el-form-item>
        <el-form-item label="猪场id" prop="id">
          <el-input v-model="form.id" placeholder="请输入猪场id" />
        </el-form-item>
        <!-- <el-form-item label="厂名" prop="colName1">
          <el-input v-model="form.colName1" placeholder="请输入厂名" />
        </el-form-item> -->
        <el-form-item label="地址" prop="text">
          <el-input
            v-model="form.text"
            placeholder="请输入地址"
            @change="handleAddressQuery($event)"
          />
        </el-form-item>
        <el-form-item label="经度" prop="lng">
          <el-input v-model="form.lng" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="lat">
          <el-input v-model="form.lat" placeholder="请输入纬度" />
        </el-form-item>
        <!-- <el-form-item label="厂名" prop="routeurl">
          <el-input v-model="form.routeurl" placeholder="请输入厂名" />
        </el-form-item> -->

        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择猪场状态">
            <el-option
              v-for="dict in facWorkStatus"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规模" prop="scale">
          <el-input v-model="form.scale" placeholder="请输入规模" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择猪场类型">
            <el-option
              v-for="dict in facTypeList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="跳转路由" prop="routeurl">
          <el-input v-model="form.routeurl" placeholder="请输入跳转路由" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="recharge_title"
      :visible.sync="recharge_open"
      width="500px"
      append-to-body
    >
      <el-form ref="rechargeForm" :model="rechargeForm" label-width="80px">
        <el-form-item label="当前额度" prop="addpre">
          <el-input v-model="rechargeForm.addpre" disabled />
        </el-form-item>
        <el-form-item label="充值额度" prop="addnum">
          <el-input
            type="number"
            v-model="rechargeForm.addnum"
            placeholder="请输入充值额度"
          />
        </el-form-item>
        <el-form-item label="备注" prop="tag">
          <el-input v-model="rechargeForm.tag" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRechargeForm">确 定</el-button>
        <el-button @click="cancelRechargeForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listFactory,
  getFactory,
  delFactory,
  addFactory,
  updateFactory,
  exportFactory,
} from "@/api/system/factory";

import { addFactoryRecharge } from "@/api/alarm/recharge";

import axios from "axios";
import { formatDate } from "@/utils";

export default {
  name: "Factory",
  components: {},
  data() {
    return {
      facWorkStatus: [],
      facTypeList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【猪场】表格数据
      factoryList: [],
      // 弹出层标题
      title: "",
      addOrEdlit: true,
      // 是否显示弹出层
      open: false,
      recharge_title: "充值额度",
      recharge_open: false,
      rechargeForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        facname: null,
        colName1: null,
        lng: null,
        lat: null,
        routeurl: null,
        text: null,
        status: null,
        scale: null,
        type: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        facname: [
          { required: true, message: "猪场名不能为空", trigger: "blur" },
        ],
        lng: [{ required: true, message: "经度不能为空", trigger: "blur" }],
        lat: [{ required: true, message: "纬度不能为空", trigger: "blur" }],
        text: [{ required: true, message: "地址不能为空", trigger: "blur" }],
        status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
        type: [{ required: true, message: "类型不能为空", trigger: "blur" }],
        scale: [{ required: true, message: "规模不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    // this.setPlace();
    this.getList();
    this.getDicts("fac_work_status").then((response) => {
      this.facWorkStatus = response.data;
    });
    this.getDicts("fac_type").then((response) => {
      this.facTypeList = response.data;
    });
  },
  methods: {
    handleAddressQuery(e) {
      let url = process.env.VUE_APP_BASE_API_MAP;

      axios({
        method: "get",
        url,
        params: {
          address: e,
          output: "json",
          ak: "u5DR6jX9tnmTfl4SgRnPNZGU3kNlgz3G",
        },
      }).then((res) => {
        if (res && res.data.status !== 1) {
          this.form.lng = res.data.result.location.lng;
          this.form.lat = res.data.result.location.lat;
        } else {
          this.msgError("请检查你输入的地址!");
        }
      });
    },
    // setPlace() {
    //   let url = process.env.VUE_APP_BASE_API_MAP;
    //   axios({
    //     method: "get",
    //     url,
    //     params: {
    //       address: "北京市海淀区",
    //       output: "json",
    //       ak: "u5DR6jX9tnmTfl4SgRnPNZGU3kNlgz3G",
    //     },
    //   }).then((res) => {
    //   });

    //   // this.$jsonp("https://api.map.baidu.com/geocoding/v3/", {
    //   //   address: "北京市海淀区上地十街10号",
    //   //   // output: 'jsonp',
    //   //   ak: 'u5DR6jX9tnmTfl4SgRnPNZGU3kNlgz3G',
    //   // })
    //   //   .then((json) => {
    //   //     // 得到我们想要的内容
    //   //   })
    //   //   .catch((err) => {
    //   //     console.log(err);
    //   //   });
    //   // map.clearOverlays();    //清除地图上所有覆盖物
    //   // function myFun() {
    //   //   var pp = local.getResults().getPoi(0).point; //获取第一个智能搜索的结果
    //   //   console.log("经度：" + pp.lng, "纬度：" + pp.lat);
    //   //   // map.centerAndZoom(pp, 18);
    //   //   // map.addOverlay(new BMap.Marker(pp));    //添加标注
    //   // }
    //   // var local = new BMap.LocalSearch(map, {
    //   //   //智能搜索
    //   //   onSearchComplete: myFun,
    //   // });
    //   // local.search("浙江");
    // },
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.facWorkStatus, row.status);
    },
    typeFormat(row, column) {
      return this.selectDictLabel(this.facTypeList, row.type);
    },
    /** 查询【猪场】列表 */
    getList() {
      this.loading = true;
      listFactory(this.queryParams).then((response) => {
        this.factoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelRechargeForm() {
      this.recharge_open = false;
      this.resetForm("rechargeForm");
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        facname: null,
        colName1: null,
        lng: null,
        lat: null,
        routeurl: "/overview/farmBoard",
        text: null,
        status: 1,
        scale: null,
        type: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【猪场】";
      this.addOrEdlit = true;
    },
    handleRecharge(row) {
      this.recharge_open = true;
      this.rechargeForm.addpre = row.phonenum || 0;
      this.rechargeForm.mfactory = row.id;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getFactory(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【猪场】";
        this.addOrEdlit = false;
      });
    },
    /** 提交按钮 */
    submitRechargeForm() {
      this.$refs["rechargeForm"].validate((valid) => {
        if (valid) {
          /**接口 */
          // this.rechargeForm.addtime = formatDate(new Date());
          console.log("this.rechargeForm", this.rechargeForm);
          addFactoryRecharge(this.rechargeForm).then((response) => {
            this.msgSuccess("充值成功");
            this.recharge_open = false;
            this.getList();
          });
        }
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (!this.addOrEdlit) {
            updateFactory(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFactory(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【猪场】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delFactory(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【猪场】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportFactory(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>