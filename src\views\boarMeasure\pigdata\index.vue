<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('boarMeasure.mensurationNow')" prop="checked">
        <el-checkbox v-model="queryParams.checked" />
      </el-form-item>
      <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
        <el-input
          v-model="queryParams.mid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('boarMeasure.mrfid')" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item :label="$t('boarMeasure.dbirthdate')" prop="dbirthdate">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.dbirthdate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择出生日期"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item :label="$t('boarMeasure.mdorm')" prop="mdorm">
        <el-select
          v-model="queryParams.mdorm"
          :placeholder="$t('common.pleaseChoose')"
        >
          <el-option
            v-for="item in mdormOptions"
            :key="item.mdorm"
            :label="item.mdorm"
            :value="item.mdorm"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
        <el-select
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseChoose')"
        >
          <el-option
            v-for="item in nIndexOptions"
            :key="item.nindex"
            :label="item.nindex"
            :value="item.nindex"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['boarMeasure:pigdata:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['boarMeasure:pigdata:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['boarMeasure:pigdata:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['boarMeasure:pigdata:import']"
          >{{ $t("common.batchUpdate") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['boarMeasure:pigdata:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-set-up"
          size="mini"
          @click="handleOut"
          v-hasPermi="['boarMeasure:pigdata:lanout']"
          >{{ $t("boarMeasure.mensurationEnd") }}</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="cyan"
          icon="el-icon-setting"
          size="mini"
          @click="handleEditDate"
          v-hasPermi="['boarMeasure:pigdata:editDate']"
          >批量修改测定日期</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="pigdataList"
      @selection-change="handleSelectionChange"
      :cell-style="{ padding: '0' }"
    >
      <!-- <el-table-column label="日期" width="180">
        <template scope="scope">
          <el-input size="small" v-model="scope.row.mrfid" placeholder="请输入内容" @change="handleEdit(scope.$index, scope.row)"></el-input>
          <span>{{scope.row.mrfid}}</span>
        </template>
      </el-table-column> -->
      <el-table-column type="selection" width="50" align="center" />
      <!-- <el-table-column label="序号" align="center" prop="indexn" width="50" contenteditable="true"/> -->
      <el-table-column
        :label="$t('boarMeasure.mid')"
        align="center"
        prop="mid"
        width="180"
        sortable
      />
      <el-table-column
        :label="$t('boarMeasure.mrfid')"
        align="center"
        prop="mrfid"
        width="150"
        sortable
      />
      <!-- <el-table-column
        :label="$t('boarMeasure.dbirthdate')"
        align="center"
        prop="dbirthdate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dbirthdate) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('boarMeasure.mvariety')"
        align="center"
        prop="mvariety"
      />
      <el-table-column
        :label="$t('boarMeasure.bgender')"
        align="center"
        prop="bgender"
        :formatter="valueGenderFormat"
        width="50"
      />
      <el-table-column
        :label="$t('boarMeasure.mdorm')"
        align="center"
        prop="mdorm"
        width="85"
        sortable
      />
      <el-table-column
        :label="$t('boarMeasure.nindex')"
        align="center"
        prop="nindex"
        width="75"
        sortable
      />

      <!-- <el-table-column :label="$t('boarMeasure.mname')" align="center" prop="mname" />
      <el-table-column :label="$t('boarMeasure.naddress')" align="center" prop="naddress" /> -->
      <el-table-column
        :label="$t('boarMeasure.dstartdate')"
        align="center"
        prop="dstartdate"
        width="125"
        sortable
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dstartdate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.denddate')"
        align="center"
        prop="denddate"
        width="125"
        sortable
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.denddate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.boarrlage')"
        align="center"
        prop="nrlage"
        width="75"
        sortable
      />
      <el-table-column
        :label="$t('boarMeasure.nweightStart')"
        align="right"
        prop="nweight1"
        sortable
        ><template slot-scope="scope">
          <span>{{ scope.row.nweight1 | formatNumber(1) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.nweightEnd')"
        align="right"
        prop="nweight2"
        sortable
        ><template slot-scope="scope">
          <span>{{ scope.row.nweight2 | formatNumber(1) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="测定日期" align="center" prop="ddate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ddate) }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('boarMeasure.nweight')"
        align="right"
        prop="nweight"
        sortable
        ><template slot-scope="scope">
          <span>{{ scope.row.nweight | formatNumber(1) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.ntype')"
        align="center"
        prop="ntype"
        width="80"
      >
        <template slot-scope="scope">
          <el-tag
            effect="dark"
            :type="
              scope.row.ntype && scope.row.ntype === 1 ? 'success' : 'danger'
            "
            disable-transitions
          >
            <!-- {{
              (scope.row.ntype === 1 || scope.row.ntype === 0) &&
              typeList &&
              typeList[scope.row.ntype].dictLabel
            }} -->
            {{
              scope.row.ntype === 1
                ? $t("boarMeasure.mensurationNow")
                : $t("boarMeasure.mensurationEnd")
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.remarks')"
        align="center"
        prop="remarks"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        width="100"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['boarMeasure:pigdata:edit']"
          />
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['boarMeasure:pigdata:remove']"
          />
          <el-button
            size="mini"
            type="text"
            icon="el-icon-price-tag"
            @click="handleChangeMrfid(scope.row)"
            v-hasPermi="['boarMeasure:pigdata:changemrfid']"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【种猪信息】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
          <el-input
            v-model="form.mid"
            disabled
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mrfid')" prop="mrfid">
          <el-input
            v-model="form.mrfid"
            disabled
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mvariety')" prop="mvariety">
          <!-- <el-select v-model="form.ntype" placeholder="请选择品种">
            <el-option
              v-for="dict in varietyList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictLabel"
            ></el-option>
          </el-select> -->
          <el-input
            v-model="form.mvariety"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.bgender')" prop="bgender">
          <el-select
            v-model="form.bgender"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in genderList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
          <!-- <el-input v-model="form.bGender" placeholder="请输入性别" /> -->
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.dbirthdate')" prop="dbirthdate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.dbirthdate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mdorm')" prop="mdorm">
          <el-select
            v-model="form.mdorm"
            :placeholder="$t('common.pleaseChoose')"
            clearable
            size="small"
            @change="changeMethods($event)"
          >
            <el-option
              v-for="item in mdormOptions"
              :key="item.mdorm"
              :label="item.mdorm"
              :value="item.mdorm"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
          <el-select
            v-model="form.nindex"
            :placeholder="$t('common.pleaseChoose')"
            clearable
            size="small"
          >
            <el-option
              v-for="item in nIndexOptions"
              :key="item.nindex"
              :label="item.nindex"
              :value="item.nindex"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mname')" prop="mname">
          <el-input
            v-model="form.mname"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.naddress')" prop="naddress">
          <el-input
            v-model="form.naddress"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.boarrlage')" prop="nrlage">
          <el-input
            v-model="form.nrlage"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.dstartdate')" prop="dstartdate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.dstartdate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.denddate')" prop="denddate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.denddate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nweightStart')" prop="nweight1">
          <el-input
            v-model="form.nweight1"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nweightEnd')" prop="nweight2">
          <el-input
            v-model="form.nweight2"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <!-- <el-form-item label="测定日期" prop="ddate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ddate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择测定日期"
          >
          </el-date-picker>
        </el-form-item> -->
        <el-form-item :label="$t('boarMeasure.nweight')" prop="nweight">
          <el-input
            v-model="form.nweight"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.ntype')" prop="ntype">
          <el-select
            v-model="form.ntype"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in typeList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.remarks')" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="titleLan"
      :visible.sync="openLan"
      width="500px"
      append-to-body
    >
      <div class="head-container">
        <el-tree
          show-checkbox
          :data="deptOptions"
          :props="defaultProps"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          ref="tree"
          default-expand-all
          @node-click="handleNodeClick"
          node-key="nIndex"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleLanOut">{{
          $t("boarMeasure.mensurationEnd")
        }}</el-button>
        <el-button @click="cancelLan">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="titleMrfid"
      :visible.sync="openMrfid"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formMrfid"
        :model="formMrfid"
        :rules="rulesMrfid"
        label-width="150px"
      >
        <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
          <el-input
            v-model="formMrfid.mid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mrfid')" prop="mrfid">
          <el-input
            v-model="formMrfid.mrfid"
            disabled
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.newMrfid')" prop="newMrfid">
          <el-input
            v-model="formMrfid.newMrfid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormMrfid">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancelFormMrfid">{{
          $t("common.cancel")
        }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="
          upload.url + '?mfactory=' + this.$store.state.settings.nowPigFarm
        "
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          {{ $t("boarMeasure.dragFile") }}
          <em>{{ $t("boarMeasure.clickUpload") }}</em>
        </div>
        <!-- <div class="el-upload__tip" slot="tip">
          <el-checkbox
            v-model="upload.updateSupport"
          />是否更新已经存在的用户数据
          <el-link type="info" style="font-size: 12px" @click="importTemplate"
            >下载模板</el-link
          >
        </div> -->
        <div class="el-upload__tip" style="color: red" slot="tip">
          {{ $t("boarMeasure.hintOnly") }}
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="upload.open = false">{{
          $t("common.cancel")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPigdata,
  getPigdata,
  delPigdata,
  addPigdata,
  updatePigdata,
  exportPigdata,
  outLan,
  changemrfid,
  importTemplate, //还没有
} from "@/api/system/pigdata";
import { getToken } from "@/utils/auth";
import { treeselect } from "@/api/system/control";
import Treeselect from "@riophae/vue-treeselect";
import { listControl } from "@/api/system/control";
// import { isNil } from "lodash";

export default {
  name: "Pigdata",
  components: {},
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "label",
      },
      //测定状态
      typeList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【种猪信息】表格数据
      genderList: [],
      pigdataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      titleLan: this.$t("boarMeasure.mensurationEnd"),
      openLan: false,
      // 猪场树选项
      deptOptions: undefined,
      lan: [],
      formMrfid: {},
      openMrfid: false,
      titleMrfid: "",
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/pigdata/importData", //还没有
      },
      // 查询参数
      queryParams: {
        checked: true,
        pageNum: 1,
        pageSize: 200,
        mid: null,
        mrfid: null,
        dbirthdate: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        dstartdate: null,
        nrlage: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        ddate: null,
        nweight: null,
        ntype: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // mid: [{ required: true, message: "耳缺号不能为空", trigger: "blur" }],
      },
      rulesMrfid: {
        newMrfid: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
      },
      mdormOptions: [],
      nIndexOptions: [],
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  created() {
    //查询猪舍号
    listControl({
      ntype: 1,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.mdormOptions = response.rows;
    });
    //查询栏号
    listControl({
      ntype: 2,
      // mdorm:this.form.mdorm,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.nIndexOptions = response.rows;
    });
    this.getDicts("sys_cd_status").then((response) => {
      this.typeList = response.data;
    });
    this.getDicts("sys_gender_type").then((response) => {
      this.genderList = response.data;
    });
    this.getList();
    this.getTreeselect();
  },
  methods: {
    // @cell-click="celledit"
    // celledit(row, column, cell, event) {
    //   cell.contentEditable = true;
    //   cell.focus();
    // },
    // 字典状态字典翻译
    valueGenderFormat(row, column) {
      return this.selectDictLabel(this.genderList, row.bgender);
    },
    changeMethods(value) {
      listControl({
        ntype: 2,
        mdorm: value && value,
        mfactory: this.$store.state.settings.nowPigFarm,
      }).then((response) => {
        this.nIndexOptions = response.rows;
      });
      this.form.nindex = null;
    },
    /** 查询【种猪信息】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      this.queryParams.ntype = this.queryParams.checked ? "1" : "0";
      listPigdata(this.queryParams).then((response) => {
        this.pigdataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        mid: null,
        mrfid: null,
        dbirthdate: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nrlage: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        ddate: null,
        nweight: null,
        ntype: null,
      };
      this.resetForm("form");
    },

    /** 查询猪舍结构树 */
    getTreeselect() {
      // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      treeselect({ mfactory: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          response.data.forEach((item) => {
            item.children &&
              item.children.forEach((i) => {
                this.lan.push(i.nIndex);
                if (i.children) {
                  delete i.children;
                }
              });
          });
          this.deptOptions = response.data;
        }
      );
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      // this.queryParams.pageNum = 1;
      // this.queryParams.indexn = data.id;
      // this.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    //  handleEdit(index, row) {
    //     console.log(index, row);
    //   },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("boarMeasure.addPigdata");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // console.log("this.pigdataList",this.pigdataList)
      this.reset();
      const indexn = row.indexn || this.ids;
      getPigdata(indexn).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("boarMeasure.updatePigdata");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.mfactory = this.$store.state.settings.nowPigFarm;
          if (this.form.indexn != null) {
            updatePigdata(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            addPigdata(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleChangeMrfid(row) {
      const indexn = row.indexn;
      getPigdata(indexn).then((response) => {
        this.formMrfid = response.data;
        this.openMrfid = true;
        this.titleMrfid = this.$t("boarMeasure.changeMrfid");
      });
    },
    submitFormMrfid() {
      this.$refs["formMrfid"].validate((valid) => {
        if (valid) {
          if (this.formMrfid.indexn != null) {
            changemrfid({
              indexn: this.formMrfid.indexn,
              mid: this.formMrfid.mid,
              newMrfid: this.formMrfid.newMrfid,
              mfactory: this.$store.state.settings.nowPigFarm,
            }).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.openMrfid = false;
              this.getList();
            });
          }
        }
      });
    },
    cancelFormMrfid() {
      this.openMrfid = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        this.$t("boarMeasure.sureCancelPigdata") +
          `"` +
          indexns +
          `"` +
          this.$t("boarMeasure.dataItem") +
          this.$t("boarMeasure.cannotbeRecovered"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delPigdata(indexns);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("boarMeasure.sureExportPigdata"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportPigdata(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    handleOut() {
      // openLan
      //   this.reset();

      this.openLan = true;
      this.$refs.tree.setCheckedKeys([]);
    },
    cancelLan() {
      // this.$refs.tree.setCheckedKeys([]);
      this.openLan = false;
    },
    handleLanOut() {
      let nindexs = this.lan.filter((item) =>
        new Set(this.$refs.tree.getCheckedKeys()).has(item)
      );
      this.$confirm(
        this.$t("boarMeasure.sureMensurationEnd"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      ).then(() => {
        outLan({
          nindexs,
          mfactory: this.$store.state.settings.nowPigFarm,
        }).then((response) => {
          // this.openLan = false;
          this.msgSuccess(this.$t("boarMeasure.mensurationEndSuccess"));
          this.$refs.tree.setCheckedKeys([]);
        });
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = this.$t("boarMeasure.batchUpdateInfo");
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      exportPigdata().then((response) => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
