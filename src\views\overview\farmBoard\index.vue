<template>
  <div class="app-container board-page">
    <div class="board-header clearfix">
      <h2 class="board-title">猪场信息总览</h2>
      <span class="fl" id="date" v-show="device !== 'mobile'">
        {{ this.handleDate() }}
        <!-- <span>{{ date }}</span> -->
        <!-- <span class="year">{{ year }}</span>
        <span class="short"></span>
        <span class="month">{{ month }}</span>
        <span class="short"></span>
        <span class="dat">{{ dat }}</span>
        <span class="short"></span>
        <span class="day">{{ day }}</span>
        <span class="empty"></span>
        <span class="h">{{ h }}</span>
        <span class="short"></span>
        <span class="m">{{ m }}</span>
        <span class="short"></span>
        <span class="s">{{ s }}</span> -->
      </span>
    </div>
    <!-- <el-row> -->
    <el-row :xs="24" :sm="24" :md="24" :lg="24" class="card-box">
      <el-card v-loading="loading">
        <!-- 集团总览 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="5" class="card-box">
          <!-- <el-card>
              <div slot="header"><span>饼图</span></div> -->
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="存栏数量" name="first">
              <div id="category1" ref="category1" style="height: 330px" />
            </el-tab-pane>
            <el-tab-pane label="测定站在线数量" name="second">
              <div ref="category2" style="height: 330px" />
            </el-tab-pane>
          </el-tabs>

          <!-- </el-card> -->
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="14" class="card-box">
          <el-row>
            <el-col
              :xs="8"
              :sm="8"
              :md="8"
              :lg="8"
              class="card-box"
              style="text-align: center"
            >
              <strong class="strong">{{ this.pigFarmsOptions.length }}</strong>
              <span style="font-size: 18px">猪场数量</span>
            </el-col>
            <el-col
              :xs="8"
              :sm="8"
              :md="8"
              :lg="8"
              class="card-box"
              style="text-align: center"
            >
              <strong class="strong">{{
                countTotal(this.pigdataArry, "value")
              }}</strong>
              <span style="font-size: 18px">存栏数量</span>
            </el-col>
            <el-col
              :xs="8"
              :sm="8"
              :md="8"
              :lg="8"
              class="card-box"
              style="text-align: center"
            >
              <!-- 测定站在线数量 -->
              <strong class="strong">{{
                countTotal(this.controlArry, "value")
              }}</strong>
              <span style="font-size: 18px">测定站在线数量</span>
            </el-col>
          </el-row>
          <el-row type="flex" align="middle">
            <el-col
              :xs="24"
              :sm="24"
              :md="12"
              :lg="12"
              style="text-align: center"
            >
              <div ref="category3" style="height: 300px"
            /></el-col>
            <el-col
              :xs="24"
              :sm="24"
              :md="12"
              :lg="12"
              style="text-align: center"
            >
              <div ref="category4" style="height: 300px"
            /></el-col>

            <!-- <el-col
              :xs="24"
              :sm="24"
              :md="12"
              :lg="12"
              style="text-align: center"
            >
              <div ref="category3" style="height: 300px; margin-top: 30px" />

              <span
                style="
                  font-size: 15px;
                  font-family: sans-serif;
                  font-weight: 600;
                  color: #696969;
                "
                >最新平均体重对比</span
              >
            </el-col>
            <el-col
              :xs="24"
              :sm="24"
              :md="12"
              :lg="12"
              style="text-align: center"
            >
              <div ref="category4" style="height: 300px; margin-top: 30px" />
              <span
                style="
                  font-size: 15px;
                  font-family: sans-serif;
                  font-weight: 600;
                  color: #696969;
                "
                >昨天平均饲料消耗量</span
              >
            </el-col> -->
          </el-row>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="5" class="card-box">
          <el-tabs v-model="activeName2" @tab-click="handleClick">
            <el-tab-pane label="猪场排行榜" name="four">
              <el-tabs
                tab-position="right"
                style="height: 400px; margin-right: -20px"
              >
                <el-tab-pane label="存栏量">
                  <div
                    class="top-10"
                    style="
                      height: 400px;
                      overflow-y: auto; /* 启用垂直滚动条 */
                      -ms-overflow-style: none; /* Internet Explorer 10+ */
                      scrollbar-width: none; /* Firefox */
                    "
                  >
                    <ul>
                      <li v-for="(item, index) in pigdataArrySort" :key="index">
                        <el-tooltip
                          :content="fullNumber(item.name, item.value)"
                          placement="top"
                        >
                          <span v-if="isOverLength(item.name)">{{
                            truncatedNumber(item.name)
                          }}</span>
                          <span v-else>{{ item.name }}</span>
                        </el-tooltip>
                        <span style="float: right; margin-right: 10px">
                          {{ item.value }}
                        </span>
                      </li>
                    </ul>
                    <!-- <div id="rank-more">
                      <span class="span-h7"><a href="#">查看更多</a></span>
                    </div> -->
                  </div>
                </el-tab-pane>
                <el-tab-pane label="测定站">
                  <div
                    class="top-10"
                    style="
                      height: 400px;
                      overflow-y: auto; /* 启用垂直滚动条 */
                      -ms-overflow-style: none; /* Internet Explorer 10+ */
                      scrollbar-width: none; /* Firefox */
                    "
                  >
                    <ul>
                      <li v-for="(item, index) in controlArrySort" :key="index">
                        <el-tooltip
                          :content="fullNumber(item.name, item.value)"
                          placement="top"
                        >
                          <span v-if="isOverLength(item.name)">{{
                            truncatedNumber(item.name)
                          }}</span>
                          <span v-else>{{ item.name }}</span>
                        </el-tooltip>
                        <span style="float: right; margin-right: 10px"
                          >{{ item.value }}
                        </span>
                      </li>
                    </ul>
                    <!-- <div id="rank-more">
                      <span class="span-h7"><a href="#">查看更多</a></span>
                    </div> -->
                  </div>
                </el-tab-pane>
                <el-tab-pane label="平均体重">
                  <div
                    class="top-10"
                    style="
                      height: 400px;
                      overflow-y: auto; /* 启用垂直滚动条 */
                      -ms-overflow-style: none; /* Internet Explorer 10+ */
                      scrollbar-width: none; /* Firefox */
                    "
                  >
                    <ul>
                      <li v-for="(item, index) in weightArrySort" :key="index">
                        <el-tooltip
                          :content="fullNumber(item.name, item.value)"
                          placement="top"
                        >
                          <span v-if="isOverLength(item.name)">{{
                            truncatedNumber(item.name)
                          }}</span>
                          <span v-else>{{ item.name }}</span>
                        </el-tooltip>
                        <span style="float: right; margin-right: 10px"
                          >{{ item.value }}
                        </span>
                      </li>
                    </ul>
                    <!-- <div id="rank-more">
                      <span class="span-h7"><a href="#">查看更多</a></span>
                    </div> -->
                  </div>
                </el-tab-pane>
                <el-tab-pane label="昨日饲料消耗">
                  <div
                    class="top-10"
                    style="
                      height: 400px;
                      overflow-y: auto; /* 启用垂直滚动条 */
                      -ms-overflow-style: none; /* Internet Explorer 10+ */
                      scrollbar-width: none; /* Firefox */
                    "
                  >
                    <ul>
                      <li v-for="(item, index) in foodArrySort" :key="index">
                        <el-tooltip
                          :content="fullNumber(item.name, item.value)"
                          placement="top"
                        >
                          <span v-if="isOverLength(item.name)">{{
                            truncatedNumber(item.name)
                          }}</span>
                          <span v-else>{{ item.name }}</span>
                        </el-tooltip>
                        <span style="float: right; margin-right: 10px"
                          >{{ item.value }}
                        </span>
                      </li>
                    </ul>
                    <!-- <div id="rank-more">
                      <span class="span-h7"><a href="#">查看更多</a></span>
                    </div> -->
                  </div>
                </el-tab-pane>
              </el-tabs>
              <!-- <div ref="category5" style="height: 300px" /> -->
            </el-tab-pane>
            <el-tab-pane label="猪场参数对比" name="third">
              <div ref="category9" style="height: 400px" />
            </el-tab-pane>

            <!-- <el-tab-pane label="平均饲料消耗量对比" name="four">
                <div ref="category6" style="height: 300px" />
              </el-tab-pane> -->
          </el-tabs>
        </el-col>
      </el-card>
    </el-row>
    <!-- </el-row> -->
    <!-- <el-row> -->
    <el-row
      :xs="24"
      :sm="24"
      :md="24"
      :lg="24"
      class="card-box"
      v-for="(item, index) in pigdataArry"
      :key="index"
    >
      <el-card v-loading="loading">
        <el-row>
          <h2 style="display: inline-block; color: #6d6d6d; margin: 10px">
            {{ item.name }}
          </h2></el-row
        >
        <el-row>
          <el-col
            :xs="4"
            :sm="4"
            :md="2"
            :lg="2"
            class="card-box"
            style="text-align: center; margin-top: 50px"
          >
            <div>
              <strong class="strong">{{ item.value }}</strong>
              <span style="font-size: 14px">存栏数量</span>
              <strong class="strong" style="margin-top: 30px">{{
                controlArry[index] && controlArry[index].value
              }}</strong>
              <span style="font-size: 14px">测定站在线数量</span>
            </div>
          </el-col>
          <!-- <el-col
            :xs="2"
            :sm="2"
            :md="2"
            :lg="2"
            class="card-box"
            style="text-align: center"
          >
            <strong class="strong">{{ item.value }}</strong>
            <span style="font-size: 12px; padding: 10px">存栏数量</span>

          </el-col>
          <el-col
            :xs="2"
            :sm="2"
            :md="2"
            :lg="2"
            class="card-box"
            style="text-align: center"
          >
            <strong class="strong">{{
              controlArry[index] && controlArry[index].value
            }}</strong>
            <span style="font-size: 12px; padding: 10px">测定站在线数量</span>
          </el-col> -->
          <el-col :xs="18" :sm="18" :md="10" :lg="10">
            <div :id="'category' + index + '1'" style="height: 300px" />
          </el-col>
          <el-col :xs="22" :sm="22" :md="10" :lg="10" style="margin-left: 50px">
            <div :id="'category' + index + '2'" style="height: 300px" />
          </el-col>
        </el-row>
      </el-card>
    </el-row>

    <!-- <el-col :xs="24" :sm="24" :md="12" :lg="8" class="card-box">
        <el-card>
          <el-row>
            <el-col :xs="8" :sm="8" :md="8" :lg="8">
              <h1 style="display: inline-block">朋城</h1>
            </el-col>
            <el-col
              :xs="8"
              :sm="8"
              :md="8"
              :lg="8"
              class="card-box"
              style="text-align: center"
            >
              <strong class="strong">3</strong>
              <span style="font-size: 12px; padding: 10px">存栏数量</span>
            </el-col>
            <el-col
              :xs="8"
              :sm="8"
              :md="8"
              :lg="8"
              class="card-box"
              style="text-align: center"
            >
              <strong class="strong">3</strong>
              <span style="font-size: 12px; padding: 10px">测定站在线数量</span>
            </el-col>
          </el-row>
          <el-row>
            <div ref="category9" style="height: 300px" />
          </el-row>
          <el-row>
            <div ref="category10" style="height: 300px" />
          </el-row>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="8" class="card-box">
        <el-card>
          <el-row>
            <el-col :xs="8" :sm="8" :md="8" :lg="8">
              <h1 style="display: inline-block">达州</h1>
            </el-col>
            <el-col
              :xs="8"
              :sm="8"
              :md="8"
              :lg="8"
              class="card-box"
              style="text-align: center"
            >
              <strong class="strong">3</strong>
              <span style="font-size: 12px; padding: 10px">存栏数量</span>
            </el-col>
            <el-col
              :xs="8"
              :sm="8"
              :md="8"
              :lg="8"
              class="card-box"
              style="text-align: center"
            >
              <strong class="strong">3</strong>
              <span style="font-size: 12px; padding: 10px">测定站在线数量</span>
            </el-col>
          </el-row>
          <el-row>
            <div ref="category11" style="height: 300px" />
          </el-row>
          <el-row>
            <div ref="category12" style="height: 300px" />
          </el-row>
        </el-card>
      </el-col> -->

    <!-- <el-col :xs="24" :sm="24" :md="12" :lg="8" class="card-box">
        <el-card>
          <el-row style="display: flex">
            <span>数量</span>
          </el-row>
          <el-row style="display: flex">
            <span>体重</span>
          </el-row>
          <el-row style="display: flex">
            <span>饲料消耗量</span>
          </el-row>
        </el-card>
      </el-col> -->
    <!-- </el-row> -->
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import * as echarts from "echarts";
require("@/utils/walden"); // echarts theme
import {
  measureGatherFoodCharts,
  measureGatherweightCharts,
} from "@/api/system/measureday";
import { listFactoryByUser } from "@/api/system/factory";
import { listPigdata } from "@/api/system/pigdata";
import { listControl } from "@/api/system/control";
import store from "@/store";
import { isEmpty } from "lodash";
// let _ = require('lodash')
export default {
  name: "homeData",
  data() {
    return {
      user: store.getters && store.getters.user,
      maxLength: 10, // 超过这个长度则显示省略号
      loading: true,
      year: null,
      month: null,

      dat: null,
      day: null,
      h: null,
      m: null,
      s: null,
      date: new Date(),
      //猪场数量
      pigFarmsOptions: [],
      //种猪数
      pigdataArry: [],
      // 测定站在线数量
      controlArry: [],
      //种猪数排序
      pigdataArrySort: [],
      //测定站在线数量排序
      controlArrySort: [],
      //平均体重排序
      weightArrySort: [],
      //饲料消耗量排序
      foodArrySort: [],

      activeName: "first",
      activeName2: "four",
      //存栏数量
      category1: null,
      xAxisList1: [],
      yAxisList1: [],
      dataList1: null,
      //测定站在线数量
      category2: null,
      xAxisList2: [],
      yAxisList2: [],
      dataList2: null,
      //存栏数量
      category3: null,
      xAxisList3: [],
      yAxisList3: [],
      dataList3: null,
      //测定站在线数量
      category4: null,
      xAxisList4: [],
      yAxisList4: [],
      dataList4: null,
      //平均体重对比
      category5: null,
      xAxisList5: [],
      yAxisList5: [],
      dataList5: null,
      //平均饲料消耗量对比
      category6: null,
      xAxisList6: [],
      yAxisList6: [],
      dataList6: null,
      category7: null,
      category8: null,
      category9: null,
    };
  },
  created() {},
  watch: {
    date: {
      handler() {
        if (this.date) {
          this.year = this.date.getFullYear();
          this.month = this.date.getMonth() + 1;
          this.dat = this.date.getDate();
          this.day = this.switchDay(this.date.getDay());
          this.h = this.getZero(this.date.getHours());
          this.m = this.getZero(this.date.getMinutes());
          this.s = this.getZero(this.date.getSeconds());
        }
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters(["device"]),
  },
  mounted() {
    this.getList();

    window.onresize = () => {
      // alert("sss");
      this.category1.resize(); //重新初始化echarts
      // this.category6.resize(); //重新初始化echarts
    };
    // window.addEventListener("resize", this.handleWindowResize);
    let _this = this; // 声明一个变量指向Vue实例this，保证作用域一致
    this.timer = setInterval(() => {
      _this.date = new Date(); // 修改数据date
    }, 1000);
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer); // 在Vue实例销毁前，清除我们的定时器
    }
    // window.removeEventListener("resize", this.handleWindowResize);
  },
  methods: {
    truncatedNumber(value) {
      return `${value.toString().substring(0, this.maxLength)}...`;
    },
    fullNumber(value, number) {
      return value + " " + number;
    },
    isOverLength(value) {
      return value.toString().length > this.maxLength;
    },
    handleString(value) {
      return value.toString();
    },
    handleClick(tab, event) {
      if (tab.name == "first") this.getList1();
      if (tab.name == "second") {
        this.getList2();
        this.$nextTick(() => {
          this.category2.resize();
        });
      }

      if (tab.name == "four") {
        // this.getList6();
        // this.$nextTick()在页面交互，尤其是从后台获取数据后重新生成dom对象之后的操作有很大的优势
        // this.$nextTick()将回调延迟到下次 DOM 更新循环之后执行。在修改数据之后立即使用它，然后等待 DOM 更新。它跟全局方法 Vue.nextTick 一样，不同的是回调的 this 自动绑定到调用它的实例上。
        // this.$nextTick(() => {
        //   this.category6.resize();
        // });
      }
      if (tab.name == "third") {
        this.$nextTick(() => {
          this.category9.resize();
        });
      }
    },
    getZero(num) {
      // 个位数前补0
      if (parseInt(num) < 10) {
        num = "0" + num;
      }
      return num;
    },
    switchDay(number) {
      let nowDay = null;
      // “星期一:Monday 缩写:Mon. 星期二:Tuesday 缩写:Tue. 星期三:Wednesday 缩写:Wed. 星期四:Thursday 缩
      // 写:Thur. 星期五:Friday 缩写:Fri. 星期六:Saturday 缩写:Sat. 星期日:Sunday 缩写:Sun
      switch (number) {
        case 1:
          nowDay = "Mon";
          break; //停止执行，跳出switch
        case 2:
          nowDay = "Tue";
          break; //停止执行，跳出switch
        case 3:
          nowDay = "Wed";
          break; //停止执行，跳出switch
        case 4:
          nowDay = "Thur";
          break; //停止执行，跳出switch
        case 5:
          nowDay = "Fri";
          break; //停止执行，跳出switch
        case 6:
          nowDay = "Sat";
          break; //停止执行，跳出switch
        case 0:
          nowDay = "Sun";
          break; //停止执行，跳出switch
        default:
        //上述条件都不满足时，默认执行的代码
        // nowDay = "grey2.png";
        //   console.log("游客");
      }

      return nowDay;
    },
    handleDate() {
      // this.date = new Date();
      let year = this.date.getFullYear();
      let month = this.date.getMonth() + 1;
      let dat = this.date.getDate();
      let day = this.date.getDay();
      let h = this.getZero(this.date.getHours());
      let m = this.getZero(this.date.getMinutes());
      let s = this.getZero(this.date.getSeconds());

      let weeks = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ];
      let nowtime = `${year}年${month}月${dat}日 ${weeks[day]} ${h}:${m}:${s}`;
      return nowtime;
      // return (
      //   year + "年" + month + "月" + dat + "日" + "  " + weeks[day] + h
      // );
      // date.getFullYear(); //获取完整的年份(4位)
      // date.getMonth(); //获取当前月份(0-11,0代表1月)
      // date.getDate(); //获取当前日(1-31)
      // date.getDay(); //获取当前星期X(0-6,0代表星期天)
      // date.toLocaleDateString(); //获取当前日期
    },
    /**
     * 当窗口缩放时，echart动态调整自身大小
     */
    handleWindowResize() {
      // if (!this.category1) return;
      // this.category1.resize();
      this.category6.resize();
    },

    //计算对象数组中某个属性合计
    countTotal(arr, keyName) {
      let $total = 0;
      $total = arr.reduce(function (total, currentValue, currentIndex, arr) {
        return currentValue[keyName] ? total + currentValue[keyName] : total;
      }, 0);
      return $total;
    },

    compare(property) {
      return function (a, b) {
        var value1 = a[property];
        var value2 = b[property];
        return value1 - value2;
      };
    },
    compareMax(property) {
      return function (a, b) {
        var value1 = a[property];
        var value2 = b[property];
        return value2 - value1;
      };
    },

    async getList() {
      this.loading = true;
      await listFactoryByUser({ userId: this.user.userId }).then((response) => {
        this.pigFarmsOptions = response.rows;
      });
      this.pigdataArry = [];
      this.pigdataArrySort = [];
      let promisePigdataArr = [];
      promisePigdataArr = this.pigFarmsOptions.map(async (item, index) => {
        await listPigdata({ mfactory: item.id }).then((response) => {
          this.pigdataArry.push({
            value:
              response.rows &&
              response.rows.filter((i) => i.ntype === 1).length,
            name: item.facname,
            index,
          });
        });
      });

      await Promise.all(promisePigdataArr);

      let promiseControlArr = [];
      this.controlArry = [];
      this.controlArrySort = [];
      promiseControlArr = this.pigFarmsOptions.map(async (item, index) => {
        await listControl({ mfactory: item.id, ntype: 3 }).then((response) => {
          this.controlArry.push({
            value:
              response.rows &&
              response.rows.filter((i) => i.nstatus === 1).length,
            name: item.facname,
            index,
          });
        });
      });

      await Promise.all(promiseControlArr);

      let promiserWeightArr = [];
      this.dataList7 = [];
      // let promiserFoodArr = [];
      this.dataList8 = [];
      promiserWeightArr = this.pigFarmsOptions.map(async (item, index) => {
        await measureGatherweightCharts({ mfactory: item.id }).then(
          (response) => {
            this.dataList7.push({
              xAxisList7: response.rows && response.rows[0].xAries.reverse(),
              yAxisList7: response.rows && response.rows[0].yAries.reverse(),
              name: item.facname,
              index,
            });
          }
        );
        await measureGatherFoodCharts({ mfactory: item.id }).then(
          (response) => {
            this.dataList8.push({
              xAxisList8: response.rows && response.rows[0].xAries.reverse(),
              yAxisList8: response.rows && response.rows[0].yAries.reverse(),
              name: item.facname,
              index,
            });
          }
        );
      });
      await Promise.all(promiserWeightArr);
      // let a = this.pigdataArry;
      let a = JSON.parse(JSON.stringify(this.pigdataArry));
      this.pigdataArrySort = a.sort(this.compareMax("value"));
      this.pigdataArry.sort(this.compare("index"));

      // let b = this.controlArry;
      let b = JSON.parse(JSON.stringify(this.controlArry));
      this.controlArrySort = b.sort(this.compareMax("value"));
      this.controlArry.sort(this.compare("index"));

      this.getDatas1();
      this.getDatas3();
      // this.getDatas4();
      // this.getDatas5();
      this.getDatas7();
      this.getDatas8();
      this.getDatas9();
      this.$nextTick(() => {
        this.category7.resize();
      });
      this.loading = false;
    },
    async getList1() {
      this.getDatas1();
      // this.getDatas3();
      // this.getDatas4();
      // this.getDatas5();
      // this.getDatas7();
      // this.getDatas8();
      // this.getDatas9();
    },
    async getList2() {
      this.getDatas2();
    },
    async getList5() {
      let promiserWeightArr = [];
      this.dataList5 = [];
      promiserWeightArr = this.pigFarmsOptions.map(async (item) => {
        await measureGatherweightCharts({ mfactory: item.id }).then(
          (response) => {
            this.dataList5.push({
              xAxisList5: response.rows && response.rows[0].xAries.reverse(),
              yAxisList5: response.rows && response.rows[0].yAries.reverse(),
              name: item.facname,
            });
          }
        );
      });
      await Promise.all(promiserWeightArr);

      this.getDatas5();
      // measureGatherweightCharts().then((response) => {
      //   this.dataList5 = response.rows;
      //   this.dataList5.forEach((element) => {
      //     this.xAxisList5 = element.xAries;
      //     this.yAxisList5 = element.yAries;
      //   });
      //   this.getDatas5();
      // });
      // listHistory(this.queryParams).then((response) => {
      //   if (response && response.rows) {
      //     this.historyList = response.rows;
      //     this.xAxisList = [];
      //     this.yAxisList = [];
      //     this.historyList.forEach((element) => {
      //       this.xAxisList.push(element.uptime);
      //       this.yAxisList.push(element.temperature);
      //     });
      //   } else {
      //     this.historyList = [];
      //     this.xAxisList = [];
      //     this.yAxisList = [];
      //   }
      // this.getDatas5();
      // });
    },
    getList6() {
      this.getDatas6();
      // measureGatherFoodCharts(
      //   this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      // ).then((response) => {
      //   this.dataList6 = response.rows;
      //   this.dataList6.forEach((element) => {
      //     this.xDataFood = element.xAries;
      //     this.yDataFood = element.yAries;
      //   });
      //   this.getDatas6();
      // });
    },
    getDatas1() {
      this.category1 = echarts.init(
        document.getElementById("category1"),
        "walden"
      );
      this.category1.setOption({
        title: {
          text: "集团种猪存栏分布",
          bottom: "bottom", // 将标题放在底部
          left: "center", // 设置标题水平居中
          textStyle: {
            fontSize: 15,
            // fontFamily:'sans-serif',
            //fontFamily: "Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif",
          },
        },
        color: [
          "#507E32",
          "#618B46",
          "#73995B",
          "#84A66F",
          "#95B384",
          "#A6C098",
          "#B8CEAD",
          "#C9DBC1",
        ],
        tooltip: {
          // trigger: 'axis',
          trigger: "item",
          show: "true",
          formatter: "{b} : {c} ({d}%)",
          confine: true,
          extraCssText:
            "white-space: normal; word-break: break-all;z-index:100000;",
        },
        legend: {
          orient: "vertical",
          left: "left",
          top: "6%",
          show: false,
        },
        series: [
          {
            name: "存栏数量",
            type: "pie",
            radius: ["30%", "60%"],
            data: this.pigdataArry.sort(function (a, b) {
              return a.value - b.value;
            }),

            labelLine: {
              // lineStyle: {
              //   color: "rgba(255, 255, 255, 0.3)",
              // },
              smooth: 1,
              length: 5,
              length2: 0,
            },

            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
          {
            type: "pie",
            radius: ["30%", "60%"],
            data: this.pigdataArry.sort(function (a, b) {
              return b.value - a.value;
            }),
            label: {
              normal: {
                show: true,
                position: "inner", // 数值显示在内部
                formatter: "{c}", // 格式化数值百分比输出
              },
            },
          },
        ],
      });
    },
    getDatas2() {
      this.category2 = echarts.init(this.$refs.category2, "walden");
      this.category2.setOption({
        color: [
          "#507E32",
          "#618B46",
          "#73995B",
          "#84A66F",
          "#95B384",
          "#A6C098",
          "#B8CEAD",
          "#C9DBC1",
        ],
        title: {
          text: "集团测定站分布",
          left: "center",
          bottom: "bottom", // 将标题放在底部
          textStyle: {
            fontSize: 15,
          },
        },
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c} ({d}%)",
          confine: true,
          extraCssText:
            "white-space: normal; word-break: break-all;z-index:100000;",
        },
        legend: {
          orient: "vertical",
          left: "left",
          top: "6%",
          show: false,
        },
        series: [
          {
            name: "测定站在线数量",
            type: "pie",
            radius: "50%",
            data: this.controlArry,
          },
        ],
      });
    },
    getDatas3() {
      this.category3 = echarts.init(this.$refs.category3, "walden");
      this.category4 = echarts.init(this.$refs.category4, "walden");
      let dataX = [];
      let dataY = [];
      let data = [];

      this.dataList7.map((item) => {
        // dataY.push(item.yAxisList7.slice(-1)[0] || 0);
        // dataX.push(item.name);
        data.push({
          value: item.yAxisList7.slice(-1)[0] || 0,
          name: item.name,
        });
      });
      let c = JSON.parse(JSON.stringify(data));
      this.weightArrySort = c.sort(this.compareMax("value"));
      this.weightArrySort.map((item) => {
        dataY.push(item.value);
        dataX.push(item.name);
      });

      let data8X = [];
      let data8Y = [];
      let data8 = [];
      this.dataList8.map((item) => {
        // data8Y.push(item.yAxisList8.slice(-1)[0] / 1000 || 0);
        // data8X.push(item.name);
        data8.push({
          value: item.yAxisList8.slice(-1)[0] / 1000 || 0,
          name: item.name,
        });
      });
      let d = JSON.parse(JSON.stringify(data8));
      this.foodArrySort = d.sort(this.compareMax("value"));
      this.foodArrySort.map((item) => {
        data8Y.push(item.value);
        data8X.push(item.name);
      });
      this.category3.setOption({
        title: {
          text: "平均体重(kg)",
          bottom: "bottom", // 将标题放在底部
          left: "center",
          textStyle: {
            fontSize: 15,
          },
        },
        legend: {
          data: "平均体重(kg)",
        },
        color: [
          "#507E32",
          "#618B46",
          "#73995B",
          "#84A66F",
          "#95B384",
          "#A6C098",
          "#B8CEAD",
          "#C9DBC1",
        ],
        tooltip: {
          trigger: "axis",
        },
        // grid: {
        //   left: "0%",
        //   right: "0%",
        //   bottom: "3%",
        //   containLabel: true,
        // },

        toolbox: {
          // feature: {
          //   saveAsImage: {},
          // },
        },
        xAxis: {
          type: "category",
          data: dataX,
          // axisTick: {
          //   show: false, // 不显示坐标轴刻度线
          // },
          // axisLine: {
          //   show: false, // 不显示坐标轴线
          // },

          splitLine: {
            show: false, // 不显示网格线
          },

          axisLabel: {
            interval: 0, //代表显示所有x轴标签显
            // rotate: 45, // 将 x 轴标签倾斜45度
            formatter: function (value) {
              // 自定义格式化函数，可以根据需要截取或缩略标签名字
              return value.length > 5 ? value.substring(0, 5) + "..." : value;
            },
          },
        },
        yAxis: {
          type: "value",
          show: false,
          splitLine: {
            show: false, // 不显示网格线
          },
          axisLine: {
            //x轴坐标轴，false为隐藏，true为显示
            show: false,
          },
          axisTick: {
            //x轴刻度线
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            label: {
              show: true,
              position: "top",
            },

            data: dataY,
            type: "bar",
            name: "平均体重(kg)",
          },
        ],
      });

      this.category4.setOption({
        title: {
          text: "昨日饲料消耗(kg)",
          bottom: "bottom", // 将标题放在底部
          left: "center",
          textStyle: {
            fontSize: 15,
          },
        },
        legend: {
          data: "昨日饲料消耗(kg)",
        },
        color: [
          "#507E32",
          "#618B46",
          "#73995B",
          "#84A66F",
          "#95B384",
          "#A6C098",
          "#B8CEAD",
          "#C9DBC1",
        ],

        tooltip: {
          trigger: "axis",
        },
        // legend: {
        //   data: "体重",
        // },
        // grid: {
        //   left: "0%",
        //   right: "0%",
        //   bottom: "3%",
        //   containLabel: true,
        // },
        // toolbox: {
        //   feature: {
        //     saveAsImage: {},
        //   },
        // },
        xAxis: {
          type: "category",
          data: dataX,
          splitLine: {
            show: false, // 不显示网格线
          },
          axisLabel: {
            interval: 0, //代表显示所有x轴标签显
            // rotate: 45, // 将 x 轴标签倾斜45度
            formatter: function (value) {
              // 自定义格式化函数，可以根据需要截取或缩略标签名字
              return value.length > 5 ? value.substring(0, 5) + "..." : value;
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: false, // 不显示网格线
          },
          axisLine: {
            //x轴坐标轴，false为隐藏，true为显示
            show: false,
          },
          axisTick: {
            //x轴刻度线
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            label: {
              show: true,
              position: "top",
            },

            data: data8Y,
            type: "bar",
            name: "昨日饲料消耗量(kg)",
          },
        ],
      });
    },

    // getDatas3() {
    //   this.category3 = echarts.init(this.$refs.category3, "walden");
    //   let data = [];
    //   this.dataList7.map((item) => {
    //     data.push({
    //       value: item.yAxisList7.slice(-1)[0] || 0,
    //       name: item.name,
    //     });
    //   });
    //   let c = JSON.parse(JSON.stringify(data));
    //   this.weightArrySort = c.sort(this.compareMax("value"));
    //   this.category3.setOption({
    //     // title: {
    //     //   text: "最新平均体重对比",
    //     //   left: "bottom",
    //     // },
    //     tooltip: {
    //       trigger: "item",
    //       confine: true,
    //       extraCssText:
    //         "white-space: normal; word-break: break-all;z-index:100000;",
    //       show: true,
    //     },
    //     series: [
    //       {
    //         type: "gauge",
    //         min: 0,
    //         max: 200,
    //         splitNumber: 10,

    //         anchor: {
    //           show: true,
    //           showAbove: true,
    //           size: 12,
    //           itemStyle: {
    //             color: "#FAC858",
    //           },
    //         },
    //         pointer: {
    //           icon: "path://M2.9,0.7L2.9,0.7c1.4,0,2.6,1.2,2.6,2.6v115c0,1.4-1.2,2.6-2.6,2.6l0,0c-1.4,0-2.6-1.2-2.6-2.6V3.3C0.3,1.9,1.4,0.7,2.9,0.7z",
    //           width: 5,
    //           length: "90%",
    //           offsetCenter: [0, "5%"],
    //         },
    //         // tooltip: {
    //         //   trigger: "item",
    //         // },

    //         progress: {
    //           show: true,
    //           overlap: true,
    //           roundCap: true,
    //         },
    //         axisLine: {
    //           lineStyle: {
    //             width: 8,
    //           },
    //           roundCap: true,
    //         },
    //         axisLabel: {
    //           distance: -20,
    //           color: "#999",
    //           fontSize: 10,
    //         },
    //         axisTick: {
    //           distance: -25,
    //           splitNumber: 5,
    //           lineStyle: {
    //             width: 2,
    //             color: "#999",
    //           },
    //         },
    //         splitLine: {
    //           distance: -28,
    //           length: 14,
    //           lineStyle: {
    //             width: 3,
    //             color: "#999",
    //           },
    //         },
    //         data: data,
    //         title: {
    //           fontSize: 10,
    //           show: false,
    //         },
    //         detail: {
    //           // 仪表盘详情，用于显示数据。
    //           show: false, // 是否显示详情,默认 true。
    //           offsetCenter: ["-40%", "90%"], // 相对于仪表盘中心的偏移位置，数组第一项是水平方向的偏移，第二项是垂直方向的偏移。可以是绝对的数值，也可以是相对于仪表盘半径的百分比。
    //           color: "#fff",
    //           fontSize: 15, // 文字的字体大小,默认 15。
    //           formatter: "{value}", // 格式化函数或者字符串
    //           width: 15,
    //           height: 9,
    //           borderRadius: 3,
    //           backgroundColor: "auto",
    //         },
    //       },
    //     ],
    //   });
    // },
    getDatas4() {
      this.category4 = echarts.init(this.$refs.category4, "walden");
      let data = [];
      this.dataList8.map((item) => {
        // console.log("item", item);
        data.push({
          value: item.yAxisList8.slice(-1)[0] || 0,
          name: item.name,
        });
      });
      let d = JSON.parse(JSON.stringify(data));
      this.foodArrySort = d.sort(this.compareMax("value"));
      this.category4.setOption({
        // title: {
        //   text: "最新饲料消耗量对比",
        //   left: "bottom",
        // },
        tooltip: {
          trigger: "item",
          show: true,
          confine: true,
          extraCssText:
            "white-space: normal; word-break: break-all;z-index:100000;",
        },
        series: [
          {
            type: "gauge",
            min: 0,
            max: 20000,
            splitNumber: 10,

            anchor: {
              show: true,
              showAbove: true,
              size: 12,
              itemStyle: {
                color: "#FAC858",
              },
            },
            pointer: {
              icon: "path://M2.9,0.7L2.9,0.7c1.4,0,2.6,1.2,2.6,2.6v115c0,1.4-1.2,2.6-2.6,2.6l0,0c-1.4,0-2.6-1.2-2.6-2.6V3.3C0.3,1.9,1.4,0.7,2.9,0.7z",
              width: 5,
              length: "90%",
              offsetCenter: [0, "5%"],
            },

            progress: {
              show: true,
              overlap: true,
              roundCap: true,
            },
            axisLine: {
              lineStyle: {
                width: 8,
              },
              roundCap: true,
            },
            axisLabel: {
              distance: -20,
              color: "#999",
              fontSize: 10,
            },
            axisTick: {
              distance: -25,
              splitNumber: 5,
              lineStyle: {
                width: 2,
                color: "#999",
              },
            },
            splitLine: {
              distance: -28,
              length: 14,
              lineStyle: {
                width: 3,
                color: "#999",
              },
            },
            data: data,
            title: {
              fontSize: 10,
              show: false,
            },
            detail: {
              // 仪表盘详情，用于显示数据。
              show: false, // 是否显示详情,默认 true。
              offsetCenter: ["-40%", "90%"], // 相对于仪表盘中心的偏移位置，数组第一项是水平方向的偏移，第二项是垂直方向的偏移。可以是绝对的数值，也可以是相对于仪表盘半径的百分比。
              color: "#fff",
              fontSize: 15, // 文字的字体大小,默认 15。
              formatter: "{value}", // 格式化函数或者字符串
              width: 15,
              height: 9,
              borderRadius: 3,
              backgroundColor: "auto",
            },
          },
        ],
      });
    },
    getDatas5() {
      this.category5 = echarts.init(this.$refs.category5, "walden");
      this.category5.setOption({
        // title: {
        //   text: "折线图堆叠",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [],
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          // data: this.xAxisList5.reverse(),
        },
        yAxis: {
          type: "value",
        },
        // {
        //     name:'邮件营销',
        //     type:'line',
        //     stack: '总量',
        //     data:[120, 132, 101, 134, 90, 230, 210]
        // },
        series: [
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },
            name: "巨星",
            type: "line",
            stack: "总量",
            data: [120, 132, 101, 134, 90, 230, 210],
          },
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },
            name: "达州",
            type: "line",
            stack: "总量",
            data: [220, 182, 191, 234, 290, 330, 310],
          },
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },
            name: "朋城",
            type: "line",
            stack: "总量",
            data: [150, 232, 201, 154, 190, 330, 410],
          },
        ],
      });
    },
    getDatas6() {
      this.category6 = echarts.init(this.$refs.category6, "walden");
      this.category6.setOption({
        // title: {
        //   text: "折线图堆叠",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["巨星", "达州", "朋城"],
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },
            name: "邮件营销",
            type: "line",
            stack: "总量",
            data: [120, 132, 101, 134, 90, 230, 210],
          },
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },
            name: "联盟广告",
            type: "line",
            stack: "总量",
            data: [220, 182, 191, 234, 290, 330, 310],
          },
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },
            name: "视频广告",
            type: "line",
            stack: "总量",
            data: [150, 232, 201, 154, 190, 330, 410],
          },
        ],
      });
    },
    getDatas7() {
      this.dataList7.sort(this.compare("index"));
      this.pigdataArry.map((item, index) => {
        this.category7 = echarts.init(
          document.getElementById("category" + index + "1"),
          "walden"
        );
        this.category7.setOption({
          color: [
            "#507E32",
            "#618B46",
            "#73995B",
            "#84A66F",
            "#95B384",
            "#A6C098",
            "#B8CEAD",
            "#C9DBC1",
          ],
          title: {
            text: "体重(kg)",
            left: "center",
            textStyle: {
              fontSize: 15,
            },
          },

          tooltip: {
            trigger: "axis",
          },
          legend: {
            data: "体重(kg)",
          },
          grid: {
            left: "0%",
            right: "0%",
            bottom: "3%",
            containLabel: true,
          },
          // toolbox: {
          //   feature: {
          //     saveAsImage: {},
          //   },
          // },
          xAxis: {
            type: "category",
            data: this.dataList7[index].xAxisList7,
          },
          yAxis: {
            type: "value",
            // maxInterval:5,
            // min: Math.min(
            //   ...Array.from(new Set(this.dataList7[index].yAxisList7))
            // ),
            // max: Math.max(
            //   ...Array.from(new Set(this.dataList7[index].yAxisList7))
            // ),
            // splitNumber: 40,
          },
          series: [
            {
              symbol: "none", //取消折点圆圈
              // markPoint: {
              //   data: [
              //     {
              //       name: "最大值",
              //       type: "max",
              //     },
              //     {
              //       name: "最小值",
              //       type: "min",
              //     },
              //   ],
              // },
              data: this.dataList7[index].yAxisList7,
              type: "line",
            },
          ],
        });
      });
    },
    getDatas8() {
      this.dataList8.sort(this.compare("index"));
      this.pigdataArry.map((item, index) => {
        this.category8 = echarts.init(
          document.getElementById("category" + index + "2"),
          "walden"
        );
        this.category8.setOption({
          color: [
            "#507E32",
            "#618B46",
            "#73995B",
            "#84A66F",
            "#95B384",
            "#A6C098",
            "#B8CEAD",
            "#C9DBC1",
          ],
          title: {
            text: "饲料消耗量(g)",
            left: "center",
            textStyle: {
              fontSize: 15,
            },
          },
          tooltip: {
            trigger: "axis",
          },
          legend: {
            data: "饲料消耗量(g)",
          },
          grid: {
            left: "0%",
            right: "0%",
            bottom: "3%",
            containLabel: true,
          },
          // toolbox: {
          //   feature: {
          //     saveAsImage: {},
          //   },
          // },
          xAxis: {
            type: "category",
            data: this.dataList8[index].xAxisList8,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              symbol: "none", //取消折点圆圈
              // markPoint: {
              //   data: [
              //     {
              //       name: "最大值",
              //       type: "max",
              //     },
              //     {
              //       name: "最小值",
              //       type: "min",
              //     },
              //   ],
              // },
              data: this.dataList8[index].yAxisList8,
              type: "bar",
            },
          ],
        });
      });
    },
    getDatas9() {
      this.category9 = echarts.init(this.$refs.category9, "walden");
      let pigNameArr = [];
      this.pigFarmsOptions.forEach((item) => pigNameArr.push(item.facname));
      let seriesData = [];
      this.pigFarmsOptions.forEach((item, index) => {
        seriesData.push({
          name: item.facname,
          value: [
            this.controlArry[index].value,
            this.pigdataArry[index].value,
            !isEmpty(this.dataList8[index].yAxisList8)
              ? Number(this.dataList8[index].yAxisList8.slice(-1)[0] / 1000)
              : 0,
            !isEmpty(this.dataList7[index].yAxisList7)
              ? Number(this.dataList7[index].yAxisList7.slice(-1)[0])
              : 0,
          ],
        });
      });

      this.category9.setOption({
        color: [
          "#507E32",
          "#618B46",
          "#73995B",
          "#84A66F",
          "#95B384",
          "#A6C098",
          "#B8CEAD",
          "#C9DBC1",
        ],
        legend: {
          data: pigNameArr,
          orient: "vertical",
          left: "auto",
          show: false,
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          extraCssText:
            "white-space: normal; word-break: break-all;z-index:100000;",
          textStyle: {
            width: 30,
            height: 30,
            fontSize: 8,
          },
        },
        // grid: {
        //   left: "8%",
        //   right: "8%",
        //   bottom: "3%",
        //   top: 20,
        //   containLabel: true,
        // },
        radar: {
          // shape: 'circle',
          indicator: [
            { name: "测定站数量", max: 10 },
            { name: "存栏量", max: 100 },

            { name: "昨日饲料消耗量(kg)", max: 1000 },
            { name: "平均体重(kg)", max: 250 },
          ],
          center: ["50%", "50%"],
          radius: 70,
        },
        series: [
          {
            type: "radar",
            tooltip: {
              trigger: "item",
              textStyle: {
                width: 30,
                height: 30,
                fontSize: 8,
              },
            },
            data: seriesData,
            // data: [
            //   {
            //     value: [4200, 100, 20000],
            //     name: "巨星",
            //   },
            //   {
            //     value: [5000, 200, 28000],
            //     name: "达州",
            //   },
            //   {
            //     value: [5000, 100, 28000],
            //     name: "朋城",
            //   },
            // ],
          },
        ],
      });
    },
  },
};
</script>
<style scoped>
.empty {
  /* border-radius: 5px; */
  width: 20px;
  /* height: 30px; */
  /* background: #1ecd98; */
  text-align: center;
  /* line-height: 30px; */
  display: inline-block;
}
.short {
  /* border-radius: 5px; */
  width: 5px;
  /* height: 30px; */
  /* background: #1ecd98; */
  text-align: center;
  /* line-height: 30px; */
  display: inline-block;
}
.year {
  border-radius: 5px;
  width: 40px;
  height: 30px;
  background: #1ecd98;
  color: white;
  text-align: center;
  line-height: 30px;
  display: inline-block;
}
.month {
  color: white;
  border-radius: 5px;
  width: 25px;
  height: 30px;
  background: #1ecd98;
  text-align: center;
  line-height: 30px;
  display: inline-block;
}

.dat {
  color: white;
  border-radius: 5px;
  width: 25px;
  height: 30px;
  background: #1ecd98;
  text-align: center;
  line-height: 30px;
  display: inline-block;
}
.day {
  color: white;
  border-radius: 5px;
  width: 35px;
  height: 30px;
  background: #1ecd98;
  text-align: center;
  line-height: 30px;
  display: inline-block;
}
.h {
  color: white;
  border-radius: 5px;
  width: 25px;
  height: 30px;
  background: #1ecd98;
  text-align: center;
  line-height: 30px;
  display: inline-block;
}
.m {
  color: white;
  border-radius: 5px;
  width: 25px;
  height: 30px;
  background: #1ecd98;
  text-align: center;
  line-height: 30px;
  display: inline-block;
}
.s {
  color: white;
  border-radius: 5px;
  width: 25px;
  height: 30px;
  background: #1ecd98;
  text-align: center;
  line-height: 30px;
  display: inline-block;
}
#pie_style {
  position: absolute;
  top: 31px;
  right: 0;
  pointer-events: none;
}
.strong {
  display: block;
  line-height: 1;
  margin-bottom: 10px;
  font-size: 50px;
  color: rgb(30, 205, 152);
  /* text-shadow: 0 1px 0 #c9cfce,0 2px 0 #bcc2c2,0 3px 0 #afb6b6,0 4px 0 #a4adac,0 5px 0 #9fa8a7,0 6px 0 #99a3a2,0 7px 0 #97a1a0,0 8px 0 #949e9d,0 0 5px rgba(0,0,0,.05),0 1px 3px rgba(0,0,0,.2),0 3px 5px rgba(0,0,0,.2),0 5px 10px rgba(0,0,0,.2),0 10px 10px rgba(0,0,0,.2),0 20px 20px rgba(0,0,0,.3); */
}
.board-page {
  min-height: 100%;
  padding: 0 20px;
  /* font-family: Helvetica; */
  /* background: #181c2e url(/farms_public/page/farmBoard/board-bg.jpg) no-repeat 0 0;
    background-size: cover; */
}
.board-header {
  position: relative;
  line-height: 48px;
  height: 58px;
  padding: 10px 20px;
  font-size: 15px;
  text-align: right;
  color: rgb(41, 41, 41);
}
.clearfix {
  *zoom: 1;
}
.board-title {
  position: absolute;
  left: 0;
  margin-top: 0px;
  /* top: 10px; */
  width: 100%;
  text-align: center;
  line-height: 48px;
  font-size: 25px;
  /* font-weight: 400; */
  color: #6d6d6d;
  /* color: rgb(41, 41, 41); */
}
.fl {
  float: left;
}
.el-tabs__nav-wrap::after {
  /* content: ""; */
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  /* background-color: #dfe4ed; */
  z-index: 1;
}

/* a {
  color: #525c66;
  text-decoration: none;
} */
.top-10 {
  /* float: left; */
  width: 340px;
  margin-left: -30px;
  margin-top: -20px;
  /* margin-top: 106px;
			margin-left: 118px;
			background: #fff;
			border: 1px solid #FFF;
			box-shadow: #d0d0d0 1px 1px 10px 0px; */
}

.top-10::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

.top-10 ul {
  counter-reset: section;
}

.top-10 li {
  float: left;
  width: 260px;
  /* border-bottom: 1px solid #b8c2cc; */
  line-height: 26px;
  height: 26px;
  overflow: hidden;
  color: #525c66;
  font-size: 14px;
  /* counter-reset: section; */
}

.top-10 li:before {
  counter-increment: section;
  content: counter(section);
  display: inline-block;
  padding: 0 12px;
  margin-right: 10px;
  height: 18px;
  line-height: 18px;
  background: #b8c2cc;
  color: #fff;
  border-radius: 3px;
  font-size: 9px;
}

.top-10 li:nth-child(1):before {
  /* background: #0164b4; */
  background: #507e32;
}

.top-10 li:nth-child(2):before {
  background: #507e32;
}

.top-10 li:nth-child(3):before {
  background: #507e32;
}

#rank-more {
  float: left;
  width: 100%;
  margin-top: 8px;
  text-align: center;
}

.span-h7 {
  font-size: 14px;
  color: #b8c2cc;
  font-weight: 300;
}
</style>
