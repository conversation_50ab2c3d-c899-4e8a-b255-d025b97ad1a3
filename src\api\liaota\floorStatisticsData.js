import request from '@/utils/request'

// /system/louceng/huizong     get      参数  datefrom，dateto，如果是某一天两个参数设为同一个日期

export function listLoucengDate(query) {
  return request({
    url: '/system/louceng/huizong',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】列表
export function listLouceng(query) {
  return request({
    url: '/system/louceng/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getLouceng(id) {
  return request({
    url: '/system/louceng/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addLouceng(data) {
  return request({
    url: '/system/louceng',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateLouceng(data) {
  return request({
    url: '/system/louceng',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delLouceng(id) {
  return request({
    url: '/system/louceng/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportLouceng(query) {
  return request({
    url: '/system/louceng/export',
    method: 'get',
    params: query
  })
}