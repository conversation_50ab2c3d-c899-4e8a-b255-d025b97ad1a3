<template>
  <div id="box">
    <datav />
  </div>
</template>

<script>
import datav from "./components/datav/index.vue";
// import store from "@/store";
export default {
  name: "box",
  components: {
    datav,
  },
  data() {
    return {};
  },
  // mounted() {
  //   store.dispatch("app/toggleDevice", "mobile");
  //   //    store.dispatch('app/closeSideBar', { withoutAnimation: true })
  // },
  // beforeDestroy() {
  //   store.dispatch("app/toggleDevice", "desktop");
  // },
};
</script>

<style lang="less">
#box {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  width: 100%;
  height: 100%;
}
</style>
