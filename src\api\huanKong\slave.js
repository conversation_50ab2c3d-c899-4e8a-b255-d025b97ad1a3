import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listSlave(query) {
  return request({
    url: '/system/slave/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getSlave(id) {
  return request({
    url: '/system/slave/' + id,
    method: 'get'
  })
}

// 查询【环控器树型结构】详细
export function treeselectEnv(query) {
    return request({
        url: '/system/home/<USER>',
        method: 'get',
        params: query
    })
}

// 新增【请填写功能名称】
export function addSlave(data) {
  return request({
    url: '/system/slave',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateSlave(data) {
  return request({
    url: '/system/slave',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delSlave(id) {
  return request({
    url: '/system/slave/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportSlave(query) {
  return request({
    url: '/system/slave/export',
    method: 'get',
    params: query
  })
}