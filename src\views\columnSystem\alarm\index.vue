<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mname')" prop="mname">
        <el-input
          v-model="queryParams.mname"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.dalarmdate')" prop="dalarmdate">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.dalarmdate"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="$t('common.pleaseChoose')"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.nalarmid')" prop="nalarmid">
        <el-input
          v-model="queryParams.nalarmid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mmemo')" prop="mmemo">
        <el-input
          v-model="queryParams.mmemo"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.nstate')" prop="nstate">
        <el-input
          v-model="queryParams.nstate"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          {{ $t("common.reset") }}</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['columnSystem:alarm:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['columnSystem:alarm:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['columnSystem:alarm:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['columnSystem:alarm:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="alarmList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('common.serialNumber')"
        align="center"
        prop="id"
      />
      <el-table-column
        :label="$t('columnSystem.nindex')"
        align="center"
        prop="nindex"
      />
      <el-table-column
        :label="$t('columnSystem.mname')"
        align="center"
        prop="mname"
      />
      <el-table-column
        :label="$t('columnSystem.dalarmdate')"
        align="center"
        prop="dalarmdate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dalarmdate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('columnSystem.nalarmid')"
        align="center"
        prop="nalarmid"
      />
      <el-table-column
        :label="$t('columnSystem.mmemo')"
        align="center"
        prop="mmemo"
      />
      <el-table-column
        :label="$t('columnSystem.nstate')"
        align="center"
        prop="nstate"
      />
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['columnSystem:alarm:edit']"
            >{{ $t("common.update") }}</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['columnSystem:alarm:remove']"
            >{{ $t("common.delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【设备报警记录】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
          <el-input
            v-model="form.nindex"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.mname')" prop="mname">
          <el-input
            v-model="form.mname"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.dalarmdate')" prop="dalarmdate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.dalarmdate"
            type="date"
            value-format="yyyy-MM-dd"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.nalarmid')" prop="nalarmid">
          <el-input
            v-model="form.nalarmid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.mmemo')" prop="mmemo">
          <el-input
            v-model="form.mmemo"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.nstate')" prop="nstate">
          <el-input
            v-model="form.nstate"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAlarm,
  getAlarm,
  delAlarm,
  addAlarm,
  updateAlarm,
  exportAlarm,
} from "@/api/columnSystem/alarm";

export default {
  name: "Alarm",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【设备报警记录】表格数据
      alarmList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nindex: null,
        mname: null,
        dalarmdate: null,
        nalarmid: null,
        mmemo: null,
        nstate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        nindex: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
        mname: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
        nalarmid: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【设备报警记录】列表 */
    getList() {
      this.loading = true;
      listAlarm(this.queryParams).then((response) => {
        this.alarmList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nindex: null,
        mname: null,
        dalarmdate: null,
        nalarmid: null,
        mmemo: null,
        nstate: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("columnSystem.addAlarm");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAlarm(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("columnSystem.updateAlarm");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAlarm(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            addAlarm(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("columnSystem.sureCancelAlarm") +
          `"` +
          ids +
          `"` +
          this.$t("common.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delAlarm(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("columnSystem.sureExportAlarm"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportAlarm(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>