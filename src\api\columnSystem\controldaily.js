import request from '@/utils/request'
// 查询【请填写功能名称】列表
export function listControldaily(query) {
  return request({
    url: '/fenlan/controldaily/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getControldaily(id) {
  return request({
    url: '/fenlan/controldaily/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addControldaily(data) {
  return request({
    url: '/fenlan/controldaily',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateControldaily(data) {
  return request({
    url: '/fenlan/controldaily',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delControldaily(id) {
  return request({
    url: '/fenlan/controldaily/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportControldaily(query) {
  return request({
    url: '/fenlan/controldaily/export',
    method: 'get',
    params: query
  })
}
