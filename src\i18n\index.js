import Vue from 'vue';
import VueI18n from 'vue-i18n';
const defaultSettings = require('../../src/settings.js')
import localeEn from '../../node_modules/element-ui/lib/locale/lang/en.js'
import localeZh from '../../node_modules/element-ui/lib/locale/lang/zh-CN.js'
import localeRu from '../../node_modules/element-ui/lib/locale/lang/ru-RU.js'
Vue.use(VueI18n);
// 引入各个语言配置文件
import zh from './lang/zh';
import en from './lang/en';
import ru from './lang/ru';
// 创建vue-i18n实例i18n
const i18n = new VueI18n({
    // 设置默认语言
    locale: localStorage.getItem('locale') || defaultSettings.defaultLanguage,  // 语言标识
    // 添加多语言（每一个语言标示对应一个语言文件）
    messages: {
        zh:{...zh,...localeZh},
        en:{...en,...localeEn},
        ru:{...ru,...localeRu},
    },
     // 隐藏警告
  silentTranslationWarn: true
})
// Vue.use(Element, {
//     i18n: (key, value) => i18n.t(key, value) // 在注册Element时设置i18n的处理方法
// });
// 暴露i18n
export default i18n;