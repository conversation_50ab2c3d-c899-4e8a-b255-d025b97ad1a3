import request from '@/utils/request'


//单个下发参数保存
// /system/singleparam

export function environParaMqtt(data) {
  ///envmqtt/envParamDown
  return request({
    url: '/envmqtt/envParamDown',
    method: 'post',
    data: data
  })
}

// 查询【请填写功能名称】列表
export function listParam(query) {
  return request({
    url: '/system/singleparam/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getParam(id) {
  return request({
    url: '/system/singleparam/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addParam(data) {
  return request({
    url: '/system/singleparam',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateParam(data) {
  return request({
    url: '/system/singleparam',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delParam(id) {
  return request({
    url: '/system/singleparam/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportParam(query) {
  return request({
    url: '/system/singleparam/export',
    method: 'get',
    params: query
  })
}