


<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="监控节点id" prop="sta">
        <el-input
          v-model="queryParams.sta"
          placeholder="请输入监控节点id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监控节点名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入监控节点名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网关id" prop="swid">
        <el-input
          v-model="queryParams.swid"
          placeholder="请输入网关id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="通信状态" prop="net">
        <el-select
          v-model="queryParams.net"
          placeholder="请选择通信状态"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="dict in slavenetworkList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="上传日期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['warning:outage:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['warning:outage:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['warning:outage:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['warning:outage:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>  -->

    <el-table
      v-loading="loading"
      :data="msgList"
      @sort-change="handleSortChange"
    >
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="监控节点id" align="center" prop="sta" />
      <el-table-column
        label="监控节点名称"
        width="255"
        align="center"
        prop="name"
      />
      <el-table-column label="网关id" align="center" prop="swid" />

      <el-table-column label="断电" align="center" prop="dd">
        <template slot-scope="scope">
          <el-tag
            effect="dark"
            :type="scope.row.dd == 0 ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.dd == 0 ? "正常" : "异常" }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="缺项" align="center" prop="qx">
        <template slot-scope="scope">
          <el-tag
            effect="dark"
            :type="scope.row.qx == 0 ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.qx == 0 ? "正常" : "异常" }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="环境异常" align="center" prop="hj">
        <template slot-scope="scope">
          <el-tag
            effect="dark"
            :type="scope.row.hj == 0 ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.hj == 0 ? "正常" : "异常" }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="通信状态" align="center" prop="net">
        <template slot-scope="scope">
          <el-tag
            effect="dark"
            :type="scope.row.net === 'ok' ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.net === "ok" ? "在线" : "离线" }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        label="上传时间"
        align="center"
        prop="uptime"
        width="180"
        sortable="custom"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.uptime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['warning:outage:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['warning:outage:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【断电缺项】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="68px">
        <el-form-item label="猪场id" prop="faid">
          <el-input v-model="form.faid" placeholder="请输入猪场id" />
        </el-form-item>
        <el-form-item label="监控节点id" prop="sta">
          <el-input v-model="form.sta" placeholder="请输入监控节点id" />
        </el-form-item>
        <el-form-item label="监控节点名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入监控节点名称" />
        </el-form-item>
        <el-form-item label="网关id" prop="swid">
          <el-input v-model="form.swid" placeholder="请输入网关id" />
        </el-form-item>
        <el-form-item label="通信状态" prop="net">
          <el-select
            v-model="form.net"
            placeholder="请选择通信状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in slavenetworkList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传时间" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.uptime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择上传时间"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="断电" prop="dd">
          <el-input v-model="form.dd" placeholder="请输入断电" />
        </el-form-item>
        <el-form-item label="缺相" prop="qx">
          <el-input v-model="form.qx" placeholder="请输入缺相" />
        </el-form-item>
        <el-form-item label="环境异常" prop="hj">
          <el-input v-model="form.hj" placeholder="请输入环境异常" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMsg,
  getMsg,
  delMsg,
  addMsg,
  updateMsg,
  exportMsg,
} from "@/api/warning/outage";

export default {
  name: "Outage",
  components: {},
  data() {
    return {
      dataInterval: 3 * 1000,
      //设备状态
      slavenetworkList: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【断电缺项】表格数据
      msgList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        orderByColumn: "uptime",
        isAsc: "desc",
        pageNum: 1,
        pageSize: 10,
        faid: null,
        sta: null,
        name: null,
        swid: null,
        dd: null,
        qx: null,
        hj: null,
        colStr1: null,
        colStr2: null,
        net: null,
        uptime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  created() {
    this.getList();
    this.getDicts("hk_work_status").then((response) => {
      this.slavenetworkList = response.data;
    });
  },
  beforeDestroy() {
    clearInterval(this.updateInterval);
  },
  methods: {
    // 处理排序变化
    handleSortChange({ column, prop, order }) {
      // 转换排序参数格式
      this.queryParams.orderByColumn = prop;
      this.queryParams.isAsc = order === "ascending" ? "asc" : "desc";

      // 重新获取数据
      this.getList();
    },
    /** 查询【断电缺项】列表 */
    getList() {
      this.loading = true;
      this.queryParams.faid = this.$store.state.settings.nowPigFarm;
      listMsg(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        this.msgList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        faid: null,
        sta: null,
        name: null,
        swid: null,
        dd: null,
        qx: null,
        hj: null,
        colStr1: null,
        colStr2: null,
        net: null,
        uptime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【断电缺项】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMsg(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【断电缺项】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateMsg(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.faid = this.$store.state.settings.nowPigFarm;
            addMsg(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【断电缺项】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delMsg(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【断电缺项】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportMsg(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>
