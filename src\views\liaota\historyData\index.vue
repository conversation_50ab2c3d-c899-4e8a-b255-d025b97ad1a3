<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('liaota.towerName')" prop="slavename">
        <el-select
          v-model="queryParams.slavename"
          :placeholder="$t('liaota.enterTowerName')"
          clearable
          size="small"
        >
          <el-option
            v-for="item in slaveOptions"
            :key="item.id"
            :label="item.slavename"
            :value="item.slavename"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="$t('liaota.gatewayId')" prop="switchid">
        <el-input
          v-model="queryParams.switchid"
          :placeholder="$t('liaota.enterGatewayId')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item :label="$t('liaota.pigHouseId')" prop="homeid">
        <el-input
          v-model="queryParams.homeid"
          :placeholder="$t('liaota.enterPigHouseId')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item :label="$t('liaota.selectDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('liaota.startDate')"
          :end-placeholder="$t('liaota.endDate')"
          @change="handleDateClick"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item :label="$t('common.uploadTime')" prop="dateLine">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.dateLine"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="$t('common.chooseUploadTime')"
        >
        </el-date-picker> -->
      <!-- <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.uptime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择打料开始时间">
        </el-date-picker> -->
      <!-- </el-form-item> -->
      <el-form-item
        :label="$t('liaota.towerWorkingStatus')"
        prop="slavenetwork"
      >
        <el-select
          v-model="queryParams.slavenetwork"
          :placeholder="$t('liaota.choosetTowerWorkingStatus')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="dict in slavenetworkList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="$t('liaota.feedType')" prop="feedtype">
        <el-input
          v-model="queryParams.feedtype"
          :placeholder="$t('liaota.enterFeedType')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item :label="$t('liaota.weight')" prop="weight">
        <el-input
          v-model="queryParams.weight"
          :placeholder="$t('liaota.enterWeight')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item :label="$t('liaota.alarmInformation')" prop="alarmmsg">
        <el-input
          v-model="queryParams.alarmmsg"
          :placeholder="$t('liaota.enterAlarmInformation')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <span v-if="this.activeName == 'second'">
        <el-form-item :label="$t('liaota.inorout')" prop="inorout">
          <el-select
            v-model="queryParams.inorout"
            :placeholder="$t('liaota.choosetInorout')"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          >
            <el-option
              v-for="dict in towerInOutList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('liaota.floor')" prop="homename">
          <el-select
            v-model="queryParams.homename"
            :placeholder="$t('liaota.enterFloor')"
            clearable
            size="small"
          >
            <el-option
              v-for="item in floorList"
              :key="item.id"
              :label="item.homename"
              :value="item.homename"
              @keyup.enter.native="handleQuery"
            ></el-option>
          </el-select>
        </el-form-item>
      </span>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="$t('liaota.weightTable')" name="first" :lazy="true">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:lthistory:add']"
              >{{ $t("common.add") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:lthistory:edit']"
              >{{ $t("common.update") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:lthistory:remove']"
              >{{ $t("common.delete") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:lthistory:export']"
              >{{ $t("common.export") }}</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
        <el-table
          v-loading="loading"
          :data="slaveList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            :label="$t('common.serialNumber')"
            align="center"
            prop="id"
          />
          <el-table-column
            :label="$t('liaota.towerId')"
            align="center"
            prop="slaveid"
          />
          <el-table-column
            :label="$t('liaota.towerName')"
            align="center"
            prop="slavename"
            width="180"
          />
          <el-table-column
            :label="$t('liaota.gatewayId')"
            align="center"
            prop="switchid"
          />
          <!-- <el-table-column
        :label="$t('liaota.pigHouseId')"
        align="center"
        prop="homeid"
      /> -->
          <el-table-column
            :label="$t('huanKong.pigFarmID')"
            align="center"
            prop="mfactory"
          />
          <el-table-column
            :label="$t('common.uploadTime')"
            align="center"
            prop="uptime"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.uptime, "{y}-{m}-{d} {h}:{i}:{s}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('liaota.towerWorkingStatus')"
            align="center"
            prop="slavenetwork"
          >
            <template slot-scope="scope">
              <el-tag
                effect="dark"
                :type="scope.row.slavenetwork === 'bad' ? 'danger' : 'success'"
                disable-transitions
                >{{
                  scope.row.slavenetwork === "bad"
                    ? $t("liaota.offline")
                    : $t("liaota.online")
                }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('liaota.weight')"
            align="center"
            prop="weight"
          />
          <!-- <el-table-column
            :label="$t('liaota.feedType')"
            align="center"
            prop="feedtype"
          /> -->
          <el-table-column
            :label="$t('liaota.alarmInformation')"
            align="center"
            prop="alarmmsg"
            show-overflow-tooltip
          />
          <el-table-column
            :label="$t('common.operate')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:lthistory:edit']"
                >{{ $t("common.update") }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:lthistory:remove']"
                >{{ $t("common.delete") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <template v-if="checkPermi(['system:inout:list'])">
        <el-tab-pane
          :label="$t('liaota.inOutTable')"
          name="second"
          :lazy="true"
        >
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['system:inout:add']"
                >{{ $t("common.add") }}</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['system:inout:edit']"
                >{{ $t("common.update") }}</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['system:inout:remove']"
                >{{ $t("common.delete") }}</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['system:inout:export']"
                >{{ $t("common.export") }}</el-button
              >
            </el-col>
            <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>
          <el-row :gutter="30" type="flex" justify="space-around">
            <el-col :span="6">
              <div class="box box1">
                <div>{{ $t("liaota.totalFeedAmount") }}</div>
                <div class="text1">{{ this.inSum }} kg</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="box box2">
                <div>{{ $t("liaota.totalDischargeAmount") }}</div>
                <div class="text2">{{ this.outSum }} kg</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="box box3">
                <div>{{ $t("liaota.numberOfFeedings") }}</div>
                <div class="text3">
                  {{ this.inSumTimes }} {{ $t("liaota.time") }}
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="box box4">
                <div>{{ $t("liaota.numberOfDischarges") }}</div>
                <div class="text4">
                  {{ this.outSumTimes }} {{ $t("liaota.time") }}
                </div>
              </div>
            </el-col>
          </el-row>
          <el-table
            v-loading="loading"
            :data="slaveInOutList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              :label="$t('common.serialNumber')"
              align="center"
              prop="id"
            />
            <!-- <el-table-column
            :label="$t('liaota.towerId')"
            align="center"
            prop="slaveid"
          /> -->
            <el-table-column
              :label="$t('liaota.towerName')"
              align="center"
              prop="slavename"
            />
            <!-- <el-table-column
            :label="$t('liaota.floorId')"
            align="center"
            prop="homeid"
          /> -->
            <el-table-column
              :label="$t('liaota.floor')"
              align="center"
              prop="homename"
            />
            <!-- <el-table-column
            :label="$t('liaota.gatewayId')"
            align="center"
            prop="switchid"
          /> -->
            <el-table-column
              :label="$t('huanKong.pigFarmID')"
              align="center"
              prop="mfactory"
            />
            <el-table-column
              :label="$t('common.uploadTime')"
              align="center"
              prop="uptime"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{
                  parseTime(scope.row.uptime, "{y}-{m}-{d} {h}:{i}:{s}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('liaota.towerWorkingStatus')"
              align="center"
              prop="slavenetwork"
            >
              <template slot-scope="scope">
                <el-tag
                  effect="dark"
                  :type="scope.row.slavenetwork === 'ok' ? 'success' : 'danger'"
                  disable-transitions
                  >{{
                    scope.row.slavenetwork === "ok"
                      ? $t("liaota.online")
                      : $t("liaota.offline")
                  }}</el-tag
                >
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('liaota.inorout')"
              align="center"
              prop="inorout"
              :formatter="towerInOutListFormat"
            />
            <el-table-column
              :label="$t('liaota.weightInOut')"
              align="center"
              prop="weight"
            />
            <el-table-column
              :label="$t('liaota.alarmInformation')"
              align="center"
              prop="alarmmsg"
              show-overflow-tooltip
            />
            <el-table-column
              :label="$t('common.operate')"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:inout:edit']"
                  >{{ $t("common.update") }}</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:inout:remove']"
                  >{{ $t("common.delete") }}</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-tab-pane>
      </template>
    </el-tabs>
    <!-- 添加或修改【料塔历史数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('liaota.towerName')" prop="slaveid">
          <el-select
            v-model="form.slaveid"
            :placeholder="$t('liaota.enterTowerName')"
            @change="changeSlavename"
          >
            <el-option
              v-for="item in slaveOptions"
              :key="item.id"
              :label="item.slavename"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('liaota.gatewayId')" prop="switchid">
          <el-input
            v-model="form.switchid"
            :placeholder="$t('liaota.enterGatewayId')"
          />
        </el-form-item>

        <el-form-item :label="$t('common.uploadTime')" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 380px"
            v-model="form.uptime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.chooseUploadTime')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          :label="$t('liaota.towerWorkingStatus')"
          prop="slavenetwork"
        >
          <el-select
            v-model="form.slavenetwork"
            :placeholder="$t('liaota.choosetTowerWorkingStatus')"
          >
            <el-option
              v-for="dict in slavenetworkList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <span v-if="this.activeName == 'first'">
          <el-form-item :label="$t('liaota.weight')" prop="weight">
            <el-input
              v-model="form.weight"
              :placeholder="$t('liaota.enterWeight')"
            />
          </el-form-item>
        </span>
        <span v-else>
          <!-- <el-form-item :label="$t('liaota.floorId')" prop="homeid">
            <el-input
              v-model="form.homeid"
              :placeholder="$t('liaota.enterfloorId')"
            />
          </el-form-item> -->
          <el-form-item :label="$t('liaota.floor')" prop="homeid">
            <!-- <el-input
              v-model="form.homename"
              :placeholder="$t('liaota.enterFloor')"
            /> -->
            <el-select
              v-model="form.homeid"
              :placeholder="$t('liaota.enterFloor')"
              @change="changeHomename"
            >
              <el-option
                v-for="item in floorList"
                :key="item.id"
                :label="item.homename"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('liaota.inorout')" prop="inorout">
            <el-select
              v-model="form.inorout"
              :placeholder="$t('liaota.choosetInorout')"
            >
              <el-option
                v-for="dict in towerInOutList"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="Number(dict.dictValue)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('liaota.weightInOut')" prop="weight">
            <el-input
              v-model="form.weight"
              :placeholder="$t('liaota.enterWeight')"
            />
          </el-form-item>
        </span>

        <el-form-item :label="$t('liaota.alarmInformation')" prop="alarmmsg">
          <el-input
            v-model="form.alarmmsg"
            :placeholder="$t('liaota.enterAlarmInformation')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listLiaotaHistory,
  getLiaotaHistory,
  delLiaotaHistory,
  addLiaotaHistory,
  updateLiaotaHistory,
  exportLiaotaHistory,
} from "@/api/liaota/history";
import { listSlave } from "@/api/liaota/slave";
import {
  listInout,
  getInout,
  addInout,
  updateInout,
  delInout,
  exportInout,
} from "@/api/liaota/inout";
import { listHome } from "@/api/liaota/floor";
import { isEmpty } from "lodash";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

export default {
  name: "Slave",
  components: {},
  data() {
    return {
      inSum: 0,
      outSum: 0,
      inSumTimes: 0,
      outSumTimes: 0,
      activeName: "first",
      //料塔值集
      slaveOptions: [],
      //设备状态
      slavenetworkList: [],
      //加放料
      towerInOutList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【料塔历史数据】表格数据
      slaveList: [],
      // 【料塔进放料数据】表格数据
      slaveInOutList: [],
      //楼层
      floorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        slaveid: null,
        slavename: null,
        switchid: null,
        homeid: null,
        uptime: null,
        slavenetwork: null,
        weight: null,
        alarmmsg: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        slaveid: [
          {
            required: true,
            message: this.$t("liaota.towerIdNotNull"),
            trigger: "blur",
          },
        ],
        slavename: [
          {
            required: true,
            message: this.$t("liaota.towerNameNotNull"),
            trigger: "blur",
          },
        ],
        switchid: [
          {
            required: true,
            message: this.$t("liaota.gatewayIdNotNull"),
            trigger: "blur",
          },
        ],
        uptime: [
          {
            required: true,
            message: this.$t("liaota.uptimeNotNull"),
            trigger: "blur",
          },
        ],
        homeid: [
          {
            required: true,
            message: this.$t("liaota.floorIdNotNull"),
            trigger: "blur",
          },
        ],
        homename: [
          {
            required: true,
            message: this.$t("liaota.floorNotNull"),
            trigger: "blur",
          },
        ],
        weight: [
          {
            required: true,
            message: this.$t("liaota.weightInOutNotNull"),
            trigger: "blur",
          },
        ],
        inorout: [
          {
            required: true,
            message: this.$t("liaota.inoroutNotNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  mounted() {
    listSlave({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.slaveOptions = response.rows;
      }
    );
    listHome({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.floorList = response.rows;
      }
    );
  },
  created() {
    this.getList();
    this.getDicts("hk_work_status").then((response) => {
      this.slavenetworkList = response.data;
    });
    this.getDicts("tower_in_out").then((response) => {
      this.towerInOutList = response.data;
    });
  },
  methods: {
    checkPermi,
    changeHomename(value) {
      this.form.homename = this.floorListFormat(value);
    },
    changeSlavename(value) {
      this.form.slavename = this.slaveOptionsFormat(value);
    },

    slaveOptionsFormat(value) {
      let arrObjFilter = this.slaveOptions
        .filter((ele) => ele.id == value)
        .map((ele) => {
          return ele.slavename;
        });
      return arrObjFilter[0];
    },
    floorListFormat(value) {
      let arrObjFilter = this.floorList
        .filter((ele) => ele.id == value)
        .map((ele) => {
          return ele.homename;
        });
      return arrObjFilter[0];
    },
    // 字典状态字典翻译
    towerInOutListFormat(row, column) {
      return this.selectDictLabel(this.towerInOutList, row.inorout);
    },
    handleClick(tab, event) {
      if (tab.name == "first") this.getList();
      if (tab.name == "second") this.getList();
    },
    /** 查询【料塔历史数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      if (this.activeName == "first") {
        listLiaotaHistory(
          this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
        ).then((response) => {
          this.slaveList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else if (this.activeName == "second") {
        listInout(this.queryParams).then((response) => {
          this.slaveInOutList = response.rows;
          this.total = response.total;
          this.slaveInOutList.map((item) => {
            if (item.inorout == "1") {
            }
          });
          let arrInFilter = this.slaveInOutList
            .filter((ele) => ele.inorout == "1")
            .map((ele) => {
              return ele.weight;
            });
          let arrOutFilter = this.slaveInOutList
            .filter((ele) => ele.inorout == "0")
            .map((ele) => {
              return ele.weight;
            });
          this.inSum = !isEmpty(arrInFilter)
            ? arrInFilter.reduce((a, b) => a + b)
            : "0";
          this.outSum = !isEmpty(arrOutFilter)
            ? arrOutFilter.reduce((a, b) => a + b)
            : "0";
          this.inSumTimes = !isEmpty(arrInFilter) ? arrInFilter.length : "0";
          this.outSumTimes = !isEmpty(arrOutFilter) ? arrOutFilter.length : "0";

          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        slaveid: null,
        slavename: null,
        switchid: null,
        homeid: null,
        uptime: null,
        slavenetwork: null,
        weight: null,
        alarmmsg: null,
        inorout: null,
        homename: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;

      if (this.activeName == "first") {
        this.title = this.$t("liaota.addLiaoTaHistory");
      } else if (this.activeName == "second") {
        this.title = this.$t("liaota.addLiaoTaInOut");
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      if (this.activeName == "first") {
        getLiaotaHistory(id).then((response) => {
          this.form = response.data;
          this.open = true;
          this.title = this.$t("liaota.modLiaoTaHistory");
        });
      } else if (this.activeName == "second") {
        getInout(id).then((response) => {
          this.form = response.data;
          this.open = true;
          this.title = this.$t("liaota.modLiaoTaInOut");
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      if (this.activeName == "first") {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            if (this.form.id != null) {
              updateLiaotaHistory(this.form).then((response) => {
                this.msgSuccess(this.$t("common.modifiedSuccess"));
                this.open = false;
                this.getList();
              });
            } else {
              this.form.mfactory = this.$store.state.settings.nowPigFarm;
              addLiaotaHistory(this.form).then((response) => {
                this.msgSuccess(this.$t("common.addSuccess"));
                this.open = false;
                this.getList();
              });
            }
          }
        });
      } else if (this.activeName == "second") {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            if (this.form.id != null) {
              updateInout(this.form).then((response) => {
                this.msgSuccess(this.$t("common.modifiedSuccess"));
                this.open = false;
                this.getList();
              });
            } else {
              this.form.mfactory = this.$store.state.settings.nowPigFarm;
              addInout(this.form).then((response) => {
                this.msgSuccess(this.$t("common.addSuccess"));
                this.open = false;
                this.getList();
              });
            }
          }
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      if (this.activeName == "first") {
        this.$confirm(
          this.$t("liaota.sureCancelLiaoTaHistory") +
            '"' +
            ids +
            '"' +
            this.$t("liaota.dataItem"),
          this.$t("common.warn"),
          {
            confirmButtonText: this.$t("common.determine"),
            cancelButtonText: this.$t("common.cancel"),
            type: "warning",
          }
        )
          .then(function () {
            return delLiaotaHistory(ids);
          })
          .then(() => {
            this.getList();
            this.msgSuccess(this.$t("common.deleteSuccess"));
          });
      } else if (this.activeName == "second") {
        this.$confirm(
          this.$t("liaota.sureCancelLiaoTaInOut") +
            '"' +
            ids +
            '"' +
            this.$t("liaota.dataItem"),
          this.$t("common.warn"),
          {
            confirmButtonText: this.$t("common.determine"),
            cancelButtonText: this.$t("common.cancel"),
            type: "warning",
          }
        )
          .then(function () {
            return delInout(ids);
          })
          .then(() => {
            this.getList();
            this.msgSuccess(this.$t("common.deleteSuccess"));
          });
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      if (this.activeName == "first") {
        this.$confirm(
          this.$t("liaota.sureExportLiaoTaHistory"),
          this.$t("common.warn"),
          {
            confirmButtonText: this.$t("common.determine"),
            cancelButtonText: this.$t("common.cancel"),
            type: "warning",
          }
        )
          .then(function () {
            return exportLiaotaHistory(queryParams);
          })
          .then((response) => {
            this.download(response.msg);
          });
      } else if (this.activeName == "second") {
        this.$confirm(
          this.$t("liaota.sureExportLiaoTaInOut"),
          this.$t("common.warn"),
          {
            confirmButtonText: this.$t("common.determine"),
            cancelButtonText: this.$t("common.cancel"),
            type: "warning",
          }
        )
          .then(function () {
            return exportInout(queryParams);
          })
          .then((response) => {
            this.download(response.msg);
          });
      }
    },
  },
};
</script>
<style scoped>
.box {
  border-radius: 6px;
  height: 150px;
  color: #fff;
  font-size: 20px;
  box-sizing: border-box;
  margin: 20px 0px;
  padding: 20px;
}
.box1 {
  background-color: #1ecd98;
}
.box2 {
  background-color: #409eff;
}
.box3 {
  background-color: #e6a23c;
}
.box4 {
  background-color: #f56c6c;
}
.text1 {
  padding: 20px 35px;
  font-size: 40px;
  text-align: right;
}
.text2 {
  padding: 20px 35px;
  font-size: 40px;
  text-align: right;
}
.text3 {
  padding: 20px 35px;
  font-size: 40px;
  text-align: right;
}
.text4 {
  padding: 20px 35px;
  font-size: 40px;
  text-align: right;
}
</style>
