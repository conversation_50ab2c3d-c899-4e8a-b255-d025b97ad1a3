// import ru from '../node_modules/element-ui/lib/locale/lang/ru-RU.js'
// const ru = require('../node_modules/element-ui/lib/locale/lang/ru-RU.js')
// const zh = require('../node_modules/element-ui/lib/locale/lang/zh-CN.js')
module.exports = {
  /**
   * 默认语言
   */
  // defaultLanguage: 'ru', //zh
  defaultLanguage: 'zh',

  /**
   * 默认路由
   * [{
      path: 'index',
      component: (resolve) => require(['@/views/monitor/map'], resolve),
      name: 'index',
      meta: {
        title: 'menu.frontPage',
        icon: 'dashboard',
        noCache: true,
        affix: true
      }
    }]
    [{
        name: "HomeData",
        path: "env_homeData",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/homeData'], resolve),
        meta: {
          title: "menu.pigHouseRealtimeMonitoring",
          icon: "chart",
          noCache: false
        }
      }]
   */
  // defaultRouter: [{
  //   name: "HomeData",
  //   path: "index",
  //   hidden: false,
  //   component: (resolve) => require(['@/views/huanKong/homeData'], resolve),
  //   meta: {
  //     title: "menu.pigHouseRealtimeMonitoring",
  //     icon: "chart",
  //     noCache: false
  //   }
  // }],

  // defaultRouter: [{
  //   path: 'index',
  //   component: (resolve) => require(['@/views/monitor/map'], resolve),
  //   name: 'index',
  //   meta: {
  //     title: 'menu.frontPage',
  //     icon: 'dashboard',
  //     noCache: true,
  //     affix: true
  //   }
  // }],

  defaultRouter: [{
      path: 'index',
      component: (resolve) => require(['@/views/system/menuShow/index'], resolve),
      name: 'index',
      meta: {
        title: 'menu.frontPage',
        icon: 'dashboard',
        noCache: true,
        affix: true
      },
    },
    {
      path: '/module',
      component: (resolve) => require(['@/views/system/menuShow/children'], resolve),
      name: 'module',
      meta: {
        noCache: true,
        title: '模块菜单',
        icon: ''
      }
    }
  ],


  /**
   * 网站标题 '科诺智慧养猪云平台'
   */
  // title: 'Облачная платформа для выращивания свиней Kono Smart',
  title: '科诺智慧养猪云平台',



  /**
   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light
   */
  sideTheme: 'theme-dark',

  /**
   * 是否系统布局配置
   */
  showSettings: false,

  /**
   * 是否显示选择猪场
   */
  showSwitching: false,

  /**
   * 是否有新的消息通知
   */
  hasNotification: false,

  /**
   * 当前猪场
   */
  nowPigFarm: null,

  /**
   * 是否显示 tagsView
   */
  tagsView: true,

  /**
   * 是否固定头部
   */
  fixedHeader: true,

  /**
   * 是否显示logo
   */
  sidebarLogo: true,

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description Need show err logs component.
   * The default is only used in the production env
   * If you want to also use it in dev, you can pass ['production', 'development']
   */
  errorLog: 'production'
}
