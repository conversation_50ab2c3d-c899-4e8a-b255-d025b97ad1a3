import request from '@/utils/request'

// 查询【请填写功能名称】列表

export function listFactoryByUser(query) {
  return request({
    url: '/system/factory/byUser',
    method: 'get',
    params: query
  })
}

export function listFactory(query) {
  return request({
    url: '/system/factory/list',
    method: 'get',
    params: query
  })
}

export function listFactorybyUserAndType(query) {
  return request({
    url: '/system/factory/byUserAndType',
    method: 'get',
    params: query
  })
}


///dev-api/system/factory/byUserAndType

// 查询【请填写功能名称】详细
export function getFactory(id) {
  return request({
    url: '/system/factory/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addFactory(data) {
  return request({
    url: '/system/factory',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateFactory(data) {
  return request({
    url: '/system/factory',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delFactory(id) {
  return request({
    url: '/system/factory/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportFactory(query) {
  return request({
    url: '/system/factory/export',
    method: 'get',
    params: query
  })
}


export function listUserandactory(query) {
  return request({
    url: '/system/useandactory/list',
    method: 'get',
    params: query
  })
}

export function selectuserbyfacid(query) {
  return request({
    url: '/system/useandactory/selectuserbyfacid',
    method: 'get',
    params: query
  })
}
