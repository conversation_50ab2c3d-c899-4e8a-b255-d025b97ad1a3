const en = {
  common: {
    exception: 'Exception',
    systemInterface: 'System interface',
    timeout: 'System interface request timeout',
    networkError: 'Backend interface connection exception',
    reRegister: 're-register',
    systemHint: 'System hint',
    logInAgain: 'Your login status has expired, you can stay on this page or log in again',
    loadingResources: 'Loading system resources, please be patient',
    refresh: 'Refresh',
    hideSearch: 'Hide search',
    showSearch: 'Show search',
    previousPage: 'Previous page',
    nextPage: 'Next page',
    saveAll: 'SaveAll',
    save: 'Save',
    search: 'Search',
    reset: 'Reset',
    add: 'Add',
    update: 'Update',
    delete: 'Delete',
    export: 'Export',
    import: 'Import',
    batchUpdate: 'Batch Update',
    serialNumber: 'SerialNumber',
    operate: 'Operate',
    uploadTime: 'UploadTime',
    chooseUploadTime: 'ChooseUploadTime',
    determine: 'Determine',
    cancel: 'Cancel',
    modifiedSuccess: 'Successfully modified ',
    addSuccess: 'Added successfully',
    deleteSuccess: 'Successfully deleted ',
    warn: 'Warn ',
    close: 'Close',
    confirmClose: 'confirm to close?',
    submit: 'Submit',
    pleaseInput: "Please enter",
    pleaseChoose: "Please select",
    notNull: 'This field cannot be empty',
    dataItem: 'Data item? ',
    startDate: 'Start date',
    endDate: 'End date',
  },
  huanKong: {
    nowTem: "Current Temp",
    targetTem: "Target Temp",
    updateTime: "UT", //Update Time
    online: "Online",
    offline: 'Offline',
    pigHouseName: 'Pig house name',
    choosePigHouseName: 'Please enter the name of the pigsty',
    pigHouseID: 'Pig house ID',
    choosePigHouseID: 'Please enter the name of the pigsty',
    pigFarmID: 'Pig farm ID',
    choosePigFarmID: 'Please enter the pig farm ID',
    pigHouseNameNotNull: "Pig house name cannot be empty",
    pigHouseIDNotNull: "Pig house name cannot be empty",
    addPigHouse: "Add Pig House",
    modifyPigHouse: "Modify [Pig House]",
    sureCancelPigHouse: 'Are you sure to delete [Pig House] The number is ',
    sureExportPigHouse: 'Are you sure to export all the [Pig House] data items?',
    fanSwitchSet: 'Fan switch is set',
    minimumVentilationPhase: 'Minimum ventilation phase',
    temperatureStage: 'Temperature',
    connectErr: 'bad',
    editTip: 'Before you modify the parameters perform a refresh operation to ensure that the current page of data is the latest!',
    environmentalControl: "Environmental control",
    controlConfiguration: "Environmental control equipment configuration",
    controlData: "Environmental control historical data",
    controlReport: "Environmental control summary report",
    pigHousesReal: "Real-time monitoring of pig houses",
    nowControlNotStarted: "The current environmental control equipment is not started!",
    temperature: "Temperature",
    humidity: "Humidity",
    waterLevel: "Water level",
    energyUsed: "Energy used",
    temperatureCurve: "Temperature curve",
    fixedFrequencyFan: "Fixed frequency fan",
    inverterFan: "Inverter fan",
    curtain1: "Curtain 1",
    curtain2: "Curtain 2",
    curtain3: "Curtain 3",
    curtain4: "Curtain 4",
    choosePigHouse: 'Please choose a pig house',
    pigHouseUnit: 'Unit', //Pig house unit
    gatewayId: 'Gateway id ',
    chooseGatewayId: 'Please enter the gateway id',
    envControlId: 'Environmental control id ',
    chooseEnvControlId: 'Please enter the control id ',
    envControlName: 'Environmental control name ',
    chooseEnvControlName: 'Please enter the control name ',
    pigHouseID: 'Pig house id ',
    choosePigHouseID: 'Please enter the pig house id',
    envWorkStatus: "Status", //Environmental control working status
    chooseEnvWorkStatus: 'Please select the working status of environmental control ',
    enterTemperature: 'Please enter temperature ',
    enterHumidity: 'Please enter humidity ',
    water: 'water',
    enterWater: 'Please enter the amount of water ',
    water1: 'Curtain 1',
    water2: 'Curtain 2 ',
    water3: 'Curtain 3',
    water4: 'Curtain 4 ',
    enterWater1: "Please enter  curtain 1 ",
    enterWater2: 'Please enter  curtain 2',
    enterWater3: 'Please enter  curtain 3',
    enterWater4: 'Please enter  curtain 4',
    fixedFan1: "Fixed frequency fan 1 ",
    fixedFan2: 'Fixed frequency fan 2',
    fixedFan3: "Fixed frequency fan 3",
    fixedFan4: 'Fixed frequency fan 4',
    fixedFan5: "Fixed frequency fan 5",
    fixedFan6: 'Fixed frequency fan 6',
    fixedFan7: "Fixed frequency fan 7",
    fixedFan8: 'Fixed frequency fan 8',
    fixedFan9: "Fixed frequency fan 9",
    fixedFan10: 'Fixed frequency fan 10',
    fixedFan11: "Fixed frequency fan 11",
    fixedFan12: 'Fixed frequency fan 12',
    fixedFan13: "Fixed frequency fan 13",
    fixedFan14: 'Fixed frequency fan 14',
    fixedFan15: "Fixed frequency fan 15",
    fixedFan16: 'Fixed frequency fan 16',
    fixedFan17: "Fixed frequency fan 17",
    fixedFan18: 'Fixed frequency fan 18',
    fixedFan19: "Fixed frequency fan 19",
    fixedFan20: 'Fixed frequency fan 20',
    fixedFan21: "Fixed frequency fan 21",
    fixedFan22: 'Fixed frequency fan 22',
    fixedFan23: "Fixed frequency fan 23",
    fixedFan24: 'Fixed frequency fan 24',
    enterFixedFan1: "Please enter fixed frequency fan 1 ",
    enterFixedFan2: 'Please enter fixed frequency fan 2',
    enterFixedFan3: "Please enter fixed frequency fan 3",
    enterFixedFan4: 'Please enter fixed frequency fan 4',
    enterFixedFan5: "Please enter fixed frequency fan 5",
    enterFixedFan6: 'Please enter fixed frequency fan 6',
    enterFixedFan7: "Please enter fixed frequency fan 7",
    enterFixedFan8: 'Please enter fixed frequency fan 8',
    enterFixedFan9: "Please enter fixed frequency fan 9",
    enterFixedFan10: 'Please enter fixed frequency fan 10',
    enterFixedFan11: "Please enter fixed frequency fan 11",
    enterFixedFan12: 'Please enter fixed frequency fan 12',
    enterFixedFan13: "Please enter fixed frequency fan 13",
    enterFixedFan14: 'Please enter fixed frequency fan 14',
    enterFixedFan15: "Please enter fixed frequency fan 15",
    enterFixedFan16: 'Please enter fixed frequency fan 16',
    fan1: "Inverter fan 1 ",
    fan2: 'Inverter fan 2',
    fan3: "Inverter fan 3",
    fan4: 'Inverter fan 4',
    fan5: "Inverter fan 5",
    fan6: 'Inverter fan 6',
    fan7: "Inverter fan 7",
    fan8: 'Inverter fan 8',
    enterFan1: "Please enter the frequency conversion fan 1",
    enterFan2: 'Please enter the frequency conversion fan 2',
    enterFan3: "Please enter the frequency conversion fan 3",
    enterFan4: 'Please enter the frequency conversion fan 4',
    alarmInformation: 'Alarm information ',
    alarmNo: 'No alarm information',
    enterAlarmInformation: 'Please enter the alarm information ',
    envControlIdNotNull: 'Environmental control id cannot be empty ',
    envControlNameNotNull: 'The environmental control name cannot be empty ',
    gatewayIdNotNull: 'Gateway id cannot be empty ',
    pigHouseIDNotNull: 'The pig house id cannot be empty ',
    addEnvSet: 'Add [Environmental Control Equipment Configuration] ',
    modEnvSet: 'Modify [Environmental Control Equipment Configuration] ',
    sureCancelEnvSet: 'Are you sure to delete [Environmental Control Equipment Configuration] The number is ',
    dataItem: 'Data item? ',
    sureExportEnvSet: 'Are you sure to export all the data items of [Environmental Control Equipment Configuration]?',
    addHistoryData: "Add【historical data】 ",
    modHistoryData: "Modify【historical data】 ",
    sureCancelHistoryData: 'Are you sure to delete [historical data] The number is ',
    sureExportHistoryData: 'Are you sure to export all [historical data] data items? ',
    selectDate: 'Select date ',
    startDate: 'start date ',
    endDate: 'End date ',
    period: 'period ',
    choosePeriod: 'Please select a time period ',
    viewMonitoring: 'View real-time monitoring',
    parameterRealTimeIssued: 'Parameters are issued in real time',
    parameterIssuance: 'Parameter issuance',
    parameterName: 'parameter name',
    parameterNameENG: 'parameter name(Eng)',
    parameterNameRUS: 'parameter nameRUS)',
    enterParameterName: 'Please enter the parameter name',
    parameterType: 'Parameter Type',
    enterParameterType: 'Please enter the parameter type',
    point: 'Point',
    enterPoint: 'Please enter the parameter Point',
    parameterValue: 'Parameter value',
    enterParameterValue: 'Please enter the parameter value',
    nowStage: 'Stage', //Now Stage
    stage: 'Stage',
    enterStage: 'Please enter the stage',
    stageOrdering: 'Stage ordering',
    enterStageOrdering: 'Please enter the stage ordering',
    batchEditing: 'Batch editing',
    refresh: 'Refresh',
    requestTimedOut: 'Request timed out! ! !',
    stageNotNull: "Stage cannot be empty",
    stageOrderingNotNull: "Stage ordering cannot be null",
    parameterNameNotNull: "The parameter name cannot be empty",
    pointNotNull: "Point cannot be empty",
    parameterTypeNotNull: "Type cannot be empty",
    addParameterDictionary: "Add [controller parameter]",
    modifyParameterDictionary: "Modify [controller parameter]",
    sureDeleteParameterDictionary: 'Are you sure to delete [controller parameter dictionary] number is ',
    sureExportParameterDictionary: "Are you sure to export all [controller parameter dictionary] data items?",
    addStageParameters: "Add [stage parameters]",
    modifyStageParameters: "Modify [stage parameters]",
    sureDeleteStageParameters: 'Are you sure to delete [Phase parameter dictionary] The number is ',
    sureExportStageParameters: "Are you sure to export all data items in [Phase Parameter Dictionary]?",
    querying: 'Querying',
    atLeastOne: 'Please select at least one item first',
  },
  electric: {
    metername: 'Meter name',
    enterMetername: 'Please enter meter name',
    meterNameNotNull: "Meter name cannot be empty",
    meterIdNotNull: "The meter id cannot be empty",
    energy: 'Electricity',
    enterEnergy: 'Please enter electricity',
    meterid: 'Meter ID',
    meterWorkStatus: 'Meter working status',
    choosetMeterWorkingStatus: 'Please select the working status of the meter',
    addMeterConfig: 'Add [meter configuration information]',
    modMeterConfig: 'Modify [Meter Configuration Information]',
    sureCancelMeterConfig: 'Whether to confirm to delete [meter configuration information] number is',
    sureExportMeterConfig: 'Whether to confirm to export all the [Meter configuration information] data items?',
    addMeterHistoryConfig: 'Add 【Meter History Data】',
    modMeterHistoryConfig: 'Modify 【Historical Data of Electricity Meter】',
    sureCancelMeterHistoryConfig: 'Whether to confirm to delete the [Historical data of the meter] No. is',
    sureExportMeterHistoryConfig: 'Whether to confirm to export all the data items of [Historical Data of Electricity Meter]?',
    materUse: 'battery usage'
  },
  liaota: {
    time: "Time",
    totalFeedAmount: "Total feed amount",
    totalDischargeAmount: "Total discharge amount",
    numberOfFeedings: "Number of feedings",
    numberOfDischarges: "Number of discharges",
    floorDataStatistics: 'Floor usage statistics',
    floorDataTable: 'Floor material data',
    addfloorTableData: 'Add [floor material data]',
    modfloorTableData: 'Modification of [floor material data]',
    sureCancelfloorTableData: 'Whether to confirm the deletion of the [Floor Usage Data] number as ',
    sureExportfloorTableData: 'Confirmation of exporting all [Floor Material Data] data items?',
    addfloor: 'Add [floor]',
    modfloor: 'Modify [floor]',
    sureCancelfloor: 'Whether to confirm to delete the [floor] number is',
    sureExportfloor: 'Whether to confirm to export all [floor] data items?',
    inOutTable: 'Feeding and unloading data',
    weightTable: 'real-time weight data',
    inOutLine: 'Add and discharge curve',
    weightLine: 'real-time weight curve',
    uptimeNotNull: 'Upload time cannot be empty',
    floorIdNotNull: 'Floor id cannot be empty',
    floorNotNull: 'Floor name cannot be empty',
    inoroutNotNull: 'Add and discharge can not be empty',
    weightInOutNotNull: 'Weight cannot be empty',
    floorId: 'floor id',
    enterfloorId: 'Please enter the floor id',
    floor: 'Floor name',
    enterFloor: 'Please enter the floor name',
    addLiaoTaInOut: 'Add【Tower loading and unloading data】',
    modLiaoTaInOut: 'Modify [Tower loading and unloading data]',
    sureCancelLiaoTaInOut: 'Whether to confirm to delete the [Tower loading and unloading data] No. is',
    sureExportLiaoTaInOut: 'Whether to confirm to export all the data items of [Tower loading and unloading data]?',
    inFeed: "Cutting amount",
    outFeed: 'Discharge amount',
    inorout: 'Feeding and discharging behavior',
    choosetInorout: 'Please choose whether to add or discharge',
    weightInOut: "Weight (kg)",
    feedtypeNotNull: 'Feed type cannot be empty',
    volumeNotNull: 'Capacity cannot be empty',
    feedType: 'Feed type',
    enterFeedType: 'Please enter feed type',
    volume: 'Volume',
    enterVolume: 'Please enter volume',
    online: "Online",
    offline: 'Offline',
    max: "maximum value",
    min: 'minimum value',
    all: 'total',
    towerUse: 'Real-time weight data',
    currentWeight: "Current weight ",
    upperLimit: 'Upper limit ',
    lowerLimit: 'Lower limit ',
    notConnected: 'Not connected ',
    towerName: "Tower name ",
    enterTowerName: 'Please enter the name of the material tower ',
    gatewayId: 'Gateway id ',
    enterGatewayId: 'Please enter the gateway id ',
    pigHouseId: 'Pig house id ',
    enterPigHouseId: 'Please enter the pig house id ',
    towerWorkingStatus: 'Tower working status ',
    choosetTowerWorkingStatus: 'Please select the working status of the material tower ',
    weight: 'Real-time weight (kg) ',
    enterWeight: 'Please enter the weight ',
    alarmInformation: 'Alarm information ',
    enterAlarmInformation: 'Please enter the alarm information ',
    towerId: 'Tower id ',
    enterTowerId: 'Please enter the tower id',
    towerNameNotNull: 'Tower name cannot be empty ',
    gatewayIdNotNull: 'Gateway id cannot be empty ',
    pigHouseIDNotNull: 'The pig house id cannot be empty ',
    towerIdNotNull: 'Tower id cannot be empty ',
    addTowerConfig: 'Add [material tower configuration information] ',
    modTowerConfig: 'Modify [material tower configuration information] ',
    sureCancelTowerConfig: 'Are you sure to delete [material tower configuration information] The number is ',
    sureExportTowerConfig: 'Are you sure to export all the data items of [material tower configuration information] ?',
    dataItem: 'Data item?',
    addLiaoTaHistory: 'Add [Historical Data of Material Tower] ',
    modLiaoTaHistory: 'Modify [Historical Data of Material Tower] ',
    sureCancelLiaoTaHistory: 'Are you sure to delete [material tower historical data] The number is ',
    sureExportLiaoTaHistory: 'Are you sure to export all the data items of [material tower historical data]? ',
    selectDate: 'Select date ',
    startDate: 'start date ',
    endDate: 'End date ',
    period: 'Period ',
    choosePeriod: 'Please select a time period ',
  },
  content: {
    main: "this is content"
  },
  menu: {
    account: "account",
    password: "password",
    verificationCode: "verification code",
    rememberPassword: "remember password",
    logIn: "Log in",
    loggingIn: "logging in...",
    register: "register",
    registrationCode: "registration code",
    enterRegistrationCode: "Please enter the registration code",
    registrationCodeNotNull: "Registration code cannot be empty",
    usernameNotNull: "Username can not be empty",
    passwordNotNull: "password can not be blank",
    verificationCodeNotNull: "verification code must be filled",
    smartPigRaisingCloudPlatform: 'Log in',
    remainingDays: "Remaining days of registration",
    day: "days",
    currentFarm: "Current farm",
    layoutSize: "Layout size",
    switchFarms: "Switch farms",
    switchLanguage: "Switch language",
    zh: "Chinese",
    en: "English",
    ru: "Russian",
    personalCenter: 'Personal center',
    layoutSettings: "Layout settings",
    signOut: "Sign out",
    themeStyleSettings: 'Theme style settings',
    themeColor: "Theme color",
    systemLayoutConfiguration: "System layout configuration",
    turnOn: 'Turn on',
    fixed: "Fixed",
    show: "Show",
    switchCurrentFarm: "Switch Current Farm",
    selectFarmName: "Please select current farm",
    farmNameNotNull: "Farm name cannot be empty",
    nowFarmNameNotNull: 'The current farm must not be empty, please select the current farm and confirm!',
    addPigFarm: "Add 【Pig Farm】",
    increasePigFarm: "Increase pig farm",
    farmName: 'Farm name',
    enterFarmName: 'Please enter the farm name',
    hint: "hint",
    logOut: 'Are you sure you want to log out and log out of the system?',
    personalInformation: 'personal information',
    userName: "user name",
    phoneNumber: 'phone number',
    userMailbox: 'User mailbox',
    department: "Department",
    ownRole: 'own role',
    dateCreated: 'Date created',
    basicInformation: "basic information",
    changePassword: 'change Password',
    userNickname: 'User Nickname',
    gender: "gender",
    male: 'male',
    female: 'female',
    usernameCannotBeEmpty: "Username cannot be empty",
    phoneNumberCannotBeBlank: 'Phone number can not be blank',
    emailAddressCannotBeEmpty: 'Email address cannot be empty',
    enterCorrectEmail: 'Please input the correct email address',
    enterCorrectPhone: 'Please enter the correct phone number',
    enterOldPassword: 'Please enter old password',
    enterNewPassword: 'Please enter a new password',
    enterConfirmPassword: 'Please confirm your password',
    sureTwoEnterMatch: 'The two entered passwords do not match',
    lengthBetween: '6 to 20 characters long',
    clickUploadAvatar: 'Click to upload avatar',
    choose: 'choose',
    modifyAvatar: 'Modify avatar',
    fileFormatError: 'The file format is incorrect, please upload the image type, such as: JPG, PNG suffixed files.',
    oldPassword: "Old Password",
    newPassword: 'NEW Password',
    confirmPassword: 'Confirm Password',
    oldPasswordCannotBeEmpty: "Old password cannot be empty",
    newPasswordCannotBeEmpty: 'New password cannot be empty',
    confirmPasswordCannotBeEmpty: 'confirm password can not be blank',
    address: "address",
    scale: 'scale',
    type: 'type',
    state: "state",
    breedingPig: 'breeding pig',
    sow: "sow",
    columns: 'Columns',
    tower: "Tower",
    precisionFeeding: 'precision feeding',
    offline: 'Offline',
    frontPage: "Front page",
    environmentalControl: "Environmental control",
    pigHouseConfiguration: "Pig house configuration",
    pigHouseRealtimeMonitoring: "Pig house real-time monitoring",
    RingControlParameterAdjustment: "Ring control parameter adjustment",
    envConDeviceConfiguration: "Environment control device configuration",
    envConHistoricalData: "Environmental control historical data",
    envConSummaryReport: "Environmental Control Summary Report",
    stageParameterConfiguration: "Stage parameter configuration",
    singleParameterConfiguration: "Single parameter configuration",
    videoRealtimeMonitoring: "Video real-time monitoring",
    electricityMeter: "Electricity meter",
    realtimeMonitoringElectricityMeters: "Electricity Meter Reading Monitoring",
    meterDeviceConfiguration: "Electricity Meter Device Configuration",
    meterHistoryData: "Electricity Usage Data Collection",
    meterSummaryReport: "Electricity Usage Trend Summary",
    waterSummaryReport: "Water Usage Trend Summary",
    realtimeMonitoringWater: "Water Meter Reading Monitoring",
    waterHistoryData: "Water Usage Data Collection",
    tower: "Tower",
    realtimeMonitoringMaterialTower: "Real-time monitoring of material tower",
    materialTowerEquipmentConfiguration: "Material tower equipment configuration",
    materialTowerHistoricalData: "Material tower historical data",
    towerSummaryReport: "Tower Summary Report",
    boarMeasure: "Breeding pig testing",
    breedPigdata: "Breeding pig information",
    breedOverview: "Overview of pig houses",
    breedMeasureday: "Individual testing report",
    breedDayReport: "Daily testing report",
    breedMeasuredaygather: "Summary report",
    breedSocialAnimal: "Group testing report",
    breedControl: "Controller configuration",
    breedControlV2: "Controller configuration V2",
    breedMeasure: "Feed intake data",
    breedFeeddata: "Feeding data",

    columnSystem: "育肥分栏模块",
    columnSystemOverview: "猪场总览",
    columnSystemDailyweightday: "日汇总",
    columnSystemDailyWeight: "分栏称重记录",
    columnSystemPigdatadaily: "分栏猪管理",
    columnSystemWeightdistribution: "体重分布",
    columnSystemTransactionprocessing: "事务处理",
    columnSystemControldaily: "设备管理",
    columnSystemPrediction: "上市预测",
    columnSystemStationGrowthReport: "分栏站生长报告",
    columnSystemIndividualGrowthReport: "个体生长报告",
    columnSystemZeroprediction: '零耳牌上市预测',
    columnSystemSametime: "同时开启",
    columnSystemAlarm: "报警记录",
  },
  boarMeasure: {
    dragFile: "Drag the file here or ",
    clickUpload: "click to upload",
    hintOnly: "Tip: Only 'xls' or 'xlsx' format files are allowed for import! The imported file must be an updated version based on the exported file, and do not modify the 'Unique Identifier' column!",
    batchUpdateInfo: "Batch Update and Import of Pig Information",
    changeMrfid: "Replace electronic ear tag",
    newMrfid: "Modified electronic ear tag",
    zeroQuery: "Zero Ear Brand Inquiry",
    pleaseEnterColumn: "Please enter field number",
    pleaseEnterColumnFirst: "Please enter  field number first, then ear notch number or electronic ear tag number",
    noRecords: "No records found for the pig",
    noData: "Data not found",
    feedingInterval: "Feed interval (s)",
    inFeed: "Feed amount",
    feedingDate: "Feed date",
    feedTime: "Feeding time",
    unfed: "Not fed",
    fed: "Fed",
    zeroearDistribution: "Zero ear tag allocation",
    unpicked: "Not eating",
    timeout: "Request timed out!!!",
    successfulLaunch: "Successfully sent, please refresh after 5s to see the modification result",
    timingSuccess: "Time synchronization successful",
    pleaseEnterCorrectRange: "Please enter the correct range",
    inactive: "Not activated",
    active: "Activated",
    stop: "Disabled",
    normal: "Normal",
    pleaseInputPigHouseName: "Please enter the name of the pigsty",
    pleaseInput: "Please enter",
    pleaseChoose: "Please select",
    mid: "Ear notch number",
    mrfid: "Electronic ear tag",
    mvariety: "Breed",
    bgender: "Gender",
    dbirthdate: "Date of birth",
    mdorm: "Pigpen number",
    nindex: "Field number",
    mname: "Testing station",
    naddress: "Device address",
    dstartdate: "Start date of testing",
    denddate: "End date of testing",
    nweightStart: "Initial weight (kg) for testing",
    nweightEnd: "Final weight (kg) for testing",
    nweight: "Measured pig weight (kg)",
    ntype: "Testing status",
    remarks: "Remarks",
    mensurationEnd: "Completed",
    mensurationNow: "Measuring",
    addPigdata: "Add breeding pig information",
    updatePigdata: "Update breeding pig information",
    sureCancelPigdata: 'Whether to confirm the deletion of [Breeder Information] No.',
    dataItem: 'The data item',
    cannotbeRecovered: 'After deletion, it will not be possible to recover, if you want to keep the data, you can modify the measurement status!',
    sureExportPigdata: 'Are you sure you want to export all breeding pig data entries?',
    notNull: 'This field cannot be empty',
    sureMensurationEnd: 'Are you sure to confirm the end of testing?',
    mensurationEndSuccess: 'Testing completed successfully.',
    lessThanSomeGrams: 'The number of pigs with a feed intake of less than',
    pigIs: 'grams yesterday is',
    heads: 'heads',
    midIs: 'The ear notch number is',
    nIngestionSToday: 'Feed intake on that day (g)',
    nIngestionSLastday: 'Feed intake yesterday (g)',
    nweightLastday: 'Weight yesterday (kg)',
    basicInformation: 'Basic information',
    measureDays: 'Number of days tested',
    recordDays: 'Number of days recorded',
    allNIngestionS: 'Total feed intake (kg)',
    weightGrow: 'Total weight gain (kg)',
    liaoRouBi: 'Feed to meat ratio',
    dailyData: 'Daily data',
    ningestion: 'Feed intake',
    weight: 'Weight',
    dailyGainWeight: 'Daily weight gain',
    dateOfDetermination: 'Testing date',
    ningestions: 'Total daily feed intake (g)',
    weightKg: 'Weight (kg)',
    nfeednum: 'Daily feed intake frequency',
    nseconds: 'Total daily feed intake time (s)',
    nsecond: 'Total daily feed intake time (h)',
    ningestionG: 'Feed intake (g)',
    ningestionKG: 'Feed intake (kg)',
    dailyGainWeightKg: 'Daily weight gain (kg)',
    weightMid: 'Weight (median) (kg)',
    addMeasureday: "Add individual test information",
    updateMeasureday: "Modify individual test information",
    sureCancelMeasureday: 'Are you sure you want to delete the individual test report with the number?',
    sureExportMeasureday: 'Are you sure you want to export all individual test report data entries?',
    addDayReport: "Add testing daily report",
    updateDayReport: "Modify testing daily report",
    sureCancelDayReport: 'Are you sure you want to delete the testing daily report with the number?',
    sureExportDayReport: 'Are you sure you want to export all testing daily report data entries?',
    sureCancelGatherReport: 'Are you sure you want to delete the testing daily report with the number?',
    sureExportGatherReport: 'Are you sure you want to export all summary report data entries?',
    dataList: 'Data List',
    feedConsumptionChart: 'Feed Consumption Graph',
    averageWeightGraph: 'Average Weight Graph',
    dataAggregation: 'Data Summary',
    measurePigs: 'Number of Tested Pigs',
    allNIngestionG: 'Total Feed Intake (g)',
    intakesNumber: 'Number of Feedings',
    intakesTimeH: 'Feeding Time (h)',
    averageWeightKg: 'Average Weight (kg)',
    feedConsumptionG: 'Feed Consumption (g)',
    measureDaysT: 'Number of Test Days',
    averageDailyFeedIntake: 'Average Daily Feed Intake (kg)',
    allIntakesTimeS: "Total Feeding Time (s)",
    allIntakesTimeH: "Total Feeding Time (h)",
    averageDailyFeedIntakeG: 'Average Daily Feed Intake (g)',
    feedConsumption: 'Feed Consumption',
    startAverageWeight: 'Starting Average Weight (kg)',
    endAverageWeight: 'Ending Average Weight (kg)',
    averageGrowth: 'Average Weight Gain (kg)',
    averageDailyGrowth: 'Average Daily Weight Gain (kg)',
    viewIndividualFeedIntakeData: 'View Individual Feeding Data',
    manualSummary: 'Manual Summary',
    manualSummaryDate: 'Manual Summary Date',
    range: 'Range',
    columnFrom: 'From Pen Number',
    columnTo: 'To Pen Number',
    totalFeedIntake: 'Total Feeding Amount',
    totalWeightGain: 'Total Weight Gain',
    productionPerformance: 'Production Performance',
    startDate: 'Starting Date',
    endDate: 'Ending Date',
    startWeight: 'Starting Weight (kg)',
    endWeight: 'Ending Weight (kg)',
    nearWeight: 'Recent weight(kg)',
    weightGain: 'Weight Gain (kg)',
    sureExportAllData: 'Confirm Exporting All Data Entries?',
    parameterDistribution: 'Parameter Issuance',
    topologyNodeType: 'Topology Node Type',
    gatewayID: 'Gateway ID',
    naddressup: 'Upload Address',
    nserial: 'Device Serial Number',
    nstatus: 'Device Status',
    nversion: 'Software Version',
    ncorrect: 'Feed Trough Calibration Value',
    ncorrectmin: 'Minimum Feed Trough Calibration Value',
    ncorrectmax: 'Maximum Feed Trough Calibration Value',
    DPCkg: 'DPC (kg)',
    DPCG: 'DPC (g)',
    DPC: 'DPC',
    DPCmin: 'Minimum DPC',
    DPCmax: 'Maximum DPC',
    nsurpluskg: 'Minimum Feed Residual Amount (kg)',
    nsurplus: 'Minimum Feed Residual Amount',
    npulse: 'Feeding Pulse Value',
    nindivkg: 'Individual Scale Calibration Value (kg)',
    nindiv: 'Individual Scale Calibration Value',
    nindivmin: 'Minimum Individual Scale Calibration Value',
    nindivmax: 'Maximum Individual Scale Calibration Value',
    nindivnull: 'Individual Scale Tare Weight',
    ncorrectnull: 'Feed Trough Tare Weight',
    addControl: "Add Controller Configuration",
    updateControl: "Modify Controller Configuration",
    sureCancelControl: 'Confirm deletion of Controller Configuration with ID',
    sureExportControl: 'Confirm exporting all Controller Configuration data entries?',
    onlyShowControl: 'Show only Controllers',
    refresh: 'Refresh',
    saveDetails: 'Modify one parameter at a time, click Save √ after modification to complete the issue. Each modification must be made at an interval of more than 5s, after changing the parameter, please refresh it at an interval of 5s.',
    addFeedData: "Add Feeding Data",
    updateFeedData: "Modify Feeding Data",
    sureCancelFeedData: 'Confirm deletion of Feeding Data with ID',
    sureExportFeedData: 'Confirm exporting all Feeding Data entries?',
    intakesTime: 'Feeding Time',
    intakesStartTime: 'Feeding Start Time',
    intakesEndTime: 'Feeding End Time',
    intakesTimesS: 'Feeding Duration (s)',
    nfriweight: 'Initial Feed Trough Weight (g)',
    nsecweight: 'Final Feed Trough Weight (g)',
    tankInitialWeight: 'Initial Feed Trough Weight',
    tankEndWeight: 'Final Feed Trough Weight',
    nnum: 'Number of Feedings',
    addMeasure: "Add Feeding Data",
    updateMeasure: "Modify Feeding Data",
    sureCancelMeasure: 'Confirm deletion of Feeding Data with ID',
    sureExportMeasure: 'Confirm exporting all Feeding Data entries?',
    sureExportBreedSocialAnimal: 'Confirm exporting all Group Animal Measurement Summary Report entries?',
    tizhongfanwei: 'Weight range',
  },
  columnSystem: {
    remark: '备注',
    columnMode: "分栏中",
    outLan: "出栏",
    sureOutLan: "是否确认出栏?",
    outLanSuccess: "出栏操作成功",
    nindex: "栏号",
    mname: "分栏站",
    dalarmdate: "报警日期",
    nalarmid: "报警ID",
    mmemo: "故障类型",
    nstate: "消警记录",
    notNull: '不能为空',
    addAlarm: '添加【设备报警记录】',
    updateAlarm: '修改【设备报警记录】',
    sureCancelAlarm: '是否确认删除【设备报警记录】编号为"',
    sureExportAlarm: '是否确认导出所有【设备报警记录】数据项?',

    mdorm: "猪舍号",
    pleaseEnterDeptName: "请输入猪舍名称",
    onlyShowControl: '只显示控制器',
    topologyNodeType: '拓扑节点类型',
    naddress: '设备地址',
    naddressup: '主机地址',
    nserial: '设备序列号',
    nstatus: '设备状态',
    nversion: '软件版本',
    nworktype: '工作模式',
    ndirect: '开门方向',
    nsceen: '上市筛选',
    ntrend: '挑选筛选',
    ntimeopen: '分栏门开启时间',
    ntimereopen: '入口门重开时间',
    ntimedelay: '入口门延迟时间',
    nweighttime: '称重时间',
    npignums: '存栏量',
    basicInform: '基本信息',
    switchid: '网关id',
    mmemo: '备注',
    timeParameter: '时间参数',
    pleaseEnterNtimeopen: '请输入分栏门开启时间(0.2-5秒，默认0.5秒)',
    ntimeclose: '分栏门关闭时间',
    pleaseEnterNtimeclose: '请输入分栏门关闭时间(0.2-5秒，默认0.5秒)',
    pleaseEnterNtimereopen: '请输入入口门重开时间(0.2-5秒，默认0.5秒)',
    pleaseEnterNtimedelay: '请输入入口门延迟时间(0-10秒默认1秒)',
    nweightdelay: '称重延时时间',
    pleaseEnterNweightdelay: '请输入称重延时时间(0-10秒，默认2秒)',
    pleaseEnterNweighttime: '请输入称重时间(0-60秒，默认30秒)',
    calibrationAndWeight: '校准及重量',
    nindivnull: '分栏秤空重',
    pleaseEnterNindivnull: '请输入分栏秤空重(单位0.1公斤，0-200)',
    nindivweight: '分栏秤校准重量',
    pleaseEnterNindivweight: '请输入分栏秤校准重量(单位公斤 10-100)',
    nindiv: '分栏秤校准值',
    pleaseEnterNindiv: '请输入分栏秤校准值(60-120)',
    nweightstart: '称重触发重量',
    pleaseEnterNweightstart: '请输入称重触发重量(5-200,默认10公斤)',
    columnProperties: '分栏属性',
    nworktypeValue: '分栏秤工作模式',
    ndirectValue: '分栏门开门方向',
    nsceenValue: '上市分栏筛选值',
    pleaseEnterNsceenValue: '请输入上市分栏筛选值(1-255默认10)',
    ntrendValue: '挑选分栏筛选值',
    columns: '分栏',
    ngroupweight1: '轻群体重',
    ngroupweight2: '重群体重',
    ncolumnpct: '轻群分栏重量百分比',
    ncolumnweight: '重群分栏重量百分比',
    fatteningClusters: '育肥群组',
    ngroupweight3: '上市体重',
    nmarketweight2: '挑选体重上限',
    nmarketweight1: '挑选体重下限',
    nlightpct: '轻群百分比',
    nmidweight: '中群百分比',
    nheavypct: '重群百分比',
    ncorrect: '料槽校准值',
    nsurplus: '饲料最低剩余量',
    npulse: '投料脉冲值',
    nindiv: '个体秤校准值',
    nindivnull: '个体秤空重',
    ncorrectnull: '料槽空重',
    pleaseEnterCorrectRange: "请输入正确的范围",
    timingSuccess: "校时成功",
    addControl: '添加【控制器配置】',
    parameterDistribution: '参数下发',
    requestTimedOut: '请求超时！！！',
    updateControl: '修改【控制器配置】',
    sureCancelControl: '是否确认删除【控制器配置】编号为"',
    sureExportControl: '是否确认导出所有【设备报警记录】数据项是否确认导出所有【控制器配置】数据项?',

    mid: '耳缺号',
    mrfid: "电子耳牌",
    weightDate: "称重日期",
    dataType: '数据类型',
    weight: "体重(kg)",
    ntemp: "温度",
    addDailyWeight: '添加【分栏称重记录】',
    updateDailyWeight: '修改【分栏称重记录】',
    sureCancelDailyWeight: '是否确认删除【分栏称重记录】编号为"',
    sureExportDailyWeight: '是否确认导出所有【分栏称重记录】数据项?',

    queryDate: "查询日期",
    groupSet: '分组设置',
    percentage: '百分比',
    percentageAnd: '百分比(%)',
    visits: '访问量',
    dailySummaryData: '日汇总数据',
    pleaseEnterWeight: '请输入体重：单位0.1公斤',
    Group1: '小于该值的为Group1',
    Group3: '大于该值的为Group3',
    cohort: '存栏量',
    total: '总量',
    pleaseFirstEnterWeightDate: '请先输入称重日期',
    pleaseFirstEnterColumn: '请先输入栏号和日期范围',
    addDailyWeightDay: '添加【日汇总】',
    updateDailyWeightDay: '修改【日汇总】',
    sureCancelDailyWeightDay: '是否确认删除【日汇总】编号为"',
    sureExportDailyWeightDay: '是否确认导出所有【日汇总】数据项?',
    titleGroupSet: '分组体重设置',

    measureDays: '测定天数',
    lowWeight: '最低体重',
    heightWeight: '最高体重',
    dailyGainWeight: '日增重',
    dailyData: '日数据',
    dateOfDetermination: '测定日期',
    weightTab: "体重",
    noPigsUnderThisPen: '该栏下没有分栏猪',
    addIndividualGrowthReport: '添加【个体生长报告】',
    updateIndividualGrowthReport: '修改【个体生长报告】',
    sureCancelIndividualGrowthReport: '是否确认删除【个体生长报告】编号为"',
    sureExportIndividualGrowthReport: '是否确认导出所有【个体生长报告】数据项?',
    noRecords: "的猪没有找到记录",
    pleaseEnterColumnFirst: "请先输入栏号，再输入耳缺号或者电子耳牌号",

    nowColumnCohort: "当前分栏站的存栏量为",
    lessThanFeedNum: '昨天采食次数低于',
    pigIs: '次的猪有',
    feedTimesToday: '今日进食次数',
    feedTimesYest: '昨日进食次数',
    nWeightToday: '今日称重(kg)',
    nWeightYest: '昨日称重(kg)',

    ntype: "分栏状态",
    ddateinkjet: "喷墨日期",
    nearWeight: '最近体重(kg)',
    transaction: "事务处理",
    mreasoninkjet: "处理原因",
    ninkjet: "是否喷墨",
    ndepart: "是否分离",
    addPigdataDaily: '添加【分栏猪数据】',
    updatePigdataDaily: '修改【分栏猪数据】',
    sureCancelPigdataDaily: '是否确认删除【分栏猪数据】编号为"',
    sureExportPigdataDaily: '是否确认导出所有【分栏猪数据】数据项?',
    transactionAddSuccess: "事务处理新增成功",

    targetWeight: '目标出栏体重',
    targetWeightLow: '最小体重',
    targetWeightHigh: '最大体重',
    filteringScope: '筛选范围',
    listingDate: '上市日',
    intervalDays: '间隔天数',
    growthCalculations: '生长计算依据',
    adgRelyDays: 'ADG值基于过去天数',
    isAdgStable: 'ADG固定值',
    digital: '数据',
    sure: '确认',
    targerDay: '上市日期',
    lowWeightNum: '较轻',
    targetNum: '上市',
    highWeightNum: '较重',
    lowWeightPct: '较轻(%)',
    targetWeightPct: '上市(%)',
    highWeightPct: '较重(%)',

    nsametimeFrom: '开始日期',
    nsametimeTo: '结束日期',
    recordList: '记录列表',
    nsametime: '开门时间',
    ntimes: '延续时间',
    accruedTime: '累积时间',
    addSameTime: '添加【同时开启】',
    updateSameTime: '修改【同时开启】',
    sureCancelSameTime: '是否确认删除【同时开启】编号为"',
    sureExportSameTime: '是否确认导出所有【同时开启】数据项?',

    date: '日期',
    dataList: '数据列表',
    midWeight: '中位值',
    lightWeight: '轻群',
    highWeight: '重群',
    report: '报告',
    firstInputColumnAndDate: '请先输入栏号和日期',
    midWeightKG: '中位值(kg)',
    lightWeightKG: '轻群(kg)',
    highWeightKG: '重群(kg)',

    launchDate: '启动日期',
    nexecdate: '处理日期',
    addTransactionProcessing: '添加【事务处理】',
    updateTransactionProcessing: '修改【事务处理】',
    sureCancelTransactionProcessing: '是否确认删除【事务处理】编号为"',
    sureExportTransactionProcessing: '是否确认导出所有【事务处理】数据项?',

    WeightDistributionData: '体重分布数据',
    enterWeighingDateFirst: '请先输入称重日期',
    addWeightdistribution: '添加【体重分布】',
    updateWeightdistribution: '修改【体重分布】',
    sureCancelWeightdistribution: '是否确认删除【体重分布】编号为"',
    sureExportWeightdistribution: '是否确认导出所有【体重分布】数据项?',
    noData: "未查到数据",
    enterDate: "入栏日期",
    enterWeight: "入栏体重",

    cunlanNum: '存栏数量',
    zeroyccunlan: '零耳牌预测存栏量',
    columnSystemZeroprediction: '零耳牌上市预测',
    targetWeight2: '目标体重',
    gjdays: '预计天数',
    gjdate: '预计上市日期',
  }
}
export default en;
