<template>
  <div class="app-container">
    <el-row>
      <el-col
        :xs="24"
        :sm="24"
        :md="12"
        :lg="6"
        class="card-box"
        v-for="item in slaveList"
        v-bind:key="item.value"
      >
        <el-card v-if="item.slavenetwork === 'ok' && handleOnline(item.uptime)">
          <el-row style="display: flex">
            <el-col :span="10">
              <span class="demonstration">{{ item.slavename }}</span>
              <el-image
                style="width: 90%; margin: 10px 0px"
                :src="require('@/assets/icons/liaota/liaota1.png')"
              ></el-image>
              <el-progress
                v-if="1 < item.weight && item.weight < 30"
                :text-inside="true"
                :stroke-width="26"
                :percentage="
                  parseFloat(((item.weight / item.volume) * 100).toFixed(2))
                "
              ></el-progress>
              <el-progress
                v-else-if="item.weight >= 30"
                :text-inside="true"
                :stroke-width="26"
                :percentage="
                  parseFloat(((item.weight / item.volume) * 100).toFixed(2))
                "
                status="warning"
              ></el-progress>
              <el-progress
                v-else-if="item.weight <= 1 && item.weight >= 0"
                :text-inside="true"
                :stroke-width="26"
                :percentage="
                  parseFloat(((item.weight / item.volume) * 100).toFixed(2))
                "
                status="warning"
              ></el-progress>
              <el-progress
                v-else-if="item.weight < 0"
                :text-inside="true"
                :stroke-width="26"
                :percentage="
                  parseFloat(((item.weight / item.volume) * 100).toFixed(2))
                "
                status="exception"
              ></el-progress>
              <!-- <div v-else-if="item.weight < 0" style="text-align: center; color:red;height:26px;"></div> -->
            </el-col>
            <el-col :span="1">
              <el-divider
                style="height: 100%"
                direction="vertical"
              ></el-divider>
            </el-col>

            <el-col :span="13" class="rowStyle">
              <!-- uptime -->

              <el-row class="rowStyleSpan2">
                <el-col :span="14">{{ $t("liaota.currentWeight") }}</el-col>
                <el-col :span="10" style="font-size: 15px; font-weight: bold">{{
                  item.weight
                }}</el-col>
              </el-row>
              <el-row class="rowStyleSpan">
                <el-col :span="12">{{ $t("huanKong.updateTime") }}</el-col>
                <el-col :span="12">{{ item.uptime }}</el-col>
              </el-row>
              <el-row class="rowStyleSpan">
                <el-col :span="12">{{ $t("liaota.feedType") }}</el-col>
                <el-col :span="12">{{ item.feedtype }}</el-col>
              </el-row>
              <el-row class="rowStyleSpan">
                <el-col :span="12">{{ $t("liaota.volume") }}</el-col>
                <el-col :span="12"
                  >{{ item.volume ? item.volume : "---" }}
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-card>

        <el-card v-else>
          <el-row style="display: flex">
            <el-col :span="9">
              <span class="demonstration">{{ item.slavename }}</span>
              <el-image
                style="width: 90%; margin: 10px 0px"
                :src="require('@/assets/icons/liaota/liaota_off.png')"
              ></el-image>

              <el-progress
                :text-inside="true"
                :stroke-width="26"
                :percentage="0"
                status="exception"
              ></el-progress>
            </el-col>
            <el-col :span="1">
              <el-divider
                style="height: 100%"
                direction="vertical"
              ></el-divider>
            </el-col>

            <el-col :span="14" class="rowStyle">
              <el-row class="rowStyleSpan">
                <el-col :span="14">{{ $t("liaota.currentWeight") }}</el-col>
                <el-col :span="10" style="color: #ffba00; font-size: 15px">{{
                  $t("liaota.notConnected")
                }}</el-col>
              </el-row>
              <el-row class="rowStyleSpan">
                <el-col :span="12">{{ $t("huanKong.updateTime") }}</el-col>
                <el-col :span="12">{{ item.uptime }}</el-col>
              </el-row>
              <el-row class="rowStyleSpan">
                <el-col :span="12">{{ $t("liaota.feedType") }}</el-col>
                <el-col :span="12">{{ item.feedtype }}</el-col>
              </el-row>
              <el-row class="rowStyleSpan">
                <el-col :span="12">{{ $t("liaota.volume") }}</el-col>
                <el-col :span="12"
                  >{{ item.volume ? item.volume : "---" }}
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { listSlave } from "@/api/liaota/slave";

export default {
  name: "liaotahomeData",
  data() {
    return {
      dataInterval: 3 * 1000,
      updateIntervalLiaoTa: null,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      slaveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        slaveid: null,
        slavename: null,
        switchid: null,
        homeid: null,
        uptime: null,
        slavenetwork: null,
        weight: null,
        alarmmsg: null,
      },
    };
  },
  created() {
    this.getList();
    // this.updateIntervalLiaoTa = setInterval(() => {
    //   this.getList();
    // }, this.dataInterval);
  },
  mounted() {
    this.updateIntervalLiaoTa = setInterval(() => {
      this.getList();
    }, this.dataInterval);
  },
  beforeDestroy() {
    clearInterval(this.updateIntervalLiaoTa);
  },
  methods: {
    handleOnline(uptime) {
      var dateBegin = new Date(uptime.replace(/-/g, "/")); //replace方法将-转为/苹果兼容性
      var dateEnd = new Date(); //当前时间数据

      var startTime = dateBegin.getTime();
      var stopTime = dateEnd.getTime();
      var cTime = Number(stopTime) - Number(startTime);
      var secondTime = cTime / 1000 / 60;
      if (parseInt(secondTime) > 30) {
        return false;
      } else {
        return true;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listSlave(this.queryParams).then((response) => {
        this.slaveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
  },
};
</script>

<style scoped>
.tagLine {
  border-radius: 100px;
  width: 100%;
  text-align: center;
  height: 40px;
  line-height: 40px;
  background-color: #79bbff;
  border: none;
  font-size: 14px;
}
.rowStyle {
  font-size: 30px;
  font-family: Helvetica;
  color: #82848a;
  padding-top: 10px;
}
.spanS {
  line-height: 70px;
  font-size: 50px;
  font-family: Helvetica;
  margin: auto;
  text-align: center;
  color: #26d8e0e0;
}
.lineS {
  margin: 5px 0px;
  height: 0.3px;
}
.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 5em;
  margin: 0 8px;
  vertical-align: middle;
  position: relative;
}
/* .demonstration {
  display: block;
  color: #8492a6;
  font-size: 12px;
  margin-bottom: 5px;
  text-align: center;
} */
.cardhead {
  font-family: Helvetica;
  color: #8492a6;
}
.bkpng {
  background-image: url("../../../assets/icons/environment_svg/lianzi.png");
}

.demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin: 10px 0px;
  height: 20px;
  text-align: center;
}

.el-divider--vertical {
  height: 100%;
}

.rowStyleSpan {
  font-size: 14px;
  text-align: left;
  height: 25%;
}

.rowStyleSpan2 {
  font-size: 14px;
  /* padding-top: 3px; */
  text-align: center;
  color: #077225;
  height: 25%;
}

.lineS {
  margin: 5px 0px;
  height: 0.3px;
}
</style>