import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listMeasure(query) {
  return request({
    url: '/system/measure/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getMeasure(id) {
  return request({
    url: '/system/measure/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addMeasure(data) {
  return request({
    url: '/system/measure',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateMeasure(data) {
  return request({
    url: '/system/measure',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delMeasure(id) {
  return request({
    url: '/system/measure/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportMeasure(query) {
  return request({
    url: '/system/measure/export',
    method: 'get',
    params: query
  })
}