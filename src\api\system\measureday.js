import request from '@/utils/request'
///system/measure/gatherOneDayData
export function gatherOneDayData(query) {
  return request({
    url: '/system/measure/gatherOneDayData',
    method: 'post',
    data: query
  })
}
export function gatherOneDayOnePigData(query) {
  return request({
    url: '/system/measure/gatherOneDayOnePigData',
    method: 'post',
    data: query
  })
}


// 查询【请填写功能名称】列表
export function listMeasureday(query) {
  return request({
    url: '/system/measureday/list',
    method: 'get',
    params: query
  })
}

export function getFoodCharts(query){
    return request({
        url: '/system/measureday/foodCharts',
        method: 'get',
        params: query
    })
}

export function getWeightCharts(query){
    return request({
        url: '/system/measureday/weightCharts',
        method: 'get',
        params: query
    })
}

export function getPigweightGrowCharts(query){
  // /system/measureday/weightgorowcharts
  return request({
      url: '/system/measureday/weightgorowcharts',
      method: 'get',
      params: query
  })
}

export function getdaterangebyweightrange(query){
  return request({
    url: '/system/measureday/getdaterangebyweightrange',
    method: 'get',
    params: query
  })
}



// 查询【请填写功能名称】详细
export function getMeasureday(indexn) {
  return request({
    url: '/system/measureday/' + indexn,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addMeasureday(data) {
  return request({
    url: '/system/measureday',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateMeasureday(data) {
  return request({
    url: '/system/measureday',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delMeasureday(indexn) {
  return request({
    url: '/system/measureday/' + indexn,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportMeasureday(query) {
  return request({
    url: '/system/measureday/export',
    method: 'get',
    params: query
  })
}

// 查询【汇总】列表
export function gatherlist(query) {
    return request({
        url: '/system/measureday/gatherlist',
        method: 'get',
        params: query
    })
}

// 查询【汇总采食】列表
export function measureGatherFoodCharts(query) {
    return request({
        url: '/system/measureday/measureGatherFoodCharts',
        method: 'get',
        params: query
    })
}

// 查询【汇总采食】列表
export function measureGatherweightCharts(query) {
    return request({
        url: '/system/measureday/measureGatherweightCharts',
        method: 'get',
        params: query
    })
}

export function measureGatherweightGrowCharts(query) {
  return request({
      url: '/system/measureday/pigweightGrowCharts',
      method: 'get',
      params: query
  })
}
