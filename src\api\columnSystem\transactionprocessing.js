import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listProcess(query) {
  return request({
    url: '/system/process/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getProcess(indexn) {
  return request({
    url: '/system/process/' + indexn,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addProcess(data) {
  return request({
    url: '/system/process',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateProcess(data) {
  return request({
    url: '/system/process',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delProcess(indexn) {
  return request({
    url: '/system/process/' + indexn,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportProcess(query) {
  return request({
    url: '/system/process/export',
    method: 'get',
    params: query
  })
}
