<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
        <span>
          <el-select
            v-model="queryParams.nindex"
            :placeholder="$t('common.pleaseChoose')"
            size="small"
          >
            <el-option
              v-for="item in nIndexOptions"
              :key="item.nindex"
              :label="item.nindex"
              :value="item.nindex"
              @keyup.enter.native="handleQuery"
            ></el-option>
          </el-select>

          <div
            style="
              position: relative;
              display: inline-block;
              width: 80px;
              height: 10px;
            "
          >
            <i
              :class="{ icon: iconShowSmall }"
              class="el-icon-caret-top"
              style="
                font-size: 22px;
                position: absolute;
                top: -16px;
                color: #c0c4cc;
              "
              @click="handleSmall"
            ></i>
            <i
              :class="{ icon: iconShowBig }"
              class="el-icon-caret-bottom"
              style="font-size: 22px; position: absolute; color: #c0c4cc"
              @click="handleBig"
            ></i>
          </div>
        </span>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.queryDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
      </el-form-item>
      <el-form-item :label="$t('columnSystem.weightDate')" prop="ndate">
        <el-select
          v-model="queryParams.ndate"
          :placeholder="$t('common.pleaseChoose')"
          size="small"
        >
          <el-option
            v-for="item in dateList"
            :key="item"
            :label="item"
            :value="item"
            @keyup.enter.native="getFirst"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.cunlanNum')" prop="cunlannum">
        <el-input
            v-model="queryParams.cunlannum"
            :placeholder="$t('common.pleaseInput')"
            clearable
            size="small"
        />
      </el-form-item>
    </el-form>
    <!--
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getListAll"
      ></right-toolbar>
    </el-row> -->
    <el-tabs v-model="activeName" @tab-click="handleClick" tab-position="right">
      <el-tab-pane v-loading="loading"
        :label="$t('columnSystem.npignums')"
        name="first"
        :lazy="true"
      >
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category22" id="category22" style="height: 400px" />
        </div>
      </el-tab-pane>
      <el-tab-pane
        :label="$t('columnSystem.percentage')"
        name="second"
        :lazy="true"
      >
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category23" id="category23" style="height: 400px" />
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('columnSystem.visits')" name="third" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category24" id="category24" style="height: 400px" />
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('columnSystem.zeroyccunlan')" name="fifth" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category25" id="category25" style="height: 400px" />
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('columnSystem.hourfangwen')" name="sixth" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category26" id="category26" style="height: 400px" />
        </div>
      </el-tab-pane>
      <el-tab-pane
        :label="$t('columnSystem.WeightDistributionData')"
        name="fourth"
        :lazy="true"
        style="padding-bottom: 20px"
      >
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['columnSystem:weightdistribution:add']"
              >{{ $t("common.add") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['columnSystem:weightdistribution:edit']"
              >{{ $t("common.update") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['columnSystem:weightdistribution:remove']"
              >{{ $t("common.delete") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['columnSystem:weightdistribution:export']"
              >{{ $t("common.export") }}</el-button
            >
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="dailyweightdayListForm"
          @selection-change="handleSelectionChange"
          :cell-style="{ padding: '0' }"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            :label="$t('columnSystem.mid')"
            align="center"
            prop="mid"
          />
          <el-table-column
            :label="$t('columnSystem.mrfid')"
            align="center"
            prop="mrfid"
          />
          <el-table-column
            :label="$t('columnSystem.mdorm')"
            align="center"
            prop="mdorm"
          />
          <el-table-column
            :label="$t('columnSystem.nindex')"
            align="center"
            prop="nindex"
          />
          <el-table-column
            :label="$t('columnSystem.mname')"
            align="center"
            prop="mname"
          />
          <el-table-column
            :label="$t('columnSystem.naddress')"
            align="center"
            prop="naddress"
          />
          <el-table-column
            :label="$t('columnSystem.weightDate')"
            align="center"
            prop="ndate"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.ndate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('columnSystem.weight')"
            align="center"
            prop="nweight"
          />
          <el-table-column
            :label="$t('columnSystem.visits')"
            align="center"
            prop="nweightnum"
          />
          <el-table-column
              :label="$t('columnSystem.avgvisit')"
              align="center"
          >
            <template slot-scope="scope">
              {{ (scope.row.nweightnum / queryParams.cunlannum).toFixed(1)}}
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('columnSystem.npignums')" align="center" prop="npignum" /> -->
          <el-table-column
            :label="$t('common.operate')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['columnSystem:weightdistribution:edit']"
                >{{ $t("common.update") }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['columnSystem:weightdistribution:remove']"
                >{{ $t("common.delete") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="formtotal > 0"
            :total="formtotal"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改【体重分布】对话框 -->
        <el-dialog
          :title="title"
          :visible.sync="open"
          width="500px"
          append-to-body
        >
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item :label="$t('columnSystem.mid')" prop="mid">
              <el-input
                v-model="form.mid"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
              <el-input
                v-model="form.mrfid"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
              <el-input
                v-model="form.mdorm"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
              <el-input
                v-model="form.nindex"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.mname')" prop="mname">
              <el-input
                v-model="form.mname"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
              <el-input
                v-model="form.naddress"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.weightDate')" prop="ndate">
              <el-date-picker
                clearable
                size="small"
                style="width: 200px"
                v-model="form.ndate"
                type="date"
                value-format="yyyy-MM-dd"
                :placeholder="$t('common.pleaseChoose')"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="$t('columnSystem.weight')" prop="nweight">
              <el-input
                v-model="form.nweight"
                :placeholder="$t('columnSystem.pleaseEnterWeight')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.visits')" prop="nweightnum">
              <el-input
                v-model="form.nweightnum"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <!-- <el-form-item :label="$t('columnSystem.npignums')" prop="npignum">
              <el-input v-model="form.npignum" :placeholder="$t('common.pleaseInput')" />
            </el-form-item> -->
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">{{
              $t("common.determine")
            }}</el-button>
            <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
          </div>
        </el-dialog>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  listDailyweightday,
  weightDaylist,
  getDailyweightday,
  delDailyweightday,
  addDailyweightday,
  updateDailyweightday,
  exportDailyweightday,
  listDayByCunlan,
  listDayByRate,
  listDayByFangwen,
  listdays,
} from "@/api/columnSystem/dailyweightday";
import { listControldaily } from "@/api/columnSystem/controldaily";
import * as echarts from "echarts";

export default {
  name: "Dailyweightday",
  components: {},
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true,
    },
    iconShowBig: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeName: "first",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      //查询结果
      total: 0,
      // 总条数
      formtotal: 0,
      // 【体重分布】结果
      dailyweightdayList: [],

      // 【体重分布】表格数据
      dailyweightdayListForm: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        boarid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        nweight: null,
        nweightnum: null,
        npignum: null,
        cunlannum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      //采食echart
      category22: null,
      //体重echart
      category23: null,
      //日增重echart
      category24: null,
      //零耳牌预测
      category25: null,
      //每小时访问量
      category26: null,
      cunLanList: {},
      rateList: {},
      fangWenList: {},
      //存栏量x轴数据
      xDataNpignum: [],
      //存栏量y轴数据
      yDataNpignum: [],
      //百分比x轴数据
      xWeightPercentage: [],
      //零耳牌存栏估计y轴
      yzerogj:[],
      //百分比y轴数据
      yWeightPercentage: [],
      //访问量x轴数据
      xDataNweightnum: [],
      //访问量y轴数据
      yDataNweightnum: [],
      //每小时访问量x轴数据
      xHourFangwennum: [],
      //每小时访问量y轴
      yHourFangwenNum: [],

      //栏号数组
      nIndexOptions: [],
      dateList: [],
    };
  },
  watch: {
    "queryParams.nindex": {
      handler() {
        if (this.queryParams) {
          this.handleQuery();
        }
      },
      deep: true,
    },
    "queryParams.dateRange": {
      handler() {
        if (this.queryParams) {
          this.handleQuery();
        }
      },
      deep: true,
    },
    "queryParams.ndate"() {
      this.getListAll();
    },
  },
  created() {
    listControldaily({
      ntype: 2,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.nIndexOptions = response.rows;
      this.queryParams.nindex = this.nIndexOptions[0].nindex;
    });
  },
  mounted() {
    //渲染之后
    window.onresize = () => {
      //alert("sss");
      this.category22.resize(); //重新初始化echarts
      this.category23.resize(); //重新初始化echarts
      this.category24.resize(); //重新初始化echarts
      this.category25.resize(); //重新初始化echarts
      this.category26.resize(); //重新初始化echarts
    };
  },
  methods: {
    handleSmall() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index > 0) {
          this.queryParams.nindex = this.nIndexOptions[index - 1].nindex;
        }
      } else {
        this.queryParams.nindex = this.nIndexOptions[0].nindex;
      }
    },
    handleBig() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index < this.nIndexOptions.length - 1) {
          this.queryParams.nindex = this.nIndexOptions[index + 1].nindex;
        }
      } else {
        this.queryParams.nindex =
          this.nIndexOptions[this.nIndexOptions.length - 1].nindex;
      }
    },
    handleClick(tab, event) {
      if (tab.name == "first") {
        this.$nextTick(() => {
          this.getFirst();
          this.category22.resize();
        });
      }
      if (tab.name == "second") {
        this.$nextTick(() => {
          this.getSecond();
          this.category23.resize();
        });
      }
      if (tab.name == "third") {
        this.$nextTick(() => {
          this.getThird();
          this.category24.resize();
        });
      }
      if (tab.name == "fifth") {
        this.$nextTick(() => {
          this.getFifth();
          this.category25.resize();
        });
      }
      if (tab.name == "fourth") {
        this.getList();
      }
      if (tab.name == "sixth") {
        this.$nextTick(() => {
          this.getSixth();
          this.category26.resize();
        });
      }
    },
    getSecond() {
      if (this.queryParams.ndate) {
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        weightDaylist(this.queryParams).then((response) => {
          this.dailyweightdayList = response.rows;
          this.total = response.total;
          this.xWeightPercentage = [
            5, 15, 25, 35, 45, 55, 65, 75, 85, 95, 105, 115, 125, 135, 145, 155,
            165,
          ];
          const countResult = Array(17).fill(0);

          this.dailyweightdayList.forEach((item) => {
            if(item.nweight <= 165){
              const closestIndex = Math.round(item.nweight / 10);
              countResult[closestIndex]++;
            }
          });
          // this.yWeightPercentage = countResult;
          this.yWeightPercentage = countResult.map((i) =>
            i > 0 ? Math.round((i / this.total) * 100): 0
          );
          this.getPercentage();
        });
      } else {
        this.msgInfo(this.$t("columnSystem.enterWeighingDateFirst"));
      }
    },
    getFifth() {
      if (this.queryParams.ndate) {
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        weightDaylist(this.queryParams).then((response) => {
          this.dailyweightdayList = response.rows;
          this.total = response.total;
          this.xWeightPercentage = [
            5, 15, 25, 35, 45, 55, 65, 75, 85, 95, 105, 115, 125, 135, 145, 155,
            165,
          ];
          const countResult = Array(17).fill(0);

          this.dailyweightdayList.forEach((item) => {
            if(item.nweight <= 165){
              const closestIndex = Math.round(item.nweight / 10);
              countResult[closestIndex]++;
            }
          });
          this.yzerogj = countResult.map((i) =>
              i > 0 ? Math.round((i / this.total) * this.queryParams.cunlannum) : 0
          );
          this.getzerogj();
        });
      } else {
        this.msgInfo(this.$t("columnSystem.enterWeighingDateFirst"));
      }
    },
    getThird() {
      if (this.queryParams.ndate) {
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        weightDaylist(this.queryParams).then((response) => {
          this.dailyweightdayList = response.rows;
          this.total = response.total;
          this.xDataNweightnum = [
            5, 15, 25, 35, 45, 55, 65, 75, 85, 95, 105, 115, 125, 135, 145, 155,
            165,
          ];
          const countResult = Array(17).fill(0);

          this.dailyweightdayList.forEach((item) => {
            if(item.nweight <= 165){  //超过就越界了
              const closestIndex = Math.round(item.nweight / 10);
              countResult[closestIndex] = countResult[closestIndex] + (item.nweightnum == null ? 1:item.nweightnum);
            }
          });
          this.yDataNweightnum = countResult;
          this.getNweightnumDatas();
        });
      } else {
        this.msgInfo(this.$t("columnSystem.enterWeighingDateFirst"));
      }
    },
    getSixth(){

      if (this.queryParams.ndate) {
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        weightDaylist(this.queryParams).then((response) => {
          this.dailyweightdayList = response.rows;
          this.total = response.total;
          this.xHourFangwennum = [
            '0-1','1-2','2-3','3-4','4-5','5-6','6-7','7-8','8-9','9-10','10-11','11-12','12-13','13-14'
            ,'14-15','15-16','16-17','17-18','18-19','19-20','20-21','21-22','22-23','23-24'
          ];
          const countResult = Array(24).fill(0);

          this.dailyweightdayList.forEach((item) => {
            countResult[(new Date(item.ndate)).getHours()] ++;

          });
          this.yHourFangwenNum = countResult;
          this.getHourFangwenDatas();
        });
      } else {
        this.msgInfo(this.$t("columnSystem.enterWeighingDateFirst"));
      }
    },
    getHourFangwenDatas(){

      this.category26 = this.$refs.category26
          ? echarts.init(this.$refs.category26, "walden")
          : "";
      this.category26 &&
      this.category26.setOption({
        title: {
          text: this.$t("columnSystem.nindex") +this.queryParams.nindex+'-' + this.queryParams.ndate,
          left: 'center'
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        legend: {
          data: this.$t("columnSystem.hourfangwen"),
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          axisTick: {
            alignWithLabel: true,
          },
          data: this.xHourFangwennum,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: this.$t("columnSystem.hourfangwen"),
            type: "bar",
            stack: this.$t("columnSystem.total"),
            barWidth: "40%",
            data: this.yHourFangwenNum,
            label: { // 配置数值标签
              show: true, // 显示标签
              position: 'top', // 标签位置（top/inside/outside等）
              fontSize: 12, // 字体大小
              formatter: '{c}' // {c} 代表数据值
            }
          },
        ],
      });
    },
    getNpignumDatas() {
      this.category22 = this.$refs.category22
        ? echarts.init(this.$refs.category22, "walden")
        : "";
      this.category22 &&
        this.category22.setOption({
          title: {
            text: this.$t("columnSystem.nindex") +this.queryParams.nindex+'-' + this.queryParams.ndate,
            left: 'center'
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: {
            data: this.$t("columnSystem.npignums"),
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: this.xDataNpignum,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              name: this.$t("columnSystem.npignums"),
              type: "bar",
              stack: this.$t("columnSystem.total"),
              barWidth: "40%",
              data: this.yDataNpignum,
              label: { // 配置数值标签
                show: true, // 显示标签
                position: 'top', // 标签位置（top/inside/outside等）
                fontSize: 12, // 字体大小
                formatter: '{c}' // {c} 代表数据值
              }
            },
          ],
        });
    },
    getzerogj() {
      this.category25 = this.$refs.category25
          ? echarts.init(this.$refs.category25, "walden")
          : "";
      this.category25 &&
      this.category25.setOption({
        title: {
          text: this.$t("columnSystem.nindex") +this.queryParams.nindex+'-' + this.queryParams.ndate,
          left: 'center'
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        legend: {
          data: this.$t("columnSystem.npignums"),
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          axisTick: {
            alignWithLabel: true,
          },
          data: this.xDataNpignum,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: this.$t("columnSystem.npignums"),
            type: "bar",
            stack: this.$t("columnSystem.total"),
            barWidth: "40%",
            data: this.yzerogj,
            label: { // 配置数值标签
              show: true, // 显示标签
              position: 'top', // 标签位置（top/inside/outside等）
              fontSize: 12, // 字体大小
              formatter: '{c}' // {c} 代表数据值
            }
          },
        ],
      });
    },
    getPercentage() {
      this.category23 = this.$refs.category23
        ? echarts.init(this.$refs.category23, "walden")
        : "";
      this.category23 &&
        this.category23.setOption({
          title: {
            text: this.$t("columnSystem.nindex") +this.queryParams.nindex+'-' + this.queryParams.ndate,
            left: 'center'
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: {
            data: this.$t("columnSystem.percentage"),
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: this.xWeightPercentage,
          },
          yAxis: {
            type: "value",
            axisLabel: {
              formatter: "{value} %",
            },
          },
          series: [
            {
              name: this.$t("columnSystem.percentageAnd"),
              type: "bar",
              stack: this.$t("columnSystem.total"),
              barWidth: "40%",
              data: this.yWeightPercentage,
              label: { // 配置数值标签
                show: true, // 显示标签
                position: 'top', // 标签位置（top/inside/outside等）
                fontSize: 12, // 字体大小
                formatter: '{c}' // {c} 代表数据值
              }
            },
          ],
        });

    },
    getNweightnumDatas() {
      this.category24 = this.$refs.category24
        ? echarts.init(this.$refs.category24, "walden")
        : "";
      this.category24 &&
        this.category24.setOption({
          title: {
            text: this.$t("columnSystem.nindex") +this.queryParams.nindex+'-' + this.queryParams.ndate,
            left: 'center'
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: {
            data: this.$t("columnSystem.visits"),
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: this.xDataNweightnum,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              name: this.$t("columnSystem.visits"),
              type: "bar",
              stack: this.$t("columnSystem.total"),
              barWidth: "40%",
              data: this.yDataNweightnum,
              label: { // 配置数值标签
                show: true, // 显示标签
                position: 'top', // 标签位置（top/inside/outside等）
                fontSize: 12, // 字体大小
                formatter: '{c}' // {c} 代表数据值
              }
            },
          ],
        });
    },
    /** 查询【体重分布】列表 */
    getFirst() {
      if (this.queryParams.ndate) {
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        weightDaylist(this.queryParams).then((response) => {
          this.dailyweightdayList = response.rows;
          this.xDataNpignum = [
            5, 15, 25, 35, 45, 55, 65, 75, 85, 95, 105, 115, 125, 135, 145, 155,
            165,
          ];
          const countResult = Array(17).fill(0);

          this.dailyweightdayList.forEach((item) => {
            if(item.nweight <= 165){
              const closestIndex = Math.round(item.nweight / 10);
              countResult[closestIndex]++;
            }
          });

          this.yDataNpignum = countResult;
          this.getNpignumDatas();
        });
      } else {
        this.msgInfo(this.$t("columnSystem.enterWeighingDateFirst"));
      }
    },

    getListAll() {
      if (this.queryParams.ndate) {
        this.getFirst();
        this.getSecond();
        this.getFifth();
        this.getThird();
        this.getSixth();
        this.getList();
      } else {
        this.msgInfo(this.$t("columnSystem.enterWeighingDateFirst"));
      }
    },
    //时间转换(js将 “2021-07-06T06:23:57.000+00:00” 转换为年月日时分秒)
    transformTimestamp(timestamp) {
      let a = new Date(timestamp).getTime();
      const date = new Date(a);
      const Y = date.getFullYear() + "-";
      const M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      const D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "  ";
      const h =
        (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      const m =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      // const s = date.getSeconds(); // 秒
      const dateString = Y + M + D + h + m;
      // console.log('dateString', dateString); // > dateString 2021-07-06 14:23
      return dateString;
    },

    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      this.queryParams.pageNum = this.queryParams.pageNum;
      this.queryParams.pageSize = this.queryParams.pageSize;
      listDailyweightday(this.queryParams).then((response) => {
        this.dailyweightdayListForm = response.rows;
        this.formtotal = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        boarid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        nweight: null,
        nweightnum: null,
        npignum: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.nindex && this.queryParams.dateRange) {
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        listdays(
          this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
        ).then((response) => {
          this.dateList =
            response.data &&
            response.data.map((item) =>
              this.parseTime(this.transformTimestamp(item), "{y}-{m}-{d}")
            );
          this.queryParams.ndate = this.dateList && this.dateList[0];
        });

        //查询存栏量
        listControldaily({
          ntype: 3,
          mfactory: this.$store.state.settings.nowPigFarm,
          naddress: this.queryParams.nindex
        }).then((response) => {
          this.queryParams.cunlannum = response.rows[0].cunlannum;
        });


      } else {
        this.msgInfo(this.$t("columnSystem.pleaseFirstEnterColumn"));
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("columnSystem.addWeightdistribution");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDailyweightday(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("columnSystem.updateWeightdistribution");
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDailyweightday(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addDailyweightday(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("columnSystem.sureCancelWeightdistribution") +
          `"` +
          ids +
          `"` +
          this.$t("common.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delDailyweightday(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("columnSystem.sureExportWeightdistribution"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportDailyweightday(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>
