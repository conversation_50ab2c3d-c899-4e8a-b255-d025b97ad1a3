<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" class="card-box">
        <el-card>
          <el-form ref="form" :model="form" label-width="80px">
            <!-- <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
            <el-input v-model="form.mdorm" placeholder="请输入延续时间" />
          </el-form-item> -->

            <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
              <span>
                <el-select
                    v-model="form.nindex"
                    :placeholder="$t('common.pleaseChoose')"
                    size="small"
                    @change="$forceUpdate()"
                >
                  <el-option
                      v-for="item in nIndexOptions"
                      :key="item.nindex"
                      :label="item.nindex"
                      :value="item.nindex"
                      @click.native=""
                  ></el-option>
                </el-select>

                <div
                    style="
                    position: relative;
                    display: inline-block;
                    width: 80px;
                    height: 10px;
                  "
                >

                </div>
              </span>
            </el-form-item>

            <el-descriptions
              :title="$t('columnSystem.targetWeight')"
              :column="3"
              class="margin-top"
            >
              <el-descriptions-item :label="$t('columnSystem.targetWeight')"
                ><el-input-number
                  size="small"
                  v-model="form.targetWeight"
                  controls-position="right"
                ></el-input-number>
              </el-descriptions-item>
              <el-descriptions-item :label="$t('columnSystem.targetDate')"
              ><el-date-picker
                  clearable
                  size="small"
                  style="width: 200px"
                  v-model="form.targetDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  :placeholder="$t('common.pleaseChoose')"
              >
              </el-date-picker>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions
              :title="$t('columnSystem.growthCalculations')"
              :column="3"
              class="margin-top"
            >
              <el-descriptions-item :label="$t('columnSystem.adgRelyDays')"
                ><el-input-number
                  size="small"
                  v-model="form.adgRelyDays"
                  controls-position="right"
                ></el-input-number>
              </el-descriptions-item>
              <el-descriptions-item label="ADG"
                ><el-input-number
                  size="small"
                  v-model="form.adg"
                  :precision="1"
                  :step="0.1"
                  :min="1"
                  :max="10"
                  controls-position="right"
                ></el-input-number>
              </el-descriptions-item>
              <el-descriptions-item :label="$t('columnSystem.isAdgStable')">
                <el-checkbox
                  v-model="form.isAdgStable"
                  true-label="1"
                  false-label="0"
                ></el-checkbox>
              </el-descriptions-item>
            </el-descriptions>

          </el-form>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" size="mini" @click="getList">{{
                $t("columnSystem.sure")
              }}</el-button>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="24" class="card-box">
        <el-card>
          <el-table v-loading="loading" :data="predictionList">
            <el-table-column
                :label="$t('columnSystem.nindex')"
                align="center"
                prop="nindex"
            ></el-table-column>
            <el-table-column
                label="ADG"
                align="center"
                prop="addDayWeight"
            ></el-table-column>
            <el-table-column
                :label="$t('columnSystem.toushu')"
                align="center"
                prop="toushu"
            ></el-table-column>

          </el-table>
        </el-card>

      </el-col>
    </el-row>
  </div>
</template>
<script>
import { zeroforecastbydate } from "@/api/columnSystem/prediction";
import { listControldaily } from "@/api/columnSystem/controldaily";
export default {
  data() {
    return {
      form: {
        dataType: "1",
      },
      loading: false,
      //栏号数组
      nIndexOptions: [],
      predictionList: [],
    };
  },
 created() {
      listControldaily({
        ntype: 2,
        mfactory: this.$store.state.settings.nowPigFarm,
      }).then((response) => {
        this.nIndexOptions = response.rows;
        this.form.nindex = this.nIndexOptions[0].nindex;
      });
 },
  methods: {
    // 定义函数，将时间格式转换为 "年-月-日" 形式
    formatTime(dateString) {
      const date = new Date(dateString); // 将时间字符串转换为日期对象
      const year = date.getFullYear(); // 获取年份
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 获取月份，并补零
      const day = String(date.getDate()).padStart(2, "0"); // 获取日期，并补零
      return `${year}-${month}-${day}`; // 返回格式化后的时间字符串
    },
    // 定义处理时间字段的函数，返回处理后的 JSON 数组
    processTimeField(jsonData, timeField) {
      const processedData = [];
      for (const item of jsonData) {
        const { [timeField]: timestamp, ...rest } = item; // 使用解构赋值提取时间字段并剩余属性
        processedData.push({
          ...rest,
          [timeField]: this.formatTime(timestamp),
        }); // 构建新对象，将时间字段转换为 Date 对象
      }
      return processedData;
    },

    getList() {
      this.loading = true;
      this.form.mfactory = this.$store.state.settings.nowPigFarm;
      zeroforecastbydate(this.form).then(
        (response) => {
          this.predictionList = this.processTimeField(
            response.data,
            "targerDay"
          );
          this.loading = false;
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-descriptions-item__container .el-descriptions-item__label,
.el-descriptions-item__container .el-descriptions-item__content {
  align-items: center;
}
</style>
