<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
        <span>
          <el-select
            v-model="queryParams.nindex"
            :placeholder="$t('common.pleaseChoose')"
            size="small"
          >
            <el-option
              v-for="item in nIndexOptions"
              :key="item.nindex"
              :label="item.nindex"
              :value="item.nindex"
              @keyup.enter.native="handleQuery"
            ></el-option>
          </el-select>

          <div
            style="
              position: relative;
              display: inline-block;
              width: 80px;
              height: 10px;
            "
          >
            <i
              :class="{ icon: iconShowSmall }"
              class="el-icon-caret-top"
              style="
                font-size: 22px;
                position: absolute;
                top: -16px;
                color: #c0c4cc;
              "
              @click="handleSmall"
            ></i>
            <i
              :class="{ icon: iconShowBig }"
              class="el-icon-caret-bottom"
              style="font-size: 22px; position: absolute; color: #c0c4cc"
              @click="handleBig"
            ></i>
          </div>
        </span>
      </el-form-item>
      <el-form-item
        :label="$t('boarMeasure.dateOfDetermination')"
        prop="dateRange"
      >
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['boarMeasure:mreasuredaygather:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        :label="$t('boarMeasure.dataList')"
        name="first"
        :lazy="true"
      >
        <el-table
          v-loading="loading"
          :data="measureGatherlist"
          @selection-change="handleSelectionChange"
          :cell-style="{ padding: '0' }"
        >
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column
            :label="$t('boarMeasure.nindex')"
            align="center"
            prop="nindex"
            width="70"
          />
          <el-table-column
            :label="$t('boarMeasure.dateOfDetermination')"
            align="center"
            prop="date"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.date, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.measurePigs')"
            align="center"
            prop="pigNum"
          />
          <el-table-column
            :label="$t('boarMeasure.allNIngestionG')"
            align="right"
            prop="nIngestionSSum"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.nIngestionSSum | formatThousands(0) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.intakesNumber')"
            align="right"
            prop="feedSum"
          />
          <el-table-column
            :label="$t('boarMeasure.intakesTimeH')"
            align="right"
            prop="nSecondSSum"
          />
          <el-table-column
            :label="$t('boarMeasure.averageWeightKg')"
            align="right"
            prop="weightAvg"
            ><template slot-scope="scope">
              <span>{{ scope.row.weightAvg | formatNumber(1) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
        /> -->
      </el-tab-pane>
      <el-tab-pane
        :label="$t('boarMeasure.feedConsumptionChart')"
        name="second"
        :lazy="true"
      >
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div
            ref="category1"
            id="category1"
            style="height: 400px"
            v-if="'second' === activeName"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane
        :label="$t('boarMeasure.averageWeightGraph')"
        name="third"
        :lazy="true"
      >
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div
            ref="category2"
            id="category2"
            style="height: 400px"
            v-if="'third' === activeName"
          />
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane label="平均日增重" name="four" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div
            ref="category3"
            id="category3"
            style="height: 400px"
            v-if="'four' === activeName"
          />
        </div>
      </el-tab-pane> -->
      <el-tab-pane :label="$t('boarMeasure.dataAggregation')" name="five">
        <el-card style="margin-bottom: 15px">
          <el-descriptions :title="$t('boarMeasure.ningestion')">
            <el-descriptions-item :label="$t('boarMeasure.allNIngestionS')"
              ><span v-if="foodAll">{{
                foodAll | formatThousands(3)
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.measureDaysT')"
              ><span v-if="recordDays">{{
                recordDays
              }}</span></el-descriptions-item
            >
            <el-descriptions-item
              :label="$t('boarMeasure.averageDailyFeedIntake')"
              ><span v-if="averageDailyFeed">{{
                averageDailyFeed | formatThousands(3)
              }}</span></el-descriptions-item
            >
          </el-descriptions>
        </el-card>

        <el-card>
          <el-descriptions :title="$t('boarMeasure.weight')">
            <el-descriptions-item :label="$t('boarMeasure.startAverageWeight')"
              ><span v-if="startAverageWeight">{{
                startAverageWeight | formatNumber(1)
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.endAverageWeight')"
              ><span v-if="endAverageWeight">{{
                endAverageWeight | formatNumber(1)
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.averageGrowth')"
              ><span v-if="averageGrowth">{{
                averageGrowth | formatNumber(1)
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.measureDaysT')"
              ><span v-if="recordDays">{{
                recordDays
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.averageDailyGrowth')"
              ><span v-if="averageDailyGrowth">{{
                averageDailyGrowth | formatNumber(2)
              }}</span></el-descriptions-item
            >
          </el-descriptions>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  gatherlist,
  measureGatherFoodCharts,
  measureGatherweightCharts,
  measureGatherweightGrowCharts,
  exportMeasureday,
} from "@/api/system/measureday";
import { listControl } from "@/api/system/control";
import * as echarts from "echarts";
require("@/utils/walden"); // echarts theme

export default {
  name: "mreasuredaygather",
  components: {},
  // iconShowSmall: false,
  //  iconShowBig: false,
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true,
    },
    iconShowBig: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      //饲料总消耗量
      foodAll: "",
      //记录天数
      recordDays: "",
      //平均日采食量
      averageDailyFeed: "",
      //开始平均体重
      startAverageWeight: "",
      //结束平均体重
      endAverageWeight: "",
      //平均增重
      averageGrowth: "",
      //平均日增重
      averageDailyGrowth: "",
      orgOptions: null,
      activeName: "first",
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      //饲料消耗
      category1: null,
      //平均体重
      category2: null,
      //平均日增重
      category3: null,
      //栏号数组
      nIndexOptions: [],
      //foodEchart
      foodEchartList: [],
      //weightEchart
      weightEchartList: [],
      weightGrowEchartList: [],
      //采食x轴数据
      xDataFood: [],
      //采食y轴数据
      yDataFood: [],
      //体重x轴数据
      xWeight: [],
      //体重y轴数据
      yWeight: [],
      //日增重y轴数据
      yWeightGrow: [],
      //日增重x轴数据
      xWeightGrow: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【汇总报告】表格数据
      measureGatherlist: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 100,
        nindex: null,
        date: null,
        pigNum: null,
        nIngestionSSum: null,
        feedSum: null,
        nSecondSSum: null,
        weightAvg: null,
        datefrom: null,
        dateto: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mid: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        mrfid: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  created() {
    //查询栏号
    listControl({
      ntype: 2,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.nIndexOptions = response.rows;
      this.queryParams.nindex = this.nIndexOptions[0].nindex;
    });

    //this.getList();
  },
  mounted() {
    //渲染之后
    //this.getDatas();

    window.onresize = () => {
      //alert("sss");
      this.category1.resize(); //重新初始化echarts
      this.category2.resize(); //重新初始化echarts
      this.category3.resize(); //重新初始化echarts
    };
  },
  methods: {
    // //点击上移
    // clickUp(index) {
    //   console.log("this.nIndexOptions", this.nIndexOptions);
    //   // this.swapArray(this.tableData, index-1, index);
    // },
    // //点击下移
    // clickDown(index) {
    //   this.swapArray(this.tableData, index, index + 1);
    // },
    // //数组元素互换位置
    // swapArray(arr, index1, index2) {
    //   arr[index1] = arr.splice(index2, 1, arr[index1])[0];
    //   return arr;
    // },
    handleSmall() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index > 0) {
          this.queryParams.nindex = this.nIndexOptions[index - 1].nindex;
        }
      } else {
        this.queryParams.nindex = this.nIndexOptions[0].nindex;
      }
    },
    handleBig() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index < this.nIndexOptions.length - 1) {
          this.queryParams.nindex = this.nIndexOptions[index + 1].nindex;
        }
      } else {
        this.queryParams.nindex =
          this.nIndexOptions[this.nIndexOptions.length - 1].nindex;
      }
    },
    handleClick(tab, event) {
      if (tab.name == "first") this.getList();
      if (tab.name == "second") this.getList();
      if (tab.name == "third") this.getList();
      if (tab.name == "four") this.getList();
      if (tab.name == "five") this.getList();
    },

    /** 查询【汇总报告】列表 */
    getList() {
      if (this.queryParams.nindex) {
        this.loading = true;
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        if (this.activeName == "first" || this.activeName == "five") {
          gatherlist(
            this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
          ).then((response) => {
            this.measureGatherlist = response.rows;
            this.foodAll =
              this.measureGatherlist.reduce(function (
                accumulator,
                currentValue
              ) {
                return accumulator + currentValue.nIngestionSSum;
              },
              0) / 1000;
            this.recordDays = this.measureGatherlist.length;

            this.averageDailyFeed =
              this.recordDays && this.foodAll && this.foodAll / this.recordDays;
            this.endAverageWeight = this.measureGatherlist[0].weightAvg;
            this.startAverageWeight =
              this.measureGatherlist[this.recordDays - 1].weightAvg;
            this.averageGrowth =
              this.endAverageWeight - this.startAverageWeight;
            this.averageDailyGrowth =
              this.averageGrowth &&
              this.recordDays &&
              this.averageGrowth / this.recordDays;

            this.total = response.total;
            this.loading = false;
          });
        } else if (this.activeName == "second") {
          measureGatherFoodCharts(
            this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
          ).then((response) => {
            this.foodEchartList = response.rows;
            this.foodEchartList.forEach((element) => {
              this.xDataFood = element.xAries;
              // 饲料消耗(g)去掉小数位
              this.yDataFood = element.yAries.map((value) =>
                value !== null && value !== undefined
                  ? Math.round(Number(value))
                  : value
              );
            });
            this.getfoodConsumeData();
            this.loading = false;
          });
        } else if (this.activeName == "third") {
          measureGatherweightCharts(
            this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
          ).then((response) => {
            this.weightEchartList = response.rows;
            this.weightEchartList.forEach((element) => {
              this.xWeight = element.xAries;
              this.yWeight = element.yAries;
            });
            this.getWeightAgvData();
            this.loading = false;
          });
        } else if (this.activeName == "four") {
          measureGatherweightGrowCharts(
            this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
          ).then((response) => {
            this.weightGrowEchartList = response.rows;
            this.weightGrowEchartList.forEach((element) => {
              this.xWeightGrow = element.xAries;
              this.yWeightGrow = element.yAries;
            });
            this.getWeightGrowAgvData();
            this.loading = false;
          });
        }
        this.loading = false;
      } else {
        this.msgInfo(this.$t("boarMeasure.pleaseEnterColumn"));
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        nindex: null,
        date: null,
        pigNum: null,
        nIngestionSSum: null,
        feedSum: null,
        nSecondSSum: null,
        weightAvg: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 导出按钮操作 */
    handleExport() {
      this.$confirm(
        this.$t("boarMeasure.sureExportDayReport"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )

        .then(() => {
          return exportMeasureday(
            this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
          );
        })
        .then((response) => {
          this.download(response.msg);
        });
    },

    getfoodConsumeData() {
      this.category1 = this.$refs.category1
        ? echarts.init(this.$refs.category1, "walden")
        : "";
      // this.category1 = echarts.init(this.$refs.category1, "walden");
      this.category1 &&
        this.category1.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
          },
          legend: {
            data: [this.$t("boarMeasure.feedConsumption")],
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },

          xAxis: {
            type: "category",
            // boundaryGap: false,
            axisTick: {
              alignWithLabel: true,
            },
            data: this.xDataFood,
            inverse: true,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              name: this.$t("boarMeasure.feedConsumptionG"),
              type: "bar",
              // stack: "总量",
              data: this.yDataFood,
              inverse: true,
            },
          ],
        });
    },
    getWeightAgvData() {
      this.category2 = this.$refs.category2
        ? echarts.init(this.$refs.category2, "walden")
        : "";
      // this.category2 = echarts.init(this.$refs.category2, "walden");
      this.category2 &&
        this.category2.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
          },
          legend: {
            data: [this.$t("boarMeasure.weight")],
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: this.xWeight,
            inverse: true,
          },
          yAxis: {
            type: "value",
            maxInterval: 5,
          },
          series: [
            {
              // symbol: "none", //取消折点圆圈
              name: this.$t("boarMeasure.weightKg"),
              type: "line",
              // stack: "总量",
              data: this.yWeight,
              inverse: true,
            },
          ],
        });
    },
    getWeightGrowAgvData() {
      this.category3 = this.$refs.category3
        ? echarts.init(this.$refs.category3, "walden")
        : "";
      // this.category3 = echarts.init(this.$refs.category3, "walden");
      this.category3 &&
        this.category3.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
          },
          legend: {
            data: [this.$t("boarMeasure.dailyGainWeight")],
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: this.xWeightGrow.reverse(),
            inverse: true,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              symbol: "none", //取消折点圆圈
              name: this.$t("boarMeasure.dailyGainWeightKg"),
              type: "line",
              // stack: "日增重",
              data: this.yWeightGrow.reverse(),
              inverse: true,
            },
          ],
        });
    },
  },
};
</script>
<style scoped>
.icon:hover {
  color: #48d1cc !important;
}
</style>
