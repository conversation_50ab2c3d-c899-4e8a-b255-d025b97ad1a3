import request from '@/utils/request'

// 查询第三方应用列表
export function listThirdparty(query) {
  return request({
    url: '/system/thirdparty/list',
    method: 'get',
    params: query
  })
}

// 查询第三方应用详细
export function getThirdparty(appId) {
  return request({
    url: '/system/thirdparty/' + appId,
    method: 'get'
  })
}

// 新增第三方应用
export function addThirdparty(data) {
  return request({
    url: '/system/thirdparty',
    method: 'post',
    data: data
  })
}

// 修改第三方应用
export function updateThirdparty(data) {
  return request({
    url: '/system/thirdparty',
    method: 'put',
    data: data
  })
}

// 删除第三方应用
export function delThirdparty(appIds) {
  return request({
    url: '/system/thirdparty/' + appIds,
    method: 'delete'
  })
}

// 导出第三方应用
export function exportThirdparty(query) {
  return request({
    url: '/system/thirdparty/export',
    method: 'get',
    params: query
  })
}

// 第三方应用状态修改
export function changeThirdpartyStatus(appId, status) {
  const data = {
    appId,
    status
  }
  return request({
    url: '/system/thirdparty',
    method: 'put',
    data: data
  })
}
