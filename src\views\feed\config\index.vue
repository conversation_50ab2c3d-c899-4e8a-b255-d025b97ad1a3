<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch"   label-width="68px">
      <el-form-item label="猪场id" prop="facid">
        <el-input
          v-model="queryParams.facid"
          placeholder="请输入猪场id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="饲喂器id" prop="fieldid">
        <el-input
          v-model="queryParams.fieldid"
          placeholder="请输入饲喂器id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="饲喂器名称" prop="fieldname">
        <el-input
          v-model="queryParams.fieldname"
          placeholder="请输入饲喂器名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="饲喂器类型" prop="fieldtype">
        <el-select v-model="queryParams.fieldtype" placeholder="请选择饲喂器类型" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="网关id" prop="switchid">
        <el-input
          v-model="queryParams.switchid"
          placeholder="请输入网关id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="猪舍id" prop="homeid">
        <el-input
          v-model="queryParams.homeid"
          placeholder="请输入猪舍id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传时间" prop="uptime">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.uptime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择上传时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="饲喂器工作状态" prop="fieldnetwork">
        <el-input
          v-model="queryParams.fieldnetwork"
          placeholder="请输入饲喂器工作状态"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报警信息" prop="alarmmsg">
        <el-input
          v-model="queryParams.alarmmsg"
          placeholder="请输报警信息"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="耳缺号" prop="mid">
        <el-input
          v-model="queryParams.mid"
          placeholder="请输入耳缺号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电子耳牌" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          placeholder="请输入电子耳牌"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="出生日期" prop="dbirthdate">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.dbirthdate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择出生日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="群组编号" prop="ngroupid">
        <el-input
          v-model="queryParams.ngroupid"
          placeholder="请输入群组编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="饲喂曲线" prop="ncurveindex">
        <el-input
          v-model="queryParams.ncurveindex"
          placeholder="请输入饲喂曲线"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="胎次" prop="nparity">
        <el-input
          v-model="queryParams.nparity"
          placeholder="请输入胎次"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- 0：入群状态 Entered         输入入群处理后，母猪处于入群状态
1：空怀状态 Open  输入妊娠检查处理并且检查结果为阴性，母猪状态改为空怀状态
2：哺乳状态 Lactating  输入分娩处理后母猪状态改变为补乳状态
3：断奶状态 Weaned  输入断奶处理后母猪状态改变为断奶状态
4：配种状态 Served  输入配种或人工授精处理后母猪状态改变为配种状态.
5：怀孕状态 Pregnant 输入妊娠检查处理后母猪状态改变为怀孕状态
6：流产状态 Aborted 输入流产处理后母猪状态改变为流产状态
7：淘汰状态 Removed 输入淘汰处理后母猪状态改变为淘汰状态
8：待淘汰状态 To Be Culled 输入待淘汰处理后母猪状态改变为待淘汰状态
9：发情后不配种 Heat No Service 母猪已经断奶但在生产日历定义的发情不配种期间内没有配种。 -->

      <el-form-item label="生产状态" prop="nstate">
        <el-input
          v-model="queryParams.nstate"
          placeholder="请选择生产状态"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- 0极度瘦弱 1偏瘦 2正常 3偏胖 4极度肥胖 -->
      <el-form-item label="体况" prop="ncondition">
        <el-input
          v-model="queryParams.ncondition"
          placeholder="请选择体况"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="每次投放量" prop="nput">
        <el-input
          v-model="queryParams.nput"
          placeholder="请输入每次投放量"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂次数" prop="ndeffnum">
        <el-input
          v-model="queryParams.ndeffnum"
          placeholder="请输入默认饲喂次数"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂速率" prop="ndeffspeed">
        <el-input
          v-model="queryParams.ndeffspeed"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- <el-form-item label="默认饲喂速率" prop="colStr1">
        <el-input
          v-model="queryParams.colStr1"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂速率" prop="colStr2">
        <el-input
          v-model="queryParams.colStr2"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂速率" prop="colStr3">
        <el-input
          v-model="queryParams.colStr3"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂速率" prop="colStr4">
        <el-input
          v-model="queryParams.colStr4"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂速率" prop="colInt1">
        <el-input
          v-model="queryParams.colInt1"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂速率" prop="colInt2">
        <el-input
          v-model="queryParams.colInt2"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂速率" prop="colFloat1">
        <el-input
          v-model="queryParams.colFloat1"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认饲喂速率" prop="colFloat2">
        <el-input
          v-model="queryParams.colFloat2"
          placeholder="请输入默认饲喂速率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:control:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:control:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:control:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:control:export']"
        >导出</el-button>
      </el-col>
	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="controlList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="猪场id" align="center" prop="facid" />
      <el-table-column label="饲喂器id" align="center" prop="fieldid" />
      <el-table-column label="饲喂器名称" align="center" prop="fieldname" />
      <el-table-column label="饲喂器类型" align="center" prop="fieldtype" />
      <el-table-column label="网关id" align="center" prop="switchid" />
      <el-table-column label="猪舍id" align="center" prop="homeid" />
      <el-table-column label="上传时间" align="center" prop="uptime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.uptime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="饲喂器工作状态" align="center" prop="fieldnetwork" />
      <el-table-column label="报警信息" align="center" prop="alarmmsg" />
      <el-table-column label="耳缺号" align="center" prop="mid" />
      <el-table-column label="电子耳牌" align="center" prop="mrfid" />
      <el-table-column label="出生日期" align="center" prop="dbirthdate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dbirthdate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="群组编号" align="center" prop="ngroupid" />
      <el-table-column label="饲喂曲线" align="center" prop="ncurveindex" />
      <el-table-column label="胎次" align="center" prop="nparity" />
      <el-table-column label="生产状态" align="center" prop="nstate" />
      <el-table-column label="体况" align="center" prop="ncondition" />
      <el-table-column label="每次投放量" align="center" prop="nput" />
      <el-table-column label="默认饲喂次数" align="center" prop="ndeffnum" />
      <el-table-column label="默认饲喂速率" align="center" prop="ndeffspeed" />

      <!-- <el-table-column label="默认饲喂速率" align="center" prop="colStr1" />
      <el-table-column label="默认饲喂速率" align="center" prop="colStr2" />
      <el-table-column label="默认饲喂速率" align="center" prop="colStr3" />
      <el-table-column label="默认饲喂速率" align="center" prop="colStr4" />
      <el-table-column label="默认饲喂速率" align="center" prop="colInt1" />
      <el-table-column label="默认饲喂速率" align="center" prop="colInt2" />
      <el-table-column label="默认饲喂速率" align="center" prop="colFloat1" />
      <el-table-column label="默认饲喂速率" align="center" prop="colFloat2" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:control:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:control:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="猪场id" prop="facid">
          <el-input v-model="form.facid" placeholder="请输入猪场id" />
        </el-form-item>
        <el-form-item label="饲喂器id" prop="fieldid">
          <el-input v-model="form.fieldid" placeholder="请输入饲喂器id" />
        </el-form-item>
        <el-form-item label="饲喂器名称" prop="fieldname">
          <el-input v-model="form.fieldname" placeholder="请输入饲喂器名称" />
        </el-form-item>
        <el-form-item label="饲喂器类型" prop="fieldtype">
          <el-select v-model="form.fieldtype" placeholder="请选择饲喂器类型">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="网关id" prop="switchid">
          <el-input v-model="form.switchid" placeholder="请输入网关id" />
        </el-form-item>
        <el-form-item label="猪舍id" prop="homeid">
          <el-input v-model="form.homeid" placeholder="请输入猪舍id" />
        </el-form-item>
        <el-form-item label="上传时间" prop="uptime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.uptime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择上传时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="饲喂器工作状态" prop="fieldnetwork">
          <el-input v-model="form.fieldnetwork" placeholder="请输入饲喂器工作状态" />
        </el-form-item>
        <el-form-item label="报警信息" prop="alarmmsg">
          <el-input v-model="form.alarmmsg" placeholder="请输入报警信息" />
        </el-form-item>
        <el-form-item label="耳缺号" prop="mid">
          <el-input v-model="form.mid" placeholder="请输入耳缺号" />
        </el-form-item>
        <el-form-item label="电子耳牌" prop="mrfid">
          <el-input v-model="form.mrfid" placeholder="请输入电子耳牌" />
        </el-form-item>
        <el-form-item label="出生日期" prop="dbirthdate">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.dbirthdate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出生日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="群组编号" prop="ngroupid">
          <el-input v-model="form.ngroupid" placeholder="请输入群组编号" />
        </el-form-item>
        <el-form-item label="饲喂曲线" prop="ncurveindex">
          <el-input v-model="form.ncurveindex" placeholder="请输入饲喂曲线" />
        </el-form-item>
        <el-form-item label="胎次" prop="nparity">
          <el-input v-model="form.nparity" placeholder="请输入胎次" />
        </el-form-item>
        <el-form-item label="生产状态" prop="nstate">
          <el-input v-model="form.nstate" placeholder="请输入生产状态" />
        </el-form-item>
        <el-form-item label="体况" prop="ncondition">
          <el-input v-model="form.ncondition" placeholder="请输入体况" />
        </el-form-item>
        <el-form-item label="每次投放量" prop="nput">
          <el-input v-model="form.nput" placeholder="请输入每次投放量" />
        </el-form-item>
        <el-form-item label="默认饲喂次数" prop="ndeffnum">
          <el-input v-model="form.ndeffnum" placeholder="请输入默认饲喂次数" />
        </el-form-item>
        <el-form-item label="默认饲喂速率" prop="ndeffspeed">
          <el-input v-model="form.ndeffspeed" placeholder="请输入默认饲喂速率" />
        </el-form-item>

        <!-- <el-form-item label="默认饲喂速率" prop="colStr1">
          <el-input v-model="form.colStr1" placeholder="请输入默认饲喂速率" />
        </el-form-item>
        <el-form-item label="默认饲喂速率" prop="colStr2">
          <el-input v-model="form.colStr2" placeholder="请输入默认饲喂速率" />
        </el-form-item>
        <el-form-item label="默认饲喂速率" prop="colStr3">
          <el-input v-model="form.colStr3" placeholder="请输入默认饲喂速率" />
        </el-form-item>
        <el-form-item label="默认饲喂速率" prop="colStr4">
          <el-input v-model="form.colStr4" placeholder="请输入默认饲喂速率" />
        </el-form-item>
        <el-form-item label="默认饲喂速率" prop="colInt1">
          <el-input v-model="form.colInt1" placeholder="请输入默认饲喂速率" />
        </el-form-item>
        <el-form-item label="默认饲喂速率" prop="colInt2">
          <el-input v-model="form.colInt2" placeholder="请输入默认饲喂速率" />
        </el-form-item>
        <el-form-item label="默认饲喂速率" prop="colFloat1">
          <el-input v-model="form.colFloat1" placeholder="请输入默认饲喂速率" />
        </el-form-item>
        <el-form-item label="默认饲喂速率" prop="colFloat2">
          <el-input v-model="form.colFloat2" placeholder="请输入默认饲喂速率" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listControl, getControl, delControl, addControl, updateControl, exportControl } from "@/api/feed/config";

export default {
  name: "Control",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      controlList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        facid: null,
        fieldid: null,
        fieldname: null,
        fieldtype: null,
        switchid: null,
        homeid: null,
        uptime: null,
        fieldnetwork: null,
        alarmmsg: null,
        mid: null,
        mrfid: null,
        dbirthdate: null,
        ngroupid: null,
        ncurveindex: null,
        nparity: null,
        nstate: null,
        ncondition: null,
        nput: null,
        ndeffnum: null,
        ndeffspeed: null,
        colStr1: null,
        colStr2: null,
        colStr3: null,
        colStr4: null,
        colInt1: null,
        colInt2: null,
        colFloat1: null,
        colFloat2: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        fieldid: [
          { required: true, message: "饲喂器id不能为空", trigger: "blur" }
        ],
        fieldname: [
          { required: true, message: "饲喂器名称不能为空", trigger: "blur" }
        ],
        switchid: [
          { required: true, message: "网关id不能为空", trigger: "blur" }
        ],
        homeid: [
          { required: true, message: "猪舍id不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listControl(this.queryParams).then(response => {
        this.controlList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        facid: null,
        fieldid: null,
        fieldname: null,
        fieldtype: null,
        switchid: null,
        homeid: null,
        uptime: null,
        fieldnetwork: null,
        alarmmsg: null,
        mid: null,
        mrfid: null,
        dbirthdate: null,
        ngroupid: null,
        ncurveindex: null,
        nparity: null,
        nstate: null,
        ncondition: null,
        nput: null,
        ndeffnum: null,
        ndeffspeed: null,
        colStr1: null,
        colStr2: null,
        colStr3: null,
        colStr4: null,
        colInt1: null,
        colInt2: null,
        colFloat1: null,
        colFloat2: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【请填写功能名称】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getControl(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【请填写功能名称】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateControl(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addControl(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除【请填写功能名称】编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delControl(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有【请填写功能名称】数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportControl(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>