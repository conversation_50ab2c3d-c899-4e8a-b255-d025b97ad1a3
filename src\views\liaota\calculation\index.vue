<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <!-- <el-form-item label="单元id" prop="danyuanid">
        <el-input
          v-model="queryParams.danyuanid"
          placeholder="请输入单元id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="单元名称" prop="danyuanname">
        <el-input
          v-model="queryParams.danyuanname"
          placeholder="请输入单元名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="料塔id" prop="slaveid">
        <el-input
          v-model="queryParams.slaveid"
          placeholder="请输入料塔id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="料塔名称" prop="slavename">
        <el-input
          v-model="queryParams.slavename"
          placeholder="请输入料塔名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="打料开始时间" prop="begintime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="queryParams.begintime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择打料开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="打料结束时间" prop="enddtime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="queryParams.enddtime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择打料结束时间">
          </el-date-picker>
        </el-form-item> -->
      <!-- <el-form-item label="猪场id" prop="mfactory">
          <el-input
            v-model="queryParams.mfactory"
            placeholder="请输入猪场id"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
      <!-- <el-form-item label="料塔名" prop="colstring1">
          <el-input
            v-model="queryParams.colstring1"
            placeholder="请输入料塔名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="料塔名" prop="colstring2">
          <el-input
            v-model="queryParams.colstring2"
            placeholder="请输入料塔名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          icon="el-icon-document"
          type="info"
          size="mini"
          @click="handleLookRule"
          >规则</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:peizhi:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:peizhi:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:peizhi:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:peizhi:export']"
          >导出</el-button
        >
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="peizhiList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="单元id" align="center" prop="danyuanid" /> -->
      <el-table-column
        label="单元名称"
        align="center"
        prop="danyuanname"
        fixed
      />
      <!-- <el-table-column label="料塔id" align="center" prop="slaveid" /> -->
      <el-table-column label="料塔名称" align="center" prop="slavename" fixed />
      <el-table-column
        label="打料开始时间"
        align="center"
        prop="begintime"
        width="180"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.begintime"
            readonly
            size="mini"
            placeholder="选择打料开始时间"
            suffix-icon="el-icon-setting"
            @focus="handleFocus('begin', scope.row)"
          />
          <!-- <span>{{ parseTime(scope.row.begintime, "{y}-{m}-{d}") }}</span> -->
        </template>
      </el-table-column>
      <el-table-column
        label="打料结束时间"
        align="center"
        prop="enddtime"
        width="180"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.enddtime"
            readonly
            size="mini"
            placeholder="选择打料结束时间"
            suffix-icon="el-icon-setting"
            @focus="handleFocus('end', scope.row)"
          />
          <!-- <span>{{ parseTime(scope.row.enddtime, "{y}-{m}-{d}") }}</span> -->
        </template>
      </el-table-column>
      <el-table-column
        label="打料开始重量(T)"
        align="center"
        prop="beginweight"
        width="180"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.beginweight"
            placeholder="请输入打料开始重量"
            readonly
            disabled
          />
        </template>
      </el-table-column>
      <el-table-column
        label="打料结束重量(T)"
        align="center"
        prop="endweight"
        width="180"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.endweight"
            placeholder="请输入打料结束重量"
            readonly
            disabled
          />
        </template>
      </el-table-column>
      <el-table-column
        label="打料量(T)"
        align="center"
        prop="weight"
        width="180"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.weight"
            placeholder="请输入打料量"
            readonly
            disabled
          />
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="submitFormCalculation(scope.row)"
            v-hasPermi="['system:peizhi:calculation']"
            >保存</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resertCalculation(scope.row)"
            v-hasPermi="['system:peizhi:calculation']"
            >清空</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【单元料塔配置】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="单元" prop="danyuanid">
          <el-select
            v-model="form.danyuanid"
            placeholder="请选择单元"
            @change="changeDanyuanname"
          >
            <el-option
              v-for="item in danyuanOptions"
              :key="item.id"
              :label="item.danyuanname"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('liaota.towerName')" prop="slaveid">
          <el-select
            v-model="form.slaveid"
            :placeholder="$t('liaota.enterTowerName')"
            @change="changeSlavename"
          >
            <el-option
              v-for="item in slaveOptions"
              :key="item.id"
              :label="item.slavename"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="titleCalculation"
      :visible.sync="openCalculation"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formCalculation"
        :model="formCalculation"
        :rules="rulesCalculation"
        label-width="80px"
      >
        <el-form-item label="单元id" prop="danyuanid">
          <el-input
            v-model="formCalculation.danyuanid"
            placeholder="请输入单元id"
            readonly
          />
        </el-form-item>
        <el-form-item label="单元名称" prop="danyuanname">
          <el-input
            v-model="formCalculation.danyuanname"
            placeholder="请输入单元名称"
            readonly
          />
        </el-form-item>
        <el-form-item label="料塔id" prop="slaveid">
          <el-input
            v-model="formCalculation.slaveid"
            placeholder="请输入料塔id"
            readonly
          />
        </el-form-item>
        <el-form-item label="料塔名称" prop="slavename">
          <el-input
            v-model="formCalculation.slavename"
            placeholder="请输入料塔名称"
            readonly
          />
        </el-form-item>
        <el-form-item label="打料开始时间" prop="begintime">
          <!-- <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.begintime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择打料开始时间"
          >
          </el-date-picker> -->
          <el-input
            v-model="formCalculation.begintime"
            readonly
            size="mini"
            placeholder="选择打料开始时间"
            suffix-icon="el-icon-setting"
            @focus="handleFocus('begin', formCalculation)"
          />
        </el-form-item>
        <el-form-item label="打料结束时间" prop="enddtime">
          <!-- <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="formCalculation.enddtime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择打料结束时间"
          >
          </el-date-picker> -->
          <el-input
            v-model="formCalculation.enddtime"
            readonly
            size="mini"
            placeholder="选择打料结束时间"
            suffix-icon="el-icon-setting"
            @focus="handleFocus('end', formCalculation)"
          />
        </el-form-item>
        <el-form-item label="打料开始重量" prop="beginweight">
          <el-input
            v-model="formCalculation.beginweight"
            placeholder="请输入打料开始重量"
            readonly
          />
        </el-form-item>
        <el-form-item label="打料结束重量" prop="endweight">
          <el-input
            v-model="formCalculation.endweight"
            placeholder="请输入打料结束重量"
            readonly
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormCalculation"
          >确 定</el-button
        >
        <el-button @click="cancelCalculation">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="titleSlave"
      :visible.sync="openSlave"
      width="600px"
      append-to-body
    >
      <el-form
        :model="queryParamsSlave"
        ref="queryFormSlave"
        :inline="true"
        label-width="68px"
        class="demo-form-inline"
      >
        <el-form-item :label="$t('common.uploadTime')" prop="dateLine">
          <el-date-picker
            clearable
            size="small"
            style="width: 180px"
            v-model="queryParamsSlave.dateLine"
            type="date"
            value-format="yyyy-MM-dd"
            :placeholder="$t('common.chooseUploadTime')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            type="cyan"
            icon="el-icon-search"
            size="mini"
            @click="handleQuerySlave"
            >{{ $t("common.search") }}</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        v-loading="loadingSlave"
        :data="slaveList"
        :cell-style="{ padding: '0' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" fixed />
        <el-table-column
          :label="$t('common.serialNumber')"
          align="center"
          prop="id"
          width="80"
        />
        <!-- <el-table-column
          :label="$t('liaota.towerId')"
          align="center"
          prop="slaveid"
        /> -->
        <el-table-column
          :label="$t('liaota.towerName')"
          align="center"
          prop="slavename"
        />

        <el-table-column
          :label="$t('common.uploadTime')"
          align="center"
          prop="uptime"
          width="160"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.uptime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('liaota.weight')"
          align="center"
          prop="weight"
        />
        <!-- <el-table-column
          :label="$t('liaota.alarmInformation')"
          align="center"
          prop="alarmmsg"
          show-overflow-tooltip
        /> -->
      </el-table>
      <pagination
        v-show="totalSlave > 0"
        :total="totalSlave"
        :page.sync="queryParamsSlave.pageNum"
        :limit.sync="queryParamsSlave.pageSize"
        @pagination="getListSlave"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCheck" :disabled="single"
          >确 定</el-button
        >
        <el-button @click="cancelCheck">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import {
  listPeizhi,
  getPeizhi,
  delPeizhi,
  addPeizhi,
  updatePeizhi,
  exportPeizhi,
} from "@/api/liaota/calculation";
import { addTongji } from "@/api/liaota/danyuanStatistics";
import { listLiaotaHistory } from "@/api/liaota/history";
import { listDanyuan } from "@/api/liaota/danyuan";
import { listSlave } from "@/api/liaota/slave";
import { isNil } from "lodash";
import { formatDay } from "@/utils";

export default {
  name: "Peizhi",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      loadingSlave: true,
      // 选中数组
      ids: [],
      itemCheck: [],
      typeCheck: "",

      slaveOptions: [],
      danyuanOptions: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【单元料塔配置】表格数据
      peizhiList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      titleCalculation: "",
      // 是否显示弹出层
      openCalculation: false,
      // 表单参数
      formCalculation: {},

      slaveList: [],
      totalSlave: 0,
      openSlave: false,
      titleSlave: "",
      // 查询参数
      queryParamsSlave: {
        pageNum: 1,
        pageSize: 100,
        slaveid: null,
        dateLine: null,
      },

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        danyuanid: null,
        danyuanname: null,
        slaveid: null,
        slavename: null,
        begintime: null,
        enddtime: null,
        mfactory: null,
        colstring1: null,
        colstring2: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        danyuanid: [
          { required: true, message: "单元id不能为空", trigger: "blur" },
        ],
        slaveid: [
          { required: true, message: "料塔id不能为空", trigger: "blur" },
        ],
        mfactory: [
          { required: true, message: "猪场id不能为空", trigger: "blur" },
        ],
      },
      rulesCalculation: {
        mfactory: [
          { required: true, message: "猪场id不能为空", trigger: "blur" },
        ],
        danyuanid: [
          { required: true, message: "单元id不能为空", trigger: "blur" },
        ],
        slaveid: [
          { required: true, message: "料塔id不能为空", trigger: "blur" },
        ],
        // begintime: [
        //   { required: true, message: "打料开始时间不能为空", trigger: "blur" },
        // ],
        // enddtime: [
        //   { required: true, message: "打料结束时间不能为空", trigger: "blur" },
        // ],
        // beginweight: [
        //   { required: true, message: "打料开始重量不能为空", trigger: "blur" },
        // ],
        // endweight: [
        //   { required: true, message: "打料结束重量不能为空", trigger: "blur" },
        // ],
        // weight: [
        //   { required: true, message: "打料量不能为空", trigger: "blur" }
        // ],
      },
    };
  },
  mounted() {
    listSlave({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.slaveOptions = response.rows;
      }
    );
    listDanyuan({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.danyuanOptions = response.rows;
      }
    );
  },
  watch: {
    queryParamsSlave: {
      handler() {
        this.getListSlave();
      },
      deep: true,
    },
  },
  created() {
    this.getList();
  },
  methods: {
    changeSlavename(value) {
      this.form.slavename = this.slaveOptionsFormat(value);
    },
    changeDanyuanname(value) {
      this.form.danyuanname = this.danyuanOptionsFormat(value);
    },

    slaveOptionsFormat(value) {
      let arrObjFilter = this.slaveOptions
        .filter((ele) => ele.id == value)
        .map((ele) => {
          return ele.slavename;
        });
      // console.log("arrObjFilter", arrObjFilter);
      return arrObjFilter[0];
    },
    danyuanOptionsFormat(value) {
      let arrObjFilter = this.danyuanOptions
        .filter((ele) => ele.id == value)
        .map((ele) => {
          return ele.danyuanname;
        });
      return arrObjFilter[0];
    },
    /** 查询【单元料塔配置】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listPeizhi(this.queryParams).then((response) => {
        this.peizhiList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        danyuanid: null,
        danyuanname: null,
        slaveid: null,
        slavename: null,
        begintime: null,
        enddtime: null,
        mfactory: null,
        colstring1: null,
        colstring2: null,
      };
      this.resetForm("form");
    },
    resetCalculation() {
      this.formCalculation = {
        id: null,
        mfactory: null,
        danyuanid: null,
        danyuanname: null,
        slaveid: null,
        slavename: null,
        begintime: null,
        enddtime: null,
        beginweight: null,
        endweight: null,
        weight: null,
        msg: null,
      };
      this.resetForm("formCalculation");
    },
    getListSlave() {
      this.loadingSlave = true;
      this.queryParamsSlave.mfactory = this.$store.state.settings.nowPigFarm;
      listLiaotaHistory(this.queryParamsSlave).then((response) => {
        this.slaveList = response.rows;
        this.totalSlave = response.total;
        this.loadingSlave = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleQuerySlave() {
      this.queryParamsSlave.pageNum = 1;
      this.getListSlave();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.itemCheck = selection;
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【单元料塔配置】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPeizhi(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【单元料塔配置】";
      });
    },
    /** 计算按钮操作 */
    // handleCalculation(row) {
    //   this.resetCalculation();
    //   const id = row.id || this.ids;
    //   getPeizhi(id).then((response) => {
    //     this.formCalculation = response.data;
    //     this.openCalculation = true;
    //     this.titleCalculation = "计算【单元料塔用量】";
    //   });
    //   //   this.formCalculation = {};
    //   //   this.openCalculation = true;
    //   //   this.titleCalculation = "计算【单元料塔用量】";
    // },
    submitFormCalculation(from) {
      if (!isNil(from.weight)) {
        addTongji(from).then((response) => {
          this.msgSuccess("新增成功,请跳转至单元打料统计页面查看");
          this.openCalculation = false;
        });
      } else {
        this.msgError(
          "计算出来的打料量不得为空，请选择打料开始时间和打料结束时间"
        );
      }

      // this.$refs["formCalculation"].validate((valid) => {
      //   if (valid) {
      //     this.formCalculation.weight =
      //       this.formCalculation.beginweight - this.formCalculation.endweight;
      //     addTongji(this.formCalculation).then((response) => {
      //       this.msgSuccess("新增成功,请跳转至单元打料统计页面查看");
      //       this.openCalculation = false;
      //     });
      //   }
      // });
    },
    resertCalculation(from) {
      from.begintime = null;
      from.enddtime = null;
      from.beginweight = null;
      from.endweight = null;
      from.weight = null;
    },
    cancelCalculation() {
      this.openCalculation = false;
      this.resetCalculation();
    },
    handleFocus(type, from) {
      //打开一个table
      this.formCalculation = from;
      this.loadingSlave = true;
      this.typeCheck = type;
      // this.queryParamsSlave.slaveid = from.slaveid;
      this.queryParamsSlave.slavename = from.slavename;
      var preDate = formatDay(new Date().getTime());
      this.queryParamsSlave.dateLine = preDate;
      this.queryParamsSlave.mfactory = this.$store.state.settings.nowPigFarm;
      listLiaotaHistory(this.queryParamsSlave).then((response) => {
        this.loadingSlave = false;
        this.slaveList = response.rows;
        this.totalSlave = response.total;
        this.openSlave = true;
        this.titleSlave = "选择【料塔打料数据】";
      });
    },
    submitCheck() {
      const itemCheck = this.itemCheck;
      if (this.typeCheck == "begin") {
        this.formCalculation.begintime = itemCheck[0].uptime;
        this.formCalculation.beginweight = itemCheck[0].weight;
      } else if (this.typeCheck == "end") {
        this.formCalculation.enddtime = itemCheck[0].uptime;
        this.formCalculation.endweight = itemCheck[0].weight;
      }
      if (this.formCalculation.beginweight && this.formCalculation.endweight) {
        this.formCalculation.weight = (
          this.formCalculation.beginweight - this.formCalculation.endweight
        ).toFixed(3);
      }
      this.openSlave = false;
    },
    cancelCheck() {
      this.openSlave = false;
    },
    handleLookRule() {
      this.$alert(
        "<ul style='padding: 0px;margin: 0px;'><li style='list-style: none;padding: 0px;margin: 0px;'><strong>规则一. </strong>保育料塔1-1到1-5，保育料塔2-1到2-5，后备料塔3-5到3-6，分娩料塔3-7到3-8，这些料塔供应了2层，2层不能同时打料。</li><li style='list-style: none;padding: 0px;margin: 0px;'><strong>规则二. </strong>料塔在料车补料的同时，不能出料。</li><li style='list-style: none;padding: 0px;margin: 0px;'><strong>规则三. </strong>保育料塔1-1到1-5，保育料塔2-1到2-5，每个料塔通过2条舍内塞链供应3个单元，这2条舍内塞链不能同时打料。</li><li style='list-style: none;padding: 0px;margin: 0px;'><strong>规则四. </strong>打料的时候，需记录打料开始时间和打料结束时间（结束时间也可以用界面上数据稳定不变的方式判断）；若是使用规则1料塔打料，就必须记录达打料结束时间。</li></ul>",
        "打料规则",
        {
          confirmButtonText: "确定",
          dangerouslyUseHTMLString: true,
          // callback: (action) => {
          //   this.$message({
          //     type: "info",
          //     // message: `action: ${action}`,
          //   });
          // },
        }
      );
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updatePeizhi(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addPeizhi(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【单元料塔配置】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delPeizhi(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【单元料塔配置】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportPeizhi(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>