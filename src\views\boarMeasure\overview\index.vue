<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="3" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            :placeholder="$t('boarMeasure.pleaseInputPigHouseName')"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span
                ><svg-icon
                  v-if="data.icon"
                  :icon-class="data.icon"
                  class-name="card-panel-icon"
                />
                {{ node.label }}
              </span>
            </span>
          </el-tree>
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="21" :xs="24">
        <!-- <el-row :gutter="10" class="mb8">
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row> -->
        <el-row class="warning">
          <span>{{ $t("boarMeasure.lessThanSomeGrams") }}</span>
          <div style="display: inline-block; width: 70px">
            <el-input v-model="minFeed" size="small" />
          </div>

          <span>{{ $t("boarMeasure.pigIs") }} </span>

          <span style="color: red">{{ this.noFeedNumber }}</span>
          <span>{{ $t("boarMeasure.heads") }} </span>
          <span :style="this.noFeedNumber <= 0 && 'display: none'"
            >，{{ $t("boarMeasure.midIs") }}
            <ear-tag-link
              class="link"
              user-select="true"
              style="
                display: inline-block;
                color: #ffffff;
                margin: 4px 4px;
                padding: 2px 2px;
                background-color: red;
                border-radius: 5px;
              "
              v-for="(item, index) in noFeedArr"
              :key="index"
              :ear-tag="item.mid"
              :nindex="item.nindex"
              route-path="/boarMeasure/breed_measureday"
              >{{ item.mid }}
            </ear-tag-link>
          </span>
        </el-row>
        <el-table
          v-loading="loading"
          :data="pigdataList"
          :cell-style="{ padding: '0' }"
        >
          <!-- <el-table-column
            label="序号"
            align="center"
            prop="indexn"
            width="50"
          /> -->
          <el-table-column
            :label="$t('boarMeasure.nindex')"
            align="center"
            prop="nindex"
            width="70"
            sortable
          />
          <el-table-column
            :label="$t('boarMeasure.mid')"
            align="center"
            prop="mid"
            width="180"
            sortable
          />
          <el-table-column
            :label="$t('boarMeasure.mrfid')"
            align="center"
            prop="mrfid"
            width="150"
            sortable
          >
            <!-- <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nIngestionSToday) <= 0 &&
                  Number(scope.row.nIngestionSLastday) <= 0
                    ? 'color: red '
                    : Number(scope.row.nIngestionSToday) <= 0
                    ? 'color: #e6a700 '
                    : 'color: black '
                "
              >
                {{ scope.row.mrfid }}</span
              >
            </template> -->
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.nIngestionSToday')"
            align="right"
            sortable
            prop="nIngestionSToday"
            width="130"
          >
            <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nIngestionSToday) <= 0
                    ? 'color: #f05d18'
                    : 'color: black '
                "
              >
                {{ scope.row.nIngestionSToday | formatThousands(0) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.nIngestionSLastday')"
            align="right"
            sortable
            prop="nIngestionSLastday"
            width="130"
          >
            <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nIngestionSLastday) <= 0
                    ? 'color: #f05d18'
                    : 'color: black '
                "
              >
                {{ scope.row.nIngestionSLastday | formatThousands(0) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.nIngestionSLast2day')"
            align="right"
            sortable
            prop="nIngestionSLast2day"
            width="130"
          >
            <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nIngestionSLast2day) <= 0
                    ? 'color: #f05d18'
                    : 'color: black '
                "
              >
                {{ scope.row.nIngestionSLast2day | formatThousands(0) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.nIngestionSLast3day')"
            align="right"
            sortable
            prop="nIngestionSLast3day"
            width="130"
          >
            <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nIngestionSLast3day) <= 0
                    ? 'color: #f05d18'
                    : 'color: black '
                "
              >
                {{ scope.row.nIngestionSLast3day | formatThousands(0) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.nweightLastday')"
            align="right"
            prop="nweightlastday"
            sortable
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nweightlastday) <= 0 ||
                  (scope.row.nweightlast2day - scope.row.nweightlastday > 5 &&
                    scope.row.nweightlast3day - scope.row.nweightlast2day > 5)
                    ? 'color: #f05d18'
                    : 'color: black '
                "
              >
                {{ scope.row.nweightlastday | formatNumber(1) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.nweightlast2day')"
            align="right"
            prop="nweightlast2day"
            sortable
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nweightlast2day) <= 0
                    ? 'color: #f05d18'
                    : 'color: black '
                "
              >
                {{ scope.row.nweightlast2day | formatNumber(1) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.nweightlast3day')"
            align="right"
            prop="nweightlast3day"
            sortable
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nweightlast3day) <= 0
                    ? 'color: #f05d18'
                    : 'color: black '
                "
              >
                {{ scope.row.nweightlast3day | formatNumber(1) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.remarks')"
            align="center"
            prop="remarks"
            width="200px"
            sortable
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span style="color: #f05d18">
                {{
                  scope.row.nweightlast2day - scope.row.nweightlastday > 5 &&
                  scope.row.nweightlast3day - scope.row.nweightlast2day > 5
                    ? $t("boarMeasure.weight3dayerror")
                    : " "
                }}</span
              >
            </template>
          </el-table-column>

          <!--          <el-table-column-->
          <!--            :label="$t('boarMeasure.dstartdate')"-->
          <!--            align="center"-->
          <!--            prop="dstartdate"-->
          <!--            :show-overflow-tooltip="true"-->
          <!--            sortable-->
          <!--          >-->
          <!--            <template slot-scope="scope">-->
          <!--              <span>{{ parseTime(scope.row.dstartdate, "{y}-{m}-{d}") }}</span>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <!--          <el-table-column-->
          <!--            :label="$t('boarMeasure.ntype')"-->
          <!--            align="center"-->
          <!--            prop="ntype"-->
          <!--          >-->
          <!--            <template slot-scope="scope">-->
          <!--              <el-tag-->
          <!--                effect="dark"-->
          <!--                :type="-->
          <!--                  scope.row.ntype && scope.row.ntype === 1-->
          <!--                    ? 'success'-->
          <!--                    : 'danger'-->
          <!--                "-->
          <!--                disable-transitions-->
          <!--              >-->
          <!--                &lt;!&ndash; {{-->
          <!--                  (scope.row.ntype === 1 || scope.row.ntype === 0) &&-->
          <!--                  typeList &&-->
          <!--                  typeList[scope.row.ntype].dictLabel-->
          <!--                }} &ndash;&gt;-->
          <!--                {{-->
          <!--                  scope.row.ntype === 1-->
          <!--                    ? $t("boarMeasure.mensurationNow")-->
          <!--                    : $t("boarMeasure.mensurationEnd")-->
          <!--                }}-->
          <!--              </el-tag>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
        </el-table>

        <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        /> -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listPigdataOverview } from "@/api/system/pigdata";
import { treeselect } from "@/api/system/control";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Overview",
  components: { Treeselect },
  data() {
    return {
      noFeedNumber: "",
      minFeed: "500",
      noFeedArr: [],
      //测定状态
      typeList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      pigdataList: null,
      // 弹出层标题
      title: "",
      // 猪场树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },

      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 100,
        indexn: null,
        mid: null,
        mrfid: null,
        dbirthdate: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        ddate: null,
        nweight: null,
        ntype: null,
      },
    };
  },
  //   watch: {
  //   queryParams: {
  //     handler() {
  //       this.getList();
  //     },
  //     deep: true,
  //   },
  // },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
    minFeed() {
      this.filterArr();
    },
  },
  created() {
    this.getDicts("sys_cd_status").then((response) => {
      this.typeList = response.data;
    });
    this.getList();
    this.getTreeselect();
  },
  methods: {
    /** 查询猪舍总览 */
    filterArr() {
      let filterArr = this.pigdataList.filter(
        (item) =>
          item.nIngestionSLastday < this.minFeed &&
          item.mrfid !== "000000000000000"
      );
      this.noFeedNumber = filterArr.length;
      this.noFeedArr = filterArr.map(function (obj, index) {
        return { mid: obj.mid, nindex: obj.nindex };
      });
    },
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      // this.queryParams.ntype = 1||"1";

      listPigdataOverview(this.queryParams)
        .then((response) => {
          this.total = response.total;
          this.pigdataList = response.rows;

          // 过滤低采食量的猪只（排除零耳牌）
          let filterArr = this.pigdataList.filter(
            (item) =>
              item.nIngestionSLastday < this.minFeed &&
              item.mrfid !== "000000000000000"
          );
          this.noFeedNumber = filterArr.length;
          this.noFeedArr = filterArr.map(function (obj) {
            return { mid: obj.mid, nindex: obj.nindex };
          });
        })
        .catch((error) => {
          console.error("获取猪舍总览数据失败:", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 查询猪舍结构树 */
    getTreeselect() {
      // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      treeselect({ mfactory: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          let icons = ["she", "lan", "zhan"];
          // console.log("11", this.simplifyToTwoLevels(response.data));
          let data = this.walk(
            this.simplifyToTwoLevels(response.data),
            (data, deep) => ({
              ...data,
              icon: icons[deep],
            }),
            0
          );
          // console.log("data", data);
          // console.log("data", this.simplifyToTwoLevels(data));
          // // 执行处理并输出结果
          // const result = this.simplifyToTwoLevels(data);
          // console.log(JSON.stringify(result, null, 2));

          this.deptOptions = data;
        }
      );
    },
    /**
     * 将原始数据处理为保留两个层级的数据结构
     * @param {Object} originalData 原始数据对象
     * @returns {Object} 处理后的数据，只保留猪舍和栏号两个层级
     */
    simplifyToTwoLevels(data) {
      // 深拷贝
      const copiedData = JSON.parse(JSON.stringify(data));

      // 遍历处理
      copiedData.forEach((barn) => {
        if (Array.isArray(barn.children)) {
          barn.children.forEach((pen) => {
            delete pen.children;
          });
        }
      });

      return copiedData;
    },
    walk(list, callback, deep = 0) {
      return list.map((it) => {
        const result = callback({ ...it }, deep);
        if (it.children) {
          result.children = this.walk(it.children, callback, deep + 1);
        }
        return result;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.pageNum = 1;
      // this.queryParams.indexn = data.id;
      this.queryParams.mdorm = data.mDorm;
      this.queryParams.nindex = data.nIndex;

      this.getList();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style scoped>
.warning {
  padding: 8px 6px;
  background-color: #fff6f7;
  border-radius: 4px;
  /* border-left: 5px solid #fe6c6f; */
  margin-bottom: 20px;
}
.link:hover {
  cursor: pointer;
}
</style>
