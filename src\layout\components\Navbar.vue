<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />

    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <!-- <span class="title">{{$t('menu.home')}}</span> -->
        <span class="right-menu-item pig-farm">
          {{ $t("menu.remainingDays") }}:{{ registTime }}{{ $t("menu.day") }}
          <!-- 剩余注册使用天数:10天 -->
        </span>
        <span class="right-menu-item pig-farm" style="color: #d2d2d2">|</span>
        <span class="right-menu-item pig-farm"
          >{{ $t("menu.currentFarm") }}:{{ nameFormatter }}</span
        >
        <!-- <span class="right-menu-item pig-farm"
          >切换语言</span
        > -->
        <search id="header-search" class="right-menu-item" />

        <!--        <el-tooltip content="源码地址" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>-->

        <!--        <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip>-->

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip
          :content="$t('menu.layoutSize')"
          effect="dark"
          placement="bottom"
        >
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
        <el-tooltip
          :content="$t('menu.switchFarms')"
          effect="dark"
          placement="bottom"
        >
          <!-- @click.native  事件的native修饰符只能在组件上使用，原生的html标签是不能使用的  @click.native 监听组件根元素的原生事件 。-->
          <!-- 事件的native修饰符只能在组件上使用，原生的html标签是不能使用的，这是因为我在input标签使用了native修饰符。 -->
          <!-- span是div的子元素，点击span的时候，发生了事件冒泡，响应在了span的父元素div上，所以对onclick事件的响应应该是父元素，div。 -->
          <div
            @click="switching = true"
            class="right-menu-item hover-effect"
            style="font-size: 22px"
          >
            <div>
              <svg-icon icon-class="changeFarm" class-name="card-panel-icon" />
            </div>
          </div>
        </el-tooltip>

        <div
          @click="getMesssage"
          class="right-menu-item hover-effect notification-icon"
          style="font-size: 22px"
        >
          <div :style="{ display: hasMesssage ? 'block' : 'none' }">
            <i class="el-icon-message-solid" />
            <span
              class="badge"
              id="badge"
              :style="{ display: hasNotification ? 'block' : 'none' }"
            ></span>
          </div>
        </div>

        <el-dropdown
          class="avatar-container right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <span class="right-menu-item pig-farm">{{
              $t("menu.switchLanguage")
            }}</span>
            <i class="el-icon-caret-bottom" />
            <!-- style="margin-top: -5px; margin-left: -5px" -->
          </div>
          <el-dropdown-menu slot="dropdown">
            <!-- <span @click="tab('zh')">中文</span>|
			<span @click="tab('en')">英文</span> -->
            <el-dropdown-item @click.native="tab('zh')">
              <span>{{ $t("menu.zh") }}</span>
            </el-dropdown-item>
            <el-dropdown-item @click.native="tab('en')">
              <span>{{ $t("menu.en") }}</span>
            </el-dropdown-item>
            <el-dropdown-item @click.native="tab('ru')">
              <span>{{ $t("menu.ru") }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>

      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar" />
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>{{ $t("menu.personalCenter") }}</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>{{ $t("menu.layoutSettings") }}</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="switching = true">
            <span>{{ $t("menu.switchFarms") }}</span>
          </el-dropdown-item>

          <el-dropdown
            class="avatar-container right-menu-item hover-effect"
            trigger="click"
          >
            <div class="avatar-wrapper">
              <el-dropdown-item class="right-menu-item pig-farm">{{
                $t("menu.switchLanguage")
              }}</el-dropdown-item>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="tab('zh')">
                <span>{{ $t("menu.zh") }}</span>
              </el-dropdown-item>
              <el-dropdown-item @click.native="tab('en')">
                <span>{{ $t("menu.en") }}</span>
              </el-dropdown-item>
              <el-dropdown-item @click.native="tab('ru')">
                <span>{{ $t("menu.ru") }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown-item divided @click.native="logout">
            <span>{{ $t("menu.signOut") }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Cookies from "js-cookie";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import RuoYiGit from "@/components/RuoYi/Git";
import RuoYiDoc from "@/components/RuoYi/Doc";
import { listFactory } from "@/api/system/factory";
import { getWebalertinfo } from "@/api/system/user";
import ResizeHandler from "../mixin/ResizeHandler";
// import store from '@/store'
var timerS = null; //首先可以全局定义一个定时器
export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc,
  },
  data() {
    return {
      registTime: Cookies.get("registTime"), //store.getters && store.getters.registTime
      pigFarmsOptions: [],
      dataInterval: 300 * 1000,
    };
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device"]),
    nameFormatter() {
      if (this.$store.state.settings.nowPigFarm) {
        if (
          JSON.stringify(
            this.pigFarmsOptions.filter(
              (item) => item.id === this.$store.state.settings.nowPigFarm
            )
          ) !== "[]"
        ) {
          return this.pigFarmsOptions.filter(
            (item) => item.id === this.$store.state.settings.nowPigFarm
          )[0].facname;
        }
      }
    },
    switching: {
      get() {
        return this.$store.state.settings.showSwitching;
      },
      set(val) {
        this.$store.dispatch("settings/changeSwitching", {
          key: "showSwitching",
          value: val,
        });
      },
    },
    hasMesssage: {
      get() {
        //this.$store.state.user.user.webalert
        return this.$store.state.user.user.webalert ? true : false;
      },
    },
    hasNotification: {
      get() {
        return this.$store.state.settings.hasNotification;
      },
      set(val) {
        this.$store.dispatch("settings/changeSwitching", {
          key: "hasNotification",
          value: val,
        });
      },
    },
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
  },

  mounted() {
    //渲染之后
    //this.getDatas();
    listFactory().then((response) => {
      this.pigFarmsOptions = response.rows;
    });
    if (this.$store.state.user.user.webalert == 1) {
      this.getMesssage();
    }
  },
  created() {
    this.clearTimerS();
    //实时刷新
    if (this.$store.state.user.user.webalert == 1) {
      timerS = setInterval(() => {
        this.getMesssage();
      }, this.dataInterval);
    } else {
      this.$store.dispatch("settings/changeSwitching", {
        key: "hasNotification",
        value: false,
      });
    }
  },

  methods: {
    clearTimerS() {
      try {
        window.clearInterval(timerS);
      } catch (error) {}
      window.timerS = null;
    },
    getMesssage() {
      if (this.$store.state.user.user) {
        getWebalertinfo({ userId: this.$store.state.user.user.userId }).then(
          (response) => {
            if (Object.keys(response.rows).length != 0) {
              this.$store.dispatch("settings/changeSwitching", {
                key: "hasNotification",
                value: true,
              });
              this.$notify.closeAll(); // 清除所有通知
              let notificationOffset = 70;
              for (let i = 0; i < response.rows.length; i++) {
                if (response.rows[i].type && response.rows[i].msg) {
                  this.$notify({
                    title: response.rows[i].type,
                    dangerouslyUseHTMLString: true,
                    message: response.rows[i].msg.join("<br><br>"),
                    type: "warning",
                    offset: notificationOffset,
                    customClass: "my-notify",
                    duration: 300000, //1000 毫秒等于 1 秒。
                    onClose: () => {
                      notificationOffset -= 100; // 每关闭一个通知，调整偏移量
                    },
                  });
                  notificationOffset += 30; // 每显示一个通知，增加偏移量
                }
              }
            } else {
              this.$store.dispatch("settings/changeSwitching", {
                key: "hasNotification",
                value: false,
              });
            }
          }
        );
      }
    },
    // HandleMesssage() {
    //   if (document.getElementsByClassName("el-notification").length === 0) {
    //     this.$notify({
    //       title: "警告",
    //       message: "测定站控制器离线",
    //       type: "warning",
    //       offset: 70,
    //       duration: 0,
    //     });
    //     this.$store.dispatch("settings/changeSwitching", {
    //       key: "hasNotification",
    //       value: false,
    //     });
    //   } else {
    //     this.$notify.closeAll();
    //   }
    // },
    tab(type) {
      localStorage.setItem("locale", type); // 语言选择记录
      this.$i18n.locale = type;
      this.$store.dispatch("app/setLan", type);
    },
    // nameFormatter() {
    //   if (Number(Cookies.get("nowPigFarm"))) {
    //     if (
    //       JSON.stringify(
    //         this.pigFarmsOptions.filter(
    //           (item) => item.id === Number(Cookies.get("nowPigFarm"))
    //         )
    //       ) !== "[]"
    //     ) {
    //       return this.pigFarmsOptions.filter(
    //         (item) => item.id === Number(Cookies.get("nowPigFarm"))
    //       )[0].facname;
    //     }
    //   }
    // },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      this.$confirm(this.$t("menu.logOut"), this.$t("menu.hint"), {
        confirmButtonText: this.$t("common.determine"),
        cancelButtonText: this.$t("common.cancel"),
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          location.href = "/index";
        });
      });
    },
  },
};
</script>
<style lang="scss">
.my-notify .el-notification__content {
  max-height: 600px !important; /* 设置最大高度 */
  overflow: auto !important; /* 超出部分出现滚动条 */
}
.el-notification.my-notify {
  width: 349px;
}
</style>

<style lang="scss" scoped>
.notification-icon {
  position: relative;
  display: inline-block;
}
.notification-icon .badge {
  position: absolute;
  top: 10px;
  right: 7px;
  width: 9px;
  height: 9px;
  background-color: #e6a23c;
  border-radius: 50%;
  border: 1px solid white;
}
.navbar {
  height: 50px;
  overflow: visible;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  min-width: 800px;
  display: flex;
  align-items: center;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;
    flex-shrink: 0;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    min-width: 0;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      white-space: nowrap;
      flex-shrink: 0;

      &.pig-farm {
        font-weight: 400 !important;
        font-size: 14px !important;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
      flex-shrink: 0;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

/* 响应式设计 - 处理不同缩放比例 */
@media screen and (max-width: 1400px) {
  .navbar {
    .right-menu {
      .right-menu-item {
        padding: 0 6px;
        font-size: 16px;

        &.pig-farm {
          font-size: 12px !important;
          max-width: 150px;
        }
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .navbar {
    min-width: 600px;

    .right-menu {
      .right-menu-item {
        padding: 0 4px;
        font-size: 14px;

        &.pig-farm {
          font-size: 11px !important;
          max-width: 120px;
        }
      }

      .avatar-container {
        margin-right: 15px;
      }
    }
  }
}

@media screen and (max-width: 992px) {
  .navbar {
    min-width: 500px;

    .right-menu {
      .right-menu-item {
        padding: 0 3px;
        font-size: 12px;

        &.pig-farm {
          font-size: 10px !important;
          max-width: 100px;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .navbar {
    min-width: 400px;

    .right-menu {
      .right-menu-item {
        padding: 0 2px;
        font-size: 10px;

        &.pig-farm {
          font-size: 9px !important;
          max-width: 80px;
        }
      }

      .avatar-container {
        margin-right: 10px;

        .avatar-wrapper {
          .user-avatar {
            width: 30px;
            height: 30px;
          }
        }
      }
    }
  }
}

/* 防止极端缩放导致组件消失 */
.navbar * {
  box-sizing: border-box;
}

.navbar .right-menu .right-menu-item {
  min-width: 20px;
}

/* 确保在任何缩放比例下都可见 */
@media screen and (min-width: 1px) {
  .navbar {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .navbar .right-menu {
    display: flex !important;
    visibility: visible !important;
  }

  .navbar .right-menu .right-menu-item {
    display: inline-block !important;
    visibility: visible !important;
  }
}
</style>
