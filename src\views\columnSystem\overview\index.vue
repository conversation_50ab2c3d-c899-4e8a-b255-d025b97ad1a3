<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="3" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            :placeholder="$t('columnSystem.pleaseEnterDeptName')"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="21" :xs="24">
        <el-row class="warning">
          <span>{{ $t("columnSystem.nowColumnCohort") }}</span>
          <span>{{ this.pigdataList && this.pigdataList.length }}。</span>
          <span>{{ $t("columnSystem.lessThanFeedNum") }}</span>
          <div style="display: inline-block; width: 70px">
            <el-input v-model="minFeed" size="small" />
          </div>
          <span>{{ $t("columnSystem.pigIs") }}</span>
          <span style="color: red">{{ this.noFeedNumber }}</span>
          <span>{{ $t("boarMeasure.heads") }} </span>

          <span :style="this.noFeedNumber <= 0 && 'display: none'"
            >， {{ $t("boarMeasure.midIs")
            }}<span
              class="link"
              user-select="true"
              style="
                display: inline-block;
                color: #ffffff;
                margin: 4px 4px;
                padding: 2px 2px;
                background-color: red;
                border-radius: 5px;
              "
              v-for="(item, index) in noFeedArr"
              :key="index"
              >{{ item.mid }}</span
            >
          </span>
        </el-row>
        <el-table
          v-loading="loading"
          :data="pigdataList"
          :cell-style="{ padding: '0' }"
        >
          <!-- <el-table-column
           :label="$t('common.serialNumber')"
            align="center"
            prop="indexn"
            width="50"
          /> -->
          <el-table-column
            :label="$t('columnSystem.nindex')"
            align="center"
            prop="nindex"
            width="70"
            sortable
          />
          <el-table-column
            :label="$t('columnSystem.mname')"
            align="center"
            prop="mname"
            width="90"
            sortable
          />
          <el-table-column
            :label="$t('columnSystem.mid')"
            align="center"
            prop="mid"
            sortable
            width="180"
          />
          <el-table-column
            :label="$t('columnSystem.mrfid')"
            align="center"
            prop="mrfid"
            width="180"
            sortable
          >
            <template slot-scope="scope">
              <span
                :style="
                  Number(scope.row.nIngestionSToday) <= 0 &&
                  Number(scope.row.nIngestionSLastday) <= 0
                    ? 'color: red '
                    : Number(scope.row.nIngestionSToday) <= 0
                    ? 'color: #e6a700 '
                    : 'color: black '
                "
              >
                {{ scope.row.mrfid }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('columnSystem.feedTimesToday')"
            align="center"
            sortable
            prop="feedTimes"
            width="130"
          />
          <el-table-column
            :label="$t('columnSystem.feedTimesYest')"
            align="center"
            sortable
            prop="nWeightNum"
            width="130"
          />
          <el-table-column
            :label="$t('columnSystem.nWeightToday')"
            align="center"
            prop="nWeightToday"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('columnSystem.nWeightYest')"
            align="center"
            prop="nWeightYest"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('columnSystem.weightDate')"
            align="center"
            prop="ddate"
            :show-overflow-tooltip="true"
            sortable
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.ddate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('columnSystem.ntype')" align="center" prop="ntype">
            <template slot-scope="scope">
              <el-tag
                effect="dark"
                :type="
                  scope.row.ntype && scope.row.ntype === 1
                    ? 'success'
                    : 'danger'
                "
                disable-transitions
                >{{
                  (scope.row.ntype === 1 || scope.row.ntype === 0) &&
                  typeList &&
                  typeList[scope.row.ntype].dictLabel
                }}</el-tag
              >
            </template>
          </el-table-column> -->
        </el-table>

        <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        /> -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listPigdataOverview } from "@/api/columnSystem/overview";
import { treeselect } from "@/api/columnSystem/overview";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Overview",
  components: { Treeselect },
  data() {
    return {
      minFeed: "1",
      noFeedNumber: "",
      noFeedArr: "",
      //测定状态
      typeList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      pigdataList: null,
      // 弹出层标题
      title: "",
      // 猪场树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },

      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 100,
        indexn: null,
        mid: null,
        mrfid: null,
        dbirthdate: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        ddate: null,
        nweight: null,
        ntype: null,
      },
    };
  },
  //   watch: {
  //   queryParams: {
  //     handler() {
  //       this.getList();
  //     },
  //     deep: true,
  //   },
  // },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
    minFeed() {
      this.filterArr();
    },
  },
  created() {
    this.getDicts("fl_data_type").then((response) => {
      this.typeList = response.data;
    });
    this.getList();
    this.getTreeselect();
  },
  methods: {
    filterArr() {
      let filterArr = this.pigdataList.filter(
        (item) => item.nWeightNum < this.minFeed
      );
      this.noFeedNumber = filterArr.length;
      this.noFeedArr = filterArr.map(function (obj, index) {
        return { mid: obj.mid, nindex: obj.nindex };
      });
    },
    /** 查询猪舍总览 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      // this.queryParams.ntype = 1||"1";
      listPigdataOverview(this.queryParams).then((response) => {
        this.total = response.total;
        this.pigdataList = response.rows;
        let nowPigdataList = this.pigdataList;
        let filterArr = nowPigdataList.filter(
          (item) => item.nWeightNum < this.minFeed
        );
        this.noFeedNumber = filterArr.length;
        this.noFeedArr = filterArr.map(function (obj, index) {
          return { mid: obj.mid, nindex: obj.nindex };
        });

        this.loading = false;
      });
    },
    /** 查询猪舍结构树 */
    getTreeselect() {
      // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      treeselect({ mfactory: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.deptOptions = response.data;
        }
      );
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.pageNum = 1;
      this.queryParams.indexn = data.id;

      this.getList();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style scoped>
.warning {
  padding: 8px 6px;
  background-color: #fff6f7;
  border-radius: 4px;
  /* border-left: 5px solid #fe6c6f; */
  margin-bottom: 20px;
}
</style>