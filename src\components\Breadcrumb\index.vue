<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <span
          v-if="item.redirect === 'noRedirect' || index == levelList.length - 1"
          class="no-redirect"
          >{{ $t(item.meta.title) }}</span
        >
        <a v-else @click.prevent="handleLink(item)">{{
          $t(item.meta.title)
        }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>


<script>
// import { MessageBox } from "element-ui";
export default {
  data() {
    return {
      levelList: null,
    };
  },
  watch: {
    $route(route) {
      // if you go to the redirect page, do not update the breadcrumbs
      if (route.path.startsWith("/redirect/")) {
        return;
      }
      this.getBreadcrumb();
    },
    "$store.state.app.type": {
      handler: function () {
        this.getBreadcrumb();
      },
    },
  },
  created() {
    this.getBreadcrumb();
  },
  methods: {
    getBreadcrumb() {
      // only show routes with meta.title
      let matched = this.$route.matched.filter(
        (item) => item.meta && item.meta.title
      );
      const first = matched[0];
      if (!this.isDashboard(first)) {
        matched = [
          { path: "/index", meta: { title: this.$t("menu.frontPage") } },
        ].concat(matched);
      }

      this.levelList = matched.filter(
        (item) => item.meta && item.meta.title && item.meta.breadcrumb !== false
      );
    },
    isDashboard(route) {
      const name = route && route.name;
      if (!name) {
        return false;
      }
      return name.trim() === "首页";
    },
    handleLink(item) {
      const { redirect, path } = item;
      if (path) {
        this.$router.push(path);
        return;
      }
      // MessageBox.confirm(
      //   this.$t("common.logInAgain"),
      //   this.$t("common.systemHint"),
      //   {
      //     confirmButtonText: this.$t("common.reRegister"),
      //     cancelButtonText: this.$t("common.cancel"),
      //     type: "warning",
      //   }
      // ).then(() => {
      //   this.$router.push(path);
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;
  width: 35vw;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
