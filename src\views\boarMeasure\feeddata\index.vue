<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('boarMeasure.naddress')" prop="naddress">
        <el-input
          v-model="queryParams.naddress"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('boarMeasure.feedTime')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:feeddata:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:feeddata:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:feeddata:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:feeddata:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="feeddataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('common.serialNumber')"
        align="center"
        prop="id"
      />
      <!-- <el-table-column :label="$t('menu.farmName')" align="center" prop="mfactory" />
      <el-table-column :label="$t('boarMeasure.mdorm')"  align="center" prop="mdorm" />
      <el-table-column :label="$t('boarMeasure.nindex')" align="center" prop="nindex" />
      <el-table-column :label="$t('boarMeasure.mname')" align="center" prop="mname" /> -->
      <el-table-column
        :label="$t('boarMeasure.naddress')"
        align="center"
        prop="naddress"
      />
      <el-table-column
        :label="$t('boarMeasure.feedTime')"
        align="center"
        prop="ndate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.ndate, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('boarMeasure.tankInitialWeight')" align="center" prop="nfriweight" />
      <el-table-column :label="$t('boarMeasure.tankEndWeight')" align="center" prop="nsecweight" /> -->
      <el-table-column
        :label="$t('boarMeasure.inFeed')"
        align="center"
        prop="ningestion"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.ningestion !== null && scope.row.ningestion !== undefined
              ? Math.round(Number(scope.row.ningestion))
              : ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.DPC')"
        align="center"
        prop="ndpc"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【投料数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('boarMeasure.naddress')" prop="naddress">
          <el-input
            v-model="form.naddress"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.feedTime')" prop="ndate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ndate"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item :label="$t('boarMeasure.inFeed')" prop="ningestion">
          <el-input
            v-model="form.ningestion"
            :placeholder="$t('common.pleaseInput')"
            @blur="formatToInteger('ningestion')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.DPC')" prop="ndpc">
          <el-input
            v-model="form.ndpc"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listFeeddata,
  getFeeddata,
  delFeeddata,
  addFeeddata,
  updateFeeddata,
  exportFeeddata,
} from "@/api/system/feeddata";
import { listControl } from "@/api/system/control";

export default {
  name: "Feeddata",
  components: {},
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true,
    },
    iconShowBig: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【投料数据】表格数据
      feeddataList: [],
      //栏号数组
      nIndexOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        nfriweight: null,
        nsecweight: null,
        ndpc: null,
        npulse: null,
        ningestion: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
    listControl({
      ntype: 2,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.nIndexOptions = response.rows;
      // this.queryParams.naddress = this.nIndexOptions[0].naddress;
    });
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  methods: {
    /** 查询【投料数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listFeeddata(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        this.feeddataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mfactory: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        nfriweight: null,
        nsecweight: null,
        ndpc: null,
        npulse: null,
        ningestion: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("boarMeasure.addFeedData");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getFeeddata(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("boarMeasure.updateFeedData");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateFeeddata(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addFeeddata(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("boarMeasure.sureCancelFeedData") +
          `"` +
          ids +
          `"` +
          this.$t("boarMeasure.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delFeeddata(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("boarMeasure.sureExportFeedData"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportFeeddata(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    // 格式化为整数
    formatToInteger(field) {
      if (this.form[field] && !isNaN(this.form[field])) {
        this.form[field] = Math.round(Number(this.form[field]));
      }
    },
  },
};
</script>