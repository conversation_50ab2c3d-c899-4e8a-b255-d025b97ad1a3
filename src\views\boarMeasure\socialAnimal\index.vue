<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" class="card-box">
        <!-- <el-card> -->
        <!-- <div slot="header"><span>搜索参数</span></div> -->
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-form-item :label="$t('boarMeasure.nindex')" prop="nindexBegin">
            <span>
              <el-select
                v-model="queryParams.nindexns"
                :placeholder="$t('common.pleaseChoose')"
                size="small"
                multiple
              >
                <el-option
                  v-for="item in nIndexOptions"
                  :key="item.nindex"
                  :label="item.nindex"
                  :value="item.nindex"
                ></el-option>
              </el-select>
            </span>
          </el-form-item>
          <!-- 查询条件选择 -->
          <el-form-item label="查询方式" class="full-line">
            <el-radio-group v-model="queryType" @change="handleQueryTypeChange">
              <el-radio label="date">
                {{ $t("boarMeasure.dateOfDetermination") }}
              </el-radio>
              <el-radio label="age">
                {{ $t("boarMeasure.rlrange") }}
              </el-radio>
              <el-radio label="weight">
                {{ $t("boarMeasure.tizhongfanwei") }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 日龄范围 -->
          <el-form-item
            v-if="queryType === 'age'"
            :label="$t('boarMeasure.rlrange')"
            prop="ageRange"
          >
            <div
              class="el-date-editor el-range-editor el-input__inner el-date-editor--daterange el-range-editor--small"
            >
              <input
                v-model="queryParams.rlFrom"
                :placeholder="$t('boarMeasure.startrl')"
                class="el-range-input"
                @input="handleAgeRangeInput"
                @blur="validateAgeRange"
              />
              <span class="el-range-separator">-</span>
              <input
                v-model="queryParams.rlTo"
                :placeholder="$t('boarMeasure.endrl')"
                class="el-range-input"
                @input="handleAgeRangeInput"
                @blur="validateAgeRange"
              />
            </div>
          </el-form-item>

          <!-- 体重范围 -->
          <el-form-item
            v-if="queryType === 'weight'"
            :label="$t('boarMeasure.tizhongfanwei')"
            prop="weightRange"
          >
            <div
              class="el-date-editor el-range-editor el-input__inner el-date-editor--daterange el-range-editor--small"
            >
              <input
                v-model="queryParams.weightFrom"
                :placeholder="$t('boarMeasure.startWeight')"
                class="el-range-input"
                @input="handleWeightRangeInput"
                @blur="validateWeightRange"
              />
              <span class="el-range-separator">-</span>
              <input
                v-model="queryParams.weightTo"
                :placeholder="$t('boarMeasure.endWeight')"
                class="el-range-input"
                @input="handleWeightRangeInput"
                @blur="validateWeightRange"
              />
            </div>
          </el-form-item>

          <!-- 测定日期 -->
          <el-form-item
            v-if="queryType === 'date'"
            :label="$t('boarMeasure.dateOfDetermination')"
            prop="dateRange"
          >
            <el-date-picker
              v-model="queryParams.dateRange"
              size="small"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="cyan"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >{{ $t("common.search") }}</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
              $t("common.reset")
            }}</el-button>
          </el-form-item>
        </el-form>
        <!-- </el-card> -->
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <el-tabs v-model="activeName">
            <el-tab-pane
              :label="$t('boarMeasure.totalFeedIntake')"
              name="first"
              :lazy="true"
            >
              <el-row :gutter="10" class="mb8">
                <!--                <el-col :span="1.5">
                  <el-button
                    type="warning"
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['boarMeasure:socialAnimal:export1']"
                    >{{ $t("common.export") }}</el-button
                  >
                </el-col>-->
                <right-toolbar
                  :showSearch.sync="showSearch"
                  @queryTable="handleQuery"
                ></right-toolbar>
              </el-row>
              <el-table
                v-loading="loading"
                :data="socialAnimalList"
                :cell-style="{ padding: '0' }"
              >
                <el-table-column
                  :label="$t('boarMeasure.nindex')"
                  align="center"
                  prop="nindex"
                  sortable
                />
                <el-table-column
                  :label="$t('boarMeasure.mid')"
                  align="center"
                  prop="mid"
                  width="180"
                  sortable
                  ><template slot-scope="scope">
                    <ear-tag-link
                      :ear-tag="scope.row.mid"
                      :nindex="scope.row.nindex"
                      route-path="/boarMeasure/breed_measureday"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('boarMeasure.mrfid')"
                  align="center"
                  prop="mrfid"
                  width="180"
                  sortable
                />

                <el-table-column
                  :label="$t('boarMeasure.startDate')"
                  align="center"
                  prop="dstartdate"
                  width="110"
                  sortable
                >
                  <template slot-scope="scope">
                    <span>{{
                      parseTime(scope.row.dstartdate, "{y}-{m}-{d}")
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('boarMeasure.endDate')"
                  align="center"
                  prop="denddate"
                  width="100"
                  sortable
                >
                  <template slot-scope="scope">
                    <span>{{
                      parseTime(scope.row.denddate, "{y}-{m}-{d}")
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('boarMeasure.recordDays')"
                  align="center"
                  prop="measureDays"
                  width="100"
                  sortable
                />
                <el-table-column
                  :label="$t('boarMeasure.allNIngestionG')"
                  align="right"
                  prop="allNIngestionS"
                  sortable
                >
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.allNIngestionS | formatThousands(0)
                    }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                  :label="$t('boarMeasure.allIntakesTimeS')"
                  align="right"
                  prop="nIngestionSumTime"
                >
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.nIngestionSumTime | formatThousands(0)
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('boarMeasure.averageDailyFeedIntakeG')"
                  align="right"
                  prop="averageDayIngestion"
                  sortable
                >
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.averageDayIngestion | formatThousands(0)
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('boarMeasure.intakesNumber')"
                  align="right"
                  prop="ingestionTimes"
                  sortable
                />
              </el-table>
            </el-tab-pane>
            <el-tab-pane
              :label="$t('boarMeasure.totalWeightGain')"
              name="second"
              :lazy="true"
            >
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <el-row :gutter="10" class="mb8">
                  <!-- <el-col :span="1.5">
                   <el-button
                     type="warning"
                     icon="el-icon-download"
                     size="mini"
                     @click="handleExport"
                     v-hasPermi="['boarMeasure:socialAnimal:export2']"
                     >{{ $t("common.export") }}</el-button
                   >
                 </el-col>-->
                  <right-toolbar
                    :showSearch.sync="showSearch"
                    @queryTable="handleQuery"
                  ></right-toolbar>
                </el-row>
                <el-table
                  v-loading="loading"
                  :data="socialAnimalList"
                  :cell-style="{ padding: '0' }"
                >
                  <el-table-column
                    :label="$t('boarMeasure.nindex')"
                    align="center"
                    prop="nindex"
                    sortable
                  />
                  <el-table-column
                    :label="$t('boarMeasure.mid')"
                    align="center"
                    prop="mid"
                    width="180"
                    sortable
                    ><template slot-scope="scope">
                      <ear-tag-link
                        :ear-tag="scope.row.mid"
                        :nindex="scope.row.nindex"
                        route-path="/boarMeasure/breed_measureday"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.mrfid')"
                    align="center"
                    prop="mrfid"
                    width="180"
                    sortable
                  />

                  <el-table-column
                    :label="$t('boarMeasure.startDate')"
                    align="center"
                    prop="dstartdate"
                    width="110"
                    sortable
                  >
                    <template slot-scope="scope">
                      <span>{{
                        parseTime(scope.row.dstartdate, "{y}-{m}-{d}")
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.endDate')"
                    align="center"
                    prop="denddate"
                    width="100"
                    sortable
                  >
                    <template slot-scope="scope">
                      <span>{{
                        parseTime(scope.row.denddate, "{y}-{m}-{d}")
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.startWeight')"
                    align="right"
                    prop="nweight1"
                    sortable
                    ><template slot-scope="scope">
                      <span>{{ scope.row.nweight1 | formatNumber(1) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.endWeight')"
                    align="right"
                    prop="nweight2"
                    sortable
                    ><template slot-scope="scope">
                      <span>{{ scope.row.nweight2 | formatNumber(1) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.weightGain')"
                    align="right"
                    prop="weightGrow"
                    sortable
                    ><template slot-scope="scope">
                      <span>{{ scope.row.weightGrow | formatNumber(1) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.measureDays')"
                    align="center"
                    prop="measureDays"
                    sortable
                  />

                  <el-table-column
                    :label="$t('boarMeasure.averageDailyGrowth')"
                    align="right"
                    prop="averageDayWeight"
                    sortable
                    ><template slot-scope="scope">
                      <span>{{
                        scope.row.averageDayWeight | formatNumber(2)
                      }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
            <el-tab-pane
              :label="$t('boarMeasure.productionPerformance')"
              name="third"
              :lazy="true"
            >
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <el-row :gutter="10" class="mb8">
                  <!--                  <el-col :span="1.5">
                    <el-button
                      type="warning"
                      icon="el-icon-download"
                      size="mini"
                      @click="handleExport"
                      v-hasPermi="['boarMeasure:socialAnimal:export3']"
                      >{{ $t("common.export") }}</el-button
                    >
                  </el-col>-->
                  <right-toolbar
                    :showSearch.sync="showSearch"
                    @queryTable="handleQuery"
                  ></right-toolbar>
                </el-row>
                <el-table
                  v-loading="loading"
                  :data="socialAnimalList"
                  :cell-style="{ padding: '0' }"
                >
                  <el-table-column
                    :label="$t('boarMeasure.nindex')"
                    align="center"
                    prop="nindex"
                    sortable
                  />
                  <el-table-column
                    :label="$t('boarMeasure.mid')"
                    align="center"
                    prop="mid"
                    width="180"
                    sortable
                    ><template slot-scope="scope">
                      <ear-tag-link
                        :ear-tag="scope.row.mid"
                        :nindex="scope.row.nindex"
                        route-path="/boarMeasure/breed_measureday"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.mrfid')"
                    align="center"
                    prop="mrfid"
                    width="180"
                    sortable
                  />
                  <el-table-column
                    :label="$t('boarMeasure.allNIngestionG')"
                    align="right"
                    prop="allNIngestionS"
                    sortable
                  >
                    <template slot-scope="scope">
                      <span>{{
                        scope.row.allNIngestionS | formatThousands(0)
                      }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    :label="$t('boarMeasure.startWeight')"
                    align="right"
                    prop="nweight1"
                    sortable
                  >
                    <template slot-scope="scope">
                      <span>{{ scope.row.nweight1 | formatNumber(1) }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    :label="$t('boarMeasure.weightGain')"
                    align="right"
                    prop="weightGrow"
                    sortable
                  >
                    <template slot-scope="scope">
                      <span>{{ scope.row.weightGrow | formatNumber(1) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.averageDailyFeedIntakeG')"
                    align="right"
                    prop="averageDayIngestion"
                    sortable
                    ><template slot-scope="scope">
                      <span>{{
                        scope.row.averageDayIngestion | formatThousands(0)
                      }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    :label="$t('boarMeasure.averageDailyGrowth')"
                    align="right"
                    prop="averageDayWeight"
                    sortable
                    ><template slot-scope="scope">
                      <span>{{
                        scope.row.averageDayWeight | formatNumber(2)
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.liaoRouBi')"
                    align="right"
                    prop="liaoRouBi"
                    sortable
                    ><template slot-scope="scope">
                      <span>{{ scope.row.liaoRouBi | formatNumber(2) }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  listGroup,
  exportGroup,
  listGroupByWeight,
  listGroupByRl,
  exportgrouplistbyweight,
} from "@/api/system/socialAnimal";

import * as echarts from "echarts";
import { formatDate, formatDay, getDaysDiffBetweenDates } from "@/utils";
import { listControl } from "@/api/system/control";
import { getdaterangebyrl } from "@/api/system/pigdata";

export default {
  name: "SocialAnimal",
  components: {},
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true,
    },
    iconShowBig: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      //电子耳牌数组
      mrfidOptions: [],
      //栏号数组
      nIndexOptions: [],
      activeName: "first",
      // 防抖定时器
      rlRangeTimer: null,
      weightRangeTimer: null,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      //   total: 0,
      // 【测定日报告】表格数据
      socialAnimalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //测定天数
      measureDays: null,
      //记录天数
      recordDays: null,
      //总采食量
      allNIngestionS: null,
      //总增重
      weightGrow: null,

      pigdata: null,
      //采食echart
      category22: null,
      //体重echart
      category23: null,
      //采食x轴数据
      xDataFood: [],
      //采食y轴数据
      yDataFood: [],
      //体重x轴数据
      xWeight: [],
      //体重y轴数据
      yWeight: [],
      //foodEchart
      foodEchartList: [],
      //weightEchart
      weightEchartList: [],
      measureList: [],
      totalMeasure: 0,
      openMeasure: false,
      titleMeasure: "",

      // 表单参数
      form: {},

      // 查询类型：date-日期，age-日龄，weight-体重
      queryType: "date",

      // 防抖定时器
      rlRangeTimer: null,
      weightRangeTimer: null,

      queryParams: {
        nindexns: [],
        dateRange: [],
        weightFrom: null,
        weightTo: null,
        rlFrom: null,
        rlTo: null,
      },
    };
  },
  watch: {
    "queryParams.nindexns": {
      handler() {
        this.handleQuery();
      },
      deep: true,
    },
    // "queryParams.dateRange": {
    //   handler() {
    //     this.handleQuery();
    //   },
    //   deep: true,
    // },
  },
  created() {
    // 恢复查询条件缓存
    this.restoreQueryParams();

    listControl({
      ntype: 2,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.nIndexOptions = response.rows;
      this.handleQuery();
    });
  },
  beforeDestroy() {
    // 保存查询条件到缓存
    this.saveQueryParams();
  },
  methods: {
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("boarMeasure.sureExportBreedSocialAnimal"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportGroup(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },

    /** 查询【测定日报告】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;

      listGroup(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      )
        .then((response) => {
          this.socialAnimalList = response.data.map((item) => ({
            ...item,
            averageDayIngestion:
              item.averageDayIngestion === "Infinity"
                ? Infinity
                : Number(item.averageDayIngestion) || 0,
            liaoRouBi:
              item.liaoRouBi === "Infinity"
                ? Infinity
                : Number(item.liaoRouBi) || 0,
          }));
          // this.socialAnimalList = response.data;
          // this.total = response.total;
        })
        .catch((error) => {
          console.error("获取数据失败:", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    getListByWeightRange() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;

      listGroupByWeight(this.queryParams)
        .then((response) => {
          this.socialAnimalList = response.data.map((item) => ({
            ...item,
            averageDayIngestion:
              item.averageDayIngestion === "Infinity"
                ? Infinity
                : Number(item.averageDayIngestion) || 0,
            liaoRouBi:
              item.liaoRouBi === "Infinity"
                ? Infinity
                : Number(item.liaoRouBi) || 0,
          }));
          // this.total = response.total;
        })
        .catch((error) => {
          console.error("获取体重范围数据失败:", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    getDateRangeByRl() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;

      listGroupByRl(this.queryParams)
        .then((response) => {
          this.socialAnimalList = response.data.map((item) => ({
            ...item,
            averageDayIngestion:
              item.averageDayIngestion === "Infinity"
                ? Infinity
                : Number(item.averageDayIngestion) || 0,
            liaoRouBi:
              item.liaoRouBi === "Infinity"
                ? Infinity
                : Number(item.liaoRouBi) || 0,
          }));
          // this.total = response.total;
        })
        .catch((error) => {
          console.error("获取料肉比范围数据失败:", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      // 根据查询类型进行验证和查询
      if (this.queryType === "date") {
        // 日期查询验证
        // if (
        //   !this.queryParams.dateRange ||
        //   this.queryParams.dateRange.length !== 2
        // ) {
        //   this.$message.warning("请选择查询日期范围");
        //   return;
        // }
        this.getList(); // 按日期查询
      } else if (this.queryType === "age") {
        // 日龄查询验证
        if (!this.validateAgeRange()) {
          return;
        }
        this.getDateRangeByRl(); // 按日龄查询
      } else if (this.queryType === "weight") {
        // 体重查询验证
        if (!this.validateWeightRange()) {
          return;
        }
        this.getListByWeightRange(); // 按体重查询
      }
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryType = "date";
      this.queryParams = {
        nindexns: [],
        dateRange: [],
        weightFrom: null,
        weightTo: null,
        rlFrom: null,
        rlTo: null,
      };
      this.resetForm("form");
      // 保存重置后的状态到缓存
      this.saveQueryParams();
    },

    /** 处理日龄范围输入 */
    handleAgeRangeInput() {
      // 清除之前的定时器
      if (this.rlRangeTimer) {
        clearTimeout(this.rlRangeTimer);
      }
      // 设置防抖，800ms后执行查询
      this.rlRangeTimer = setTimeout(() => {
        if (this.validateAgeRange(false)) {
          this.getDateRangeByRl();
        }
      }, 800);
    },

    /** 处理体重范围输入 */
    handleWeightRangeInput() {
      // 清除之前的定时器
      if (this.weightRangeTimer) {
        clearTimeout(this.weightRangeTimer);
      }
      // 设置防抖，800ms后执行查询
      this.weightRangeTimer = setTimeout(() => {
        if (this.validateWeightRange(false)) {
          this.getListByWeightRange();
        }
      }, 800);
    },

    /** 验证日龄范围 */
    validateAgeRange(showMessage = true) {
      const { rlFrom, rlTo } = this.queryParams;

      // 检查是否为空
      if (!rlFrom || !rlTo) {
        if (showMessage && (rlFrom || rlTo)) {
          this.$message.warning("请输入完整的日龄范围");
        }
        return false;
      }

      // 转换为数字
      const fromNum = Number(rlFrom);
      const toNum = Number(rlTo);

      // 检查是否为有效数字
      if (isNaN(fromNum) || isNaN(toNum)) {
        if (showMessage) {
          this.$message.error("日龄必须为有效数字");
        }
        return false;
      }

      // 检查是否为负数
      if (fromNum < 0 || toNum < 0) {
        if (showMessage) {
          this.$message.error("日龄不能为负数");
        }
        return false;
      }

      // 检查大小关系
      if (fromNum >= toNum) {
        if (showMessage) {
          this.$message.error("结束日龄必须大于开始日龄");
        }
        return false;
      }

      return true;
    },

    /** 验证体重范围 */
    validateWeightRange(showMessage = true) {
      const { weightFrom, weightTo } = this.queryParams;

      // 检查是否为空
      if (!weightFrom || !weightTo) {
        if (showMessage && (weightFrom || weightTo)) {
          this.$message.warning("请输入完整的体重范围");
        }
        // if (showMessage) {
        //   this.$message.warning("请输入完整的体重范围");
        // }
        return false;
      }

      // 转换为数字
      const fromNum = Number(weightFrom);
      const toNum = Number(weightTo);

      // 检查是否为有效数字
      if (isNaN(fromNum) || isNaN(toNum)) {
        if (showMessage) {
          this.$message.error("体重必须为有效数字");
        }
        return false;
      }

      // 检查是否为负数
      if (fromNum < 0 || toNum < 0) {
        if (showMessage) {
          this.$message.error("体重不能为负数");
        }
        return false;
      }

      // 检查大小关系
      if (fromNum >= toNum) {
        if (showMessage) {
          this.$message.error("结束体重必须大于开始体重");
        }
        return false;
      }

      return true;
    },

    // 查询类型变化处理
    handleQueryTypeChange(newType) {
      // 清空其他查询条件，但不触发查询
      if (newType === "date") {
        this.queryParams.rlFrom = null;
        this.queryParams.rlTo = null;
        this.queryParams.weightFrom = null;
        this.queryParams.weightTo = null;
      } else if (newType === "age") {
        this.queryParams.dateRange = [];
        this.queryParams.weightFrom = null;
        this.queryParams.weightTo = null;
      } else if (newType === "weight") {
        this.queryParams.dateRange = [];
        this.queryParams.rlFrom = null;
        this.queryParams.rlTo = null;
      }

      // 保存查询条件到缓存（不调用 handleQuery）
      this.saveQueryParams();
    },

    // 日期范围变化处理
    handleDateRangeChange() {
      if (
        this.queryParams.dateRange &&
        this.queryParams.dateRange.length === 2
      ) {
        this.handleQuery();
      }
    },

    // 保存查询条件到缓存
    saveQueryParams() {
      const cacheKey = "socialAnimal_queryParams";
      const cacheData = {
        queryType: this.queryType,
        queryParams: { ...this.queryParams },
      };
      sessionStorage.setItem(cacheKey, JSON.stringify(cacheData));
    },

    // 恢复查询条件缓存
    restoreQueryParams() {
      const cacheKey = "socialAnimal_queryParams";
      const cacheData = sessionStorage.getItem(cacheKey);

      if (cacheData) {
        try {
          const parsedData = JSON.parse(cacheData);
          this.queryType = parsedData.queryType || "date";

          // 恢复查询参数
          if (parsedData.queryParams) {
            Object.assign(this.queryParams, parsedData.queryParams);
          }
        } catch (error) {
          console.error("恢复查询条件失败:", error);
        }
      }
    },
  },
};
</script>

<style scoped>
/* 耳缺号链接样式 */
.ear-tag-link {
  color: #409eff;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.ear-tag-link:hover {
  background-color: #ecf5ff;
  color: #337ecc;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.ear-tag-link:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.3);
}

.ear-tag-link i {
  font-size: 12px;
  opacity: 0.8;
}

/* 查询方式单选框样式优化 */
.el-radio {
  margin-right: 20px;
}

.el-radio__label {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.el-radio__label i {
  font-size: 14px;
  color: #909399;
}

.el-radio.is-checked .el-radio__label i {
  color: #409eff;
}

/* 查询条件区域样式 */
.el-form--inline .el-form-item {
  margin-bottom: 15px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 让特定表单项占满一整行 */
.full-line {
  width: 100%;
}
</style>
