<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" class="card-box">
        <el-card>
          <el-form ref="form" :model="form" label-width="80px">
            <!-- <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
            <el-input v-model="form.mdorm" placeholder="请输入延续时间" />
          </el-form-item> -->
            <el-descriptions
              :title="$t('columnSystem.targetWeight')"
              :column="3"
              class="margin-top"
            >
              <el-descriptions-item :label="$t('columnSystem.targetWeightLow')"
                ><el-input-number
                  size="small"
                  v-model="form.targetWeightLow"
                  controls-position="right"
                ></el-input-number>
              </el-descriptions-item>

              <el-descriptions-item :label="$t('columnSystem.targetWeightHigh')"
                ><el-input-number
                  size="small"
                  v-model="form.targetWeightHigh"
                  controls-position="right"
                ></el-input-number
              ></el-descriptions-item>
            </el-descriptions>
            <el-descriptions
              :title="$t('columnSystem.filteringScope')"
              :column="2"
              class="margin-top"
            >
              <el-descriptions-item :label="$t('columnSystem.listingDate')">
                <el-date-picker
                  v-model="form.dateRange"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="-"
                  :start-placeholder="$t('common.startDate')"
                  :end-placeholder="$t('common.endDate')"
                  size="small"
                >
                </el-date-picker>
              </el-descriptions-item>
              <el-descriptions-item :label="$t('columnSystem.intervalDays')"
                ><el-input-number
                  size="small"
                  v-model="form.intervalDays"
                  controls-position="right"
                ></el-input-number>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions
              :title="$t('columnSystem.growthCalculations')"
              :column="3"
              class="margin-top"
            >
              <el-descriptions-item :label="$t('columnSystem.adgRelyDays')"
                ><el-input-number
                  size="small"
                  v-model="form.adgRelyDays"
                  controls-position="right"
                ></el-input-number>
              </el-descriptions-item>
              <el-descriptions-item label="ADG"
                ><el-input-number
                  size="small"
                  v-model="form.adg"
                  :precision="1"
                  :step="0.1"
                  :min="1"
                  :max="10"
                  controls-position="right"
                ></el-input-number>
              </el-descriptions-item>
              <el-descriptions-item :label="$t('columnSystem.isAdgStable')">
                <el-checkbox
                  v-model="form.isAdgStable"
                  true-label="1"
                  false-label="0"
                ></el-checkbox>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions
              :title="$t('columnSystem.digital')"
              :column="3"
              :colon="false"
              class="margin-top"
            >
              <el-descriptions-item label="">
                <el-radio v-model="form.dataType" label="1">
                  {{ $t("columnSystem.npignums") }}</el-radio
                >
                <el-radio v-model="form.dataType" label="2">
                  {{ $t("columnSystem.percentage") }}</el-radio
                >
              </el-descriptions-item>
            </el-descriptions>
          </el-form>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" size="mini" @click="getList">{{
                $t("columnSystem.sure")
              }}</el-button>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="24" class="card-box">
        <el-card v-if="form.dataType == '1'">
          <el-table v-loading="loading" :data="predictionList">
            <el-table-column
              :label="$t('columnSystem.targerDay')"
              align="center"
              prop="targerDay"
            >
              <!-- <template slot-scope="scope">
                <span>{{
                  parseTime(
                    this.transformTimestamp(scope.row.targerDay),
                    "{y}-{m}-{d}"
                  )
                }}</span>
              </template> -->
            </el-table-column>
            <el-table-column
              :label="$t('columnSystem.lowWeightNum')"
              align="center"
              prop="lowWeightNum"
            />
            <el-table-column
              :label="$t('columnSystem.targetNum')"
              align="center"
              prop="targetNum"
            />
            <el-table-column
              :label="$t('columnSystem.highWeightNum')"
              align="center"
              prop="highWeightNum"
            />
          </el-table>
        </el-card>
        <el-card v-else-if="form.dataType == '2'">
          <el-table v-loading="loading" :data="predictionList">
            <el-table-column
              :label="$t('columnSystem.targerDay')"
              align="center"
              prop="targerDay"
            >
              <!-- <template slot-scope="scope">
                <span>{{
                  parseTime(
                    this.transformTimestamp(scope.row.targerDay),
                    "{y}-{m}-{d}"
                  )
                }}</span>
              </template> -->
            </el-table-column>
            <el-table-column
              :label="$t('columnSystem.lowWeightPct')"
              align="center"
              prop="lowWeightPct"
            />
            <el-table-column
              :label="$t('columnSystem.targetWeightPct')"
              align="center"
              prop="targetWeightPct"
            />
            <el-table-column
              :label="$t('columnSystem.highWeightPct')"
              align="center"
              prop="highWeightPct"
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { listForecast } from "@/api/columnSystem/prediction";
export default {
  data() {
    return {
      form: {
        dataType: "1",
      },
      loading: false,
      predictionList: [],
    };
  },
  // created() {
  //   this.getList();
  // },
  methods: {
    // 定义函数，将时间格式转换为 "年-月-日" 形式
    formatTime(dateString) {
      const date = new Date(dateString); // 将时间字符串转换为日期对象
      const year = date.getFullYear(); // 获取年份
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 获取月份，并补零
      const day = String(date.getDate()).padStart(2, "0"); // 获取日期，并补零
      return `${year}-${month}-${day}`; // 返回格式化后的时间字符串
    },
    // 定义处理时间字段的函数，返回处理后的 JSON 数组
    processTimeField(jsonData, timeField) {
      const processedData = [];
      for (const item of jsonData) {
        const { [timeField]: timestamp, ...rest } = item; // 使用解构赋值提取时间字段并剩余属性
        processedData.push({
          ...rest,
          [timeField]: this.formatTime(timestamp),
        }); // 构建新对象，将时间字段转换为 Date 对象
      }
      return processedData;
    },
    /** 查询【同时开启】列表 */
    getList() {
      this.loading = true;
      this.form.mfactory = this.$store.state.settings.nowPigFarm;
      listForecast(this.addDateRangeRe(this.form, this.form.dateRange)).then(
        (response) => {
          this.predictionList = this.processTimeField(
            response.data,
            "targerDay"
          );
          this.loading = false;
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-descriptions-item__container .el-descriptions-item__label,
.el-descriptions-item__container .el-descriptions-item__content {
  align-items: center;
}
</style>