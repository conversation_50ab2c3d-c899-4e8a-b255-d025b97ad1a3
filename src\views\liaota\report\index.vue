<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('liaota.towerName')" prop="slavename">
        <el-select
          v-model="queryParams.slavename"
          :placeholder="$t('liaota.enterTowerName')"
          size="small"
        >
          <el-option
            v-for="item in slaveOptions"
            :key="item.id"
            :label="item.slavename"
            :value="item.slavename"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('liaota.selectDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('liaota.startDate')"
          :end-placeholder="$t('liaota.endDate')"
          @change="handleDateClick"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('liaota.period')" prop="dayLine">
        <el-select
          v-model="queryParams.dayLine"
          :placeholder="$t('liaota.choosePeriod')"
          @change="handleDateClick"
        >
          <el-option
            v-for="dict in dateLineList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <span v-if="this.activeName == 'second'">
        <el-form-item :label="$t('liaota.floor')" prop="homename">
          <!-- <el-input
            v-model="queryParams.homename"
            :placeholder="$t('liaota.enterFloor')"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          /> -->
          <el-select
            v-model="queryParams.homename"
            :placeholder="$t('liaota.enterFloor')"
            clearable
            size="small"
          >
            <el-option
              v-for="item in floorList"
              :key="item.id"
              :label="item.homename"
              :value="item.homename"
              @keyup.enter.native="handleQuery"
            ></el-option>
          </el-select>
        </el-form-item>
      </span>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
      </el-form-item>
    </el-form>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="$t('liaota.weightLine')" name="first" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category1" id="category1" style="height: 500px" />
        </div>
      </el-tab-pane>
      <template v-if="checkPermi(['system:inout:list'])">
        <el-tab-pane :label="$t('liaota.inOutLine')" name="second" :lazy="true">
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="category2" id="category2" style="height: 500px" />
          </div>
        </el-tab-pane>
      </template>
      <!-- <el-tab-pane label="加放料数据" name="third" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div ref="category1" id="category1" style="height: 500px" />
        </div>
      </el-tab-pane> -->
    </el-tabs>
    <!-- <p v-for="(message, idx) in messages" :key="idx">{{ message }}</p> -->
  </div>
</template>

<script>
import { listLiaotaHistory } from "@/api/liaota/history";
import { listSlave } from "@/api/liaota/slave";
import { listInout } from "@/api/liaota/inout";
import { listHome } from "@/api/liaota/floor";
import * as echarts from "echarts";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
require("@/utils/walden"); // echarts theme

let sseClient;

export default {
  name: "liaotaReport",
  components: {},
  data() {
    return {
      //楼层
      floorList: [],
      messages: [],
      activeName: "first",
      // sse: {
      //   cleanup: true,
      // },
      dateLineList: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      //饲料消耗
      category1: null,
      //料塔汇总数据Echart
      towerEchartList: [],
      //料塔加放料数据Echart
      towerInOutList: [],
      //加料x轴数据
      xDataTowerIn: [],
      //加料y轴数据
      yDataTowerIn: [],
      //放料x轴数据
      xDataTowerOut: [],
      //放料y轴数据
      yDataTowerOut: [],
      //料塔x轴数据
      xDataTower: [],
      //料塔y轴数据
      yDataTower: [],
      //料塔值集
      slaveOptions: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 弹出层标题
      title: "",
      // 查询参数
      queryParams: {
        slavename: null,
        dayLine: null,
        pageNum: 1,
        pageSize: null,
        dateRange: [],
        homename: null,
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  created() {
    this.getDicts("hk_date_duan").then((response) => {
      this.dateLineList = response.data;
      this.queryParams.dayLine =
        this.dateLineList && this.dateLineList[0].dictValue;
    });
    // this.getList();
  },
  mounted() {
    listSlave({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.slaveOptions = response.rows;
        this.queryParams.slavename =
          this.slaveOptions && this.slaveOptions[0].slavename;
      }
    );
    listHome({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.floorList = response.rows;
      }
    );
    this.getList();
    window.onresize = () => {
      this.category1.resize(); //重新初始化echarts
    };
  },
  beforeDestroy() {},
  methods: {
    checkPermi,
    handleClick(tab, event) {
      if (tab.name == "first") this.getList();
      if (tab.name == "second") this.getList();
      // if (tab.name == "third") this.getList();
    },

    /** 查询【请填写功能名称】列表 */
    getList() {
      if (this.activeName === "first") {
        // this.loading = true;
        if (this.queryParams.slavename) {
          this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
          listLiaotaHistory(
            this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
          ).then((response) => {
            if (response && response.rows) {
              this.towerEchartList = response.rows;
              this.xDataTower = [];
              this.yDataTower = [];
              this.towerEchartList.forEach((element) => {
                this.xDataTower.push(element.uptime);
                this.yDataTower.push(element.weight);
              });
            } else {
              this.towerEchartList = [];
              this.xDataTower = [];
              this.yDataTower = [];
            }
            // this.loading = false;
            this.getTowerConsumeData();
          });
        }
      } else if (this.activeName === "second") {
        // this.loading = true;
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        listInout(
          this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
        ).then((response) => {
          if (response && response.rows) {
            this.towerInOutList = response.rows;
            // this.xDataTowerIn = [];
            this.yDataTowerIn = [];
            // this.xDataTowerOut = [];
            this.yDataTowerOut = [];
            this.towerInOutList.forEach((element) => {
              if (element.inorout == "1") {
                // this.xDataTowerIn.push(element.uptime);
                this.yDataTowerIn.push([element.uptime, element.weight]);
              } else if (element.inorout == "0") {
                // this.xDataTowerOut.push(element.uptime);
                this.yDataTowerOut.push([element.uptime, element.weight]);
              }
            });
          } else {
            this.towerInOutList = [];
            // this.xDataTowerIn = [];
            this.yDataTowerIn = [];
            // this.xDataTowerOut = [];
            this.yDataTowerOut = [];
          }
          // this.loading = false;
          this.getTowerInOutData();
        });
      }
    },

    /** 搜索按钮操作 */
    handleQuery(data) {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleDateClick(data) {
      // Object.prototype.toString.call(a) === '[object Array]';//true
      if (Object.prototype.toString.call(data) === "[object Array]") {
        this.queryParams.dayLine = null;
      } else {
        this.queryParams.dateRange = null;
      }
      this.getList();
    },

    getTowerConsumeData() {
      this.category1 = echarts.init(this.$refs.category1, "walden");
      this.category1.setOption({
        title: {
          text: "",
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [`${this.$t("liaota.towerUse")}`],
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.xDataTower.reverse(),
          // inverse: true,
          nameLocation: "end", //坐标轴名称显示位置。
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            name: this.$t("liaota.towerUse"),
            type: "line",
            stack: this.$t("liaota.all"),
            data: this.yDataTower.reverse(),
            inverse: true,
            markPoint: {
              data: [
                {
                  name: this.$t("liaota.max"),
                  type: "max",
                },
                {
                  name: this.$t("liaota.min"),
                  type: "min",
                },
              ],
            },
          },
        ],
      });
    },

    getTowerInOutData() {
      this.category2 = echarts.init(this.$refs.category2, "walden");
      this.category2.setOption({
        title: {
          text: "",
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [`${this.$t("liaota.inFeed")}`, `${this.$t("liaota.outFeed")}`],
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          // type: "category",
          type: "time",
          boundaryGap: false,
          // data: this.xDataTowerIn.reverse(),
          // inverse: true,
          nameLocation: "end", //坐标轴名称显示位置。
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            name: this.$t("liaota.inFeed"),
            type: "line",
            // stack: this.$t("liaota.all"),
            data: this.yDataTowerIn,
            // inverse: true,
          },
          {
            symbol: "none", //取消折点圆圈
            name: this.$t("liaota.outFeed"),
            type: "line",
            // stack: this.$t("liaota.all"),
            data: this.yDataTowerOut,
            // inverse: true,
          },
        ],
      });
    },
  },
};
</script>
