<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item label="控制器id" prop="envslaveid">
        <el-input
          v-model="queryParams.envslaveid"
          placeholder="请输入控制器id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网关id" prop="envswitchid">
        <el-input
          v-model="queryParams.envswitchid"
          placeholder="请输入网关id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item :label="$t('huanKong.parameterName')" prop="paraname">
        <el-input
          v-model="queryParams.paraname"
          :placeholder="$t('huanKong.enterParameterName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('huanKong.point')" prop="sitecode">
        <el-input
          v-model="queryParams.sitecode"
          :placeholder="$t('huanKong.enterPoint')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="参数值" prop="paravalue">
        <el-input
          v-model="queryParams.paravalue"
          placeholder="请输入参数值"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item :label="$t('huanKong.parameterType')" prop="type">
        <el-select
          v-model="queryParams.type"
          :placeholder="$t('huanKong.enterParameterType')"
          size="small"
        >
          <el-option
            v-for="dict in typeValueList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['huanKong:controlDictionary:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['huanKong:controlDictionary:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['huanKong:controlDictionary:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['huanKong:controlDictionary:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="paramList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('common.serialNumber')"
        align="center"
        prop="id"
      />
      <!-- <el-table-column label="控制器id" align="center" prop="envslaveid" />
      <el-table-column label="网关id" align="center" prop="envswitchid" /> -->
      <el-table-column
        :label="$t('huanKong.parameterName')"
        align="center"
        prop="paraname"
      />
      <el-table-column
        :label="$t('huanKong.parameterNameENG')"
        align="center"
        prop="engParaname"
        width="200"
      />
      <el-table-column
        :label="$t('huanKong.parameterNameRUS')"
        align="center"
        prop="rusParaname"
        width="200"
      />
      <el-table-column
        :label="$t('huanKong.point')"
        align="center"
        prop="sitecode"
      />
      <!-- <el-table-column label="参数值" align="center" prop="paravalue" /> -->
      <el-table-column
        :label="$t('huanKong.parameterType')"
        align="center"
        prop="type"
        :formatter="valueTypeFormat"
      />
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['huanKong:controlDictionary:edit']"
            >{{ $t("common.update") }}</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['huanKong:controlDictionary:remove']"
            >{{ $t("common.delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【控制器参数字典】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="控制器id" prop="envslaveid">
          <el-input v-model="form.envslaveid" placeholder="请输入控制器id" />
        </el-form-item>
        <el-form-item label="网关id" prop="envswitchid">
          <el-input v-model="form.envswitchid" placeholder="请输入网关id" />
        </el-form-item> -->
        <el-form-item :label="$t('huanKong.parameterName')" prop="paraname">
          <el-input
            v-model="form.paraname"
            :placeholder="$t('huanKong.enterParameterName')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('huanKong.parameterNameENG')"
          prop="engParaname"
        >
          <el-input
            v-model="form.engParaname"
            :placeholder="$t('huanKong.enterParameterName')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('huanKong.parameterNameRUS')"
          prop="rusParaname"
        >
          <el-input
            v-model="form.rusParaname"
            :placeholder="$t('huanKong.enterParameterName')"
          />
        </el-form-item>
        <el-form-item :label="$t('huanKong.point')" prop="sitecode">
          <el-input
            v-model="form.sitecode"
            :placeholder="$t('huanKong.enterPoint')"
          />
        </el-form-item>
        <!-- <el-form-item label="参数值" prop="paravalue">
          <el-input v-model="form.paravalue" placeholder="请输入参数值" />
        </el-form-item> -->
        <el-form-item :label="$t('huanKong.parameterType')" prop="type">
          <el-select
            v-model="form.type"
            :placeholder="$t('huanKong.enterParameterType')"
            size="small"
          >
            <el-option
              v-for="dict in typeValueList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listParam,
  getParam,
  delParam,
  addParam,
  updateParam,
  exportParam,
} from "@/api/huanKong/param";

export default {
  name: "Param",
  components: {},
  data() {
    return {
      typeValueList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【控制器参数字典】表格数据
      paramList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // envslaveid: null,
        // envswitchid: null,
        paraname: null,
        sitecode: null,
        paravalue: null,
        type: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // envslaveid: [
        //   {
        //     required: true,
        //     message: "控制器id不能为空",
        //     trigger: "blur",
        //   },
        // ],
        // envswitchid: [
        //   {
        //     required: true,
        //     message: "网关id不能为空",
        //     trigger: "blur",
        //   },
        // ],
        paraname: [
          {
            required: true,
            message: this.$t("huanKong.parameterNameNotNull"),
            trigger: "blur",
          },
        ],
        sitecode: [
          {
            required: true,
            message: this.$t("huanKong.pointNotNull"),
            trigger: "blur",
          },
        ],
        // paravalue: [
        //   {
        //     required: true,
        //     message: "参数值不能为空",
        //     trigger: "blur",
        //   },
        // ],
        type: [
          {
            required: true,
            message: this.$t("huanKong.parameterTypeNotNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getDicts("hk_value_type").then((response) => {
      this.typeValueList = response.data;
    });
  },
  methods: {
    // 字典状态字典翻译
    valueTypeFormat(row, column) {
      return this.selectDictLabel(this.typeValueList, row.type);
    },
    /** 查询【控制器参数字典】列表 */
    getList() {
      this.loading = true;
      // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listParam(this.queryParams).then((response) => {
        this.paramList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.loading = false;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        // envslaveid: null,
        // envswitchid: null,
        paraname: null,
        sitecode: null,
        paravalue: null,
        type: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("huanKong.addParameterDictionary");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getParam(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("huanKong.modifyParameterDictionary");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateParam(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            //this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addParam(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("huanKong.sureDeleteParameterDictionary") +
          `"` +
          ids +
          `"` +
          this.$t("huanKong.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delParam(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("huanKong.sureExportParameterDictionary"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportParam(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>