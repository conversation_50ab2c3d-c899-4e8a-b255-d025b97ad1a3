<template>
  <div class="login">
    <span class="register_hover" @click="handleRegister">CD KEY</span>
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      v-show="openLoginForm"
      :show-message="false"
    >
      <!-- <img v-if="logo" :src="logo" class="login-code-img" /> -->
      <!-- <h3 class="title">{{ $t("menu.smartPigRaisingCloudPlatform") }}</h3> -->
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          :placeholder="$t('menu.account')"
        >
          <svg-icon
            slot="prefix"
            icon-class="user"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          :placeholder="$t('menu.password')"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            icon-class="password"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          :placeholder="$t('menu.verificationCode')"
          style="width: 69%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            icon-class="validCode"
            class="el-input__icon input-icon"
          />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <el-checkbox
        v-model="loginForm.rememberMe"
        style="margin: 0px 0px 20px 0px"
        >{{ $t("menu.rememberPassword") }}</el-checkbox
      >

      <el-form-item style="width: 100%">
        <el-button
          :loading="loading"
          size="medium"
          type="login"
          style="width: 100%"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">{{ $t("menu.logIn") }}</span>
          <span v-else>{{ $t("menu.loggingIn") }}</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  搴曢儴  -->
    <div class="el-login-footer">
      <span></span>
    </div>

    <el-dialog
      :title="$t('menu.register')"
      :visible.sync="open"
      width="400px"
      @close="cancel"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="68px">
        <el-row>
          <el-col>
            <el-form-item
              :label="$t('menu.registrationCode')"
              prop="configValue"
            >
              <el-input
                v-model="form.configValue"
                :placeholder="$t('menu.enterRegistrationCode')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm"
          :loading="submitLoading"
          >{{ $t("common.determine") }}</el-button
        >
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCodeImg, registerCode } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import logoImg from "@/assets/logo/logo.png";

export default {
  name: "Login",
  data() {
    return {
      // 鏄�鍚︽樉绀哄脊鍑哄眰
      openLoginForm: true,
      open: false,
      form: {},
      // 琛ㄥ崟鏍￠獙
      rules: {
        configValue: [
          {
            required: true,
            message: this.$t("menu.registrationCodeNotNull"),
            trigger: "blur",
          },
        ],
      },
      submitLoading: false,
      codeUrl: "",
      cookiePassword: "",
      logo: logoImg,

      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: "blur",
            message: this.$t("menu.usernameNotNull"),
          },
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            message: this.$t("menu.passwordNotNull"),
          },
        ],
        code: [
          {
            required: true,
            trigger: "change",
            message: this.$t("menu.verificationCodeNotNull"),
          },
        ],
      },
      loading: false,
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    submitForm() {
      this.submitLoading = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          registerCode(this.form).then((res) => {
            if (res) {
              this.msgSuccess(res.msg);
              this.open = false;
              this.submitLoading = false;
              this.openLoginForm = true;
            } else {
              this.submitLoading = false;
            }
          });
        }
      });
      this.submitLoading = false;
    },
    handleRegister() {
      this.open = true;
      this.openLoginForm = false;
    },
    // 鍙栨秷鎸夐挳
    cancel() {
      this.open = false;
      this.submitLoading = false;
      this.form = {
        code: null,
      };
      this.openLoginForm = true;
    },
    getCode() {
      getCodeImg().then((res) => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      // const nowPigFarm = Cookies.get("nowPigFarm");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});
              // console.log("this.$store.state.settings.nowPigFarm",this.$store.state.settings.nowPigFarm)
              // if (
              //   Cookies.get("nowPigFarm") === undefined ||
              //   Cookies.get("nowPigFarm") === "null"
              // ) {
              this.$store.dispatch("settings/changeSwitching", {
                key: "showSwitching",
                value: true,
              });
              // }
            })
            .catch(() => {
              this.loading = false;
              this.getCode();
            });
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login .el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
}
.login .el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
}

//sass
// .login {
//   /deep/ .el-dialog {
//     display: flex;
//     flex-direction: column;
//     margin: 0 !important;
//     position: absolute;
//     top: 50%;
//     left: 50%;
//     transform: translate(-50%, -50%);
//     max-height: calc(100% - 30px);
//     max-width: calc(100% - 30px);
//   }
//   /deep/ .el-dialog .el-dialog__body {
//     flex: 1;
//     overflow: auto;
//   }
// }

.register_hover {
  background-color: #01833f;
  color: #fff;
  padding: 7px;
  position: fixed;
  top: 30px;
  right: 40px;
  border-radius: 10px;
  -moz-border-radius: 10px; /* 鑰佺殑 Firefox */
  // float: right;
  // color: #6c6c6c;
}
.register_hover:hover {
  background-color: #26ab5ad6;
  // text-decoration: underline;
}
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
  background: url("../assets/images/kenuoback.jpg") center center no-repeat;
  background-size: 100% auto;
}
.title {
  margin: 10px auto 20px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #fbfcfe;
  width: 380px;
  padding: 20px 20px 12px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  // width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}

.el-input__inner {
  border: 1px solid #dcdfe61f;
}

// .el-form-item {
//   margin-bottom: 15px;
// }
</style>
