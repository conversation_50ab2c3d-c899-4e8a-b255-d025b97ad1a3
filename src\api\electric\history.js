import request from '@/utils/request'
// 查询【请填写功能名称】列表 
// /system/electrichistory
export function listElectricHistory(query) {
    return request({
      url: '/system/electrichistory/list',
      method: 'get',
      params: query
    })
  }
  
  // 查询【请填写功能名称】详细
  export function getElectricHistory(id) {
    return request({
      url: '/system/electrichistory/' + id,
      method: 'get'
    })
  }
  
  // 新增【请填写功能名称】
  export function addElectricHistory(data) {
    return request({
      url: '/system/electrichistory',
      method: 'post',
      data: data
    })
  }
  
  // 修改【请填写功能名称】
  export function updateElectricHistory(data) {
    return request({
      url: '/system/electrichistory',
      method: 'put',
      data: data
    })
  }
  
  // 删除【请填写功能名称】
  export function delElectricHistory(id) {
    return request({
      url: '/system/electrichistory/' + id,
      method: 'delete'
    })
  }
  
  // 导出【请填写功能名称】
  export function exportElectricHistory(query) {
    return request({
      url: '/system/electrichistory/export',
      method: 'get',
      params: query
    })
  }