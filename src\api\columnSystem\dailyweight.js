// 零耳牌处理  分栏称重记录
import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listDailyweight(query) {
  return request({
    url: '/system/dailyweight/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getDailyweight(id) {
  return request({
    url: '/system/dailyweight/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addDailyweight(data) {
  return request({
    url: '/system/dailyweight',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateDailyweight(data) {
  return request({
    url: '/system/dailyweight',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delDailyweight(id) {
  return request({
    url: '/system/dailyweight/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportDailyweight(query) {
  return request({
    url: '/system/dailyweight/export',
    method: 'get',
    params: query
  })
}



export function gatherOneDayDataFenLan(query) {
  return request({
    url: '/system/dailyweight/gatherOneDayData',
    method: 'post',
    data: query
  })
}
