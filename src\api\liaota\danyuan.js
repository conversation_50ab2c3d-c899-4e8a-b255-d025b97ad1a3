import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listDanyuan(query) {
  return request({
    url: '/system/danyuan/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getDanyuan(id) {
  return request({
    url: '/system/danyuan/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addDanyuan(data) {
  return request({
    url: '/system/danyuan',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateDanyuan(data) {
  return request({
    url: '/system/danyuan',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delDanyuan(id) {
  return request({
    url: '/system/danyuan/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportDanyuan(query) {
  return request({
    url: '/system/danyuan/export',
    method: 'get',
    params: query
  })
}