<template>
  <div>
    <!-- :before-close="handleClose" -->
    <el-dialog
      :title="$t('menu.switchCurrentFarm')"
      :visible.sync="show"
      width="400px"
      :showClose="false"
    >
      <div>
        <i
          class="el-icon-sort"
          style="transform: rotate(90deg); font-size: 20px"
        ></i>
        <el-select
          :placeholder="$t('menu.selectFarmName')"
          v-model="nowPigFarm"
        >
          <el-option
            v-for="item in pigFarmsOptions"
            :key="item.id"
            :label="`${item.id} - ${item.facname}`"
            :value="item.id"
            name="now-pigFarm"
            @keyup.enter.native="handleChange"
          ></el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAdd" v-hasPermi="['system:factory:add']">{{
          $t("menu.increasePigFarm")
        }}</el-button>
        <!-- <el-button @click="handleClosed">取 消</el-button> -->
        <el-button type="primary" @click="handleOk">{{
          $t("common.determine")
        }}</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改【猪场】对话框 -->
    <el-dialog
      :title="$t('menu.addPigFarm')"
      :visible.sync="open"
      width="400px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('menu.farmName')" prop="facname">
          <el-input
            v-model="form.facname"
            :placeholder="$t('menu.enterFarmName')"
          />
        </el-form-item>
        <!-- <el-form-item label="厂名" prop="colName1">
          <el-input v-model="form.colName1" placeholder="请输入厂名" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>

    <!-- <el-divider /> -->
  </div>
</template>

<script>
import {
  listFactory,
  addFactory,
  listFactoryByUser,
} from "@/api/system/factory";
import { listMsg } from "@/api/warning/outage";
import Cookies from "js-cookie";
import { isNil, isEmpty } from "lodash";
import store from "@/store";
import { formatDay } from "@/utils";

export default {
  data() {
    return {
      messageButton: [],
      pigFarmsOptions: [],
      nowPigFarm: null,
      // nowPigFarm: this.$store.state.settings.nowPigFarm,
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      user: store.getters && store.getters.user,
      // 表单校验
      rules: {
        facname: [
          {
            required: true,
            message: this.$t("menu.farmNameNotNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    show: {
      get() {
        return this.$store.state.settings.showSwitching;
      },
      set(val) {
        this.$store.dispatch("settings/changeSwitching", {
          key: "showSwitching",
          value: val,
        });
      },
    },
    // nowPigFarm: {
    // get() {
    //   return this.$store.state.settings.nowPigFarm;
    // },
    // set(val) {
    //   Cookies.set("nowPigFarm", val);
    //   this.$store.dispatch("settings/changeSwitching", {
    //     key: "nowPigFarm",
    //     value: val,
    //   });
    // },
    // },
  },
  //   created:在模板渲染成html前调用，即通常初始化某些属性值，然后再渲染成视图。
  // mounted:在模板渲染成html后调用，通常是初始化页面完成后，再对html的dom节点进行一些需要的操作。
  created() {
    // console.log("created");
    // listFactory().then((response) => {
    //   this.pigFarmsOptions = response.rows;
    // });
  },
  mounted() {
    // const user = store.getters && store.getters.user;
    // console.log("roles", this.user);
    //渲染之后
    //this.getDatas();
    listFactoryByUser({ userId: this.user.userId }).then((response) => {
      this.pigFarmsOptions = response.rows;
      let pigFarmsArr = [];
      this.pigFarmsOptions.forEach((i) => pigFarmsArr.push(i.id));
      if (
        !isNil(this.$store.state.settings.nowPigFarm) &&
        pigFarmsArr.includes(this.$store.state.settings.nowPigFarm)
      ) {
        this.nowPigFarm = this.$store.state.settings.nowPigFarm;
      } else {
        this.nowPigFarm = this.pigFarmsOptions[0].id;
        Cookies.set("nowPigFarm", this.nowPigFarm);
        this.$store.dispatch("settings/changeSwitching", {
          key: "nowPigFarm",
          value: this.nowPigFarm,
        });
      }
    });
  },
  methods: {
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        facname: null,
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          addFactory(this.form).then((response) => {
            this.msgSuccess(this.$t("common.addSuccess"));
            this.open = false;
            listFactoryByUser({ userId: this.user.userId }).then((response) => {
              this.pigFarmsOptions = response.rows;
            });
          });
        }
      });
    },
    handleClosed() {
      if (
        Cookies.get("nowPigFarm") === undefined ||
        Cookies.get("nowPigFarm") === "null"
      ) {
        this.msgError(this.$t("menu.nowFarmNameNotNull"));
      } else {
        this.show = false;
      }
    },
    handleOk() {
      Cookies.set("nowPigFarm", this.nowPigFarm);
      if (
        Cookies.get("nowPigFarm") === undefined ||
        Cookies.get("nowPigFarm") === "null"
      ) {
        this.msgError(this.$t("menu.nowFarmNameNotNull"));
      } else {
        this.$store.dispatch("settings/changeSwitching", {
          key: "nowPigFarm",
          value: this.nowPigFarm,
        });
        this.show = false;
        this.$store.dispatch("tagsView/delAllViews");
        this.$router.push({ path: "/index" });
        //数据库消息提示
        this.getDicts("sys_notice_button").then((response) => {
          this.messageButton = response.data;
          if (this.messageButton && this.messageButton[0].dictValue == "Y") {
            this.$notify({
              title: "提醒",
              message: this.messageButton[1].dictValue,
              type: "warning",
              offset: 100,
              duration: 0,
            });
          }
        });

        // // 断电缺项信息提示
        // listMsg({
        //   faid: this.nowPigFarm,
        //   uptime: formatDay(new Date().toDateString()),
        // }).then((response) => {
        //   if (response) {
        //     console.log("111111111111", formatDay(new Date().toDateString()));
        //     this.$notify({
        //       title: "提醒",
        //       message: "这是一条警告的提示消息",
        //       type: "warning",
        //       offset: 100
        //     });
        //   }
        // });
      }
    },
    handleClose(done) {
      this.$confirm(this.$t("common.confirmClose"))
        .then((_) => {
          if (
            Cookies.get("nowPigFarm") === undefined ||
            Cookies.get("nowPigFarm") === "null"
          ) {
            this.msgError(this.$t("menu.nowFarmNameNotNull"));
          } else {
            done();
          }
        })
        .catch((_) => {});
    },
    handleChange(val) {
      // this.$store.dispatch("settings/changeSwitching", {
      //   key: "nowPigFarm",
      //   value: val,
      // });
      // this.$router.push({ path: "/index"})
    },
  },
};
</script>
