//同时开启
import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listSametime(query) {
    return request({
      url: '/system/sametime/list',
      method: 'get',
      params: query
    })
  }
  
  // 查询【请填写功能名称】详细
  export function getSametime(indexn) {
    return request({
      url: '/system/sametime/' + indexn,
      method: 'get'
    })
  }
  
  // 新增【请填写功能名称】
  export function addSametime(data) {
    return request({
      url: '/system/sametime',
      method: 'post',
      data: data
    })
  }
  
  // 修改【请填写功能名称】
  export function updateSametime(data) {
    return request({
      url: '/system/sametime',
      method: 'put',
      data: data
    })
  }
  
  // 删除【请填写功能名称】
  export function delSametime(indexn) {
    return request({
      url: '/system/sametime/' + indexn,
      method: 'delete'
    })
  }
  
  // 导出【请填写功能名称】
  export function exportSametime(query) {
    return request({
      url: '/system/sametime/export',
      method: 'get',
      params: query
    })
  }