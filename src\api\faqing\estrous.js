import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listEstrous(query) {
  return request({
    url: '/system/estrous/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getEstrous(id) {
  return request({
    url: '/system/estrous/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addEstrous(data) {
  return request({
    url: '/system/estrous',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateEstrous(data) {
  return request({
    url: '/system/estrous',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delEstrous(id) {
  return request({
    url: '/system/estrous/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportEstrous(query) {
  return request({
    url: '/system/estrous/export',
    method: 'get',
    params: query
  })
}
