<template>
  <span
    class="ear-tag-link"
    :class="{ clickable: clickable }"
    @click="handleClick"
    :title="title || `点击查看${earTag}的详细信息`"
    ><i class="el-icon-link"></i>
    {{ earTag }}
  </span>
</template>

<script>
export default {
  name: "EarTagLink",
  props: {
    // 耳缺号
    earTag: {
      type: [String, Number],
      required: true,
    },
    // 栏号
    nindex: {
      type: [String, Number],
      default: null,
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: true,
    },
    // 自定义标题
    title: {
      type: String,
      default: "",
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: "",
    },
    // 点击事件类型
    clickType: {
      type: String,
      default: "navigate", // navigate: 路由跳转, emit: 触发事件
      validator: (value) => ["navigate", "emit"].includes(value),
    },
    // 跳转路径（当clickType为navigate时使用）
    routePath: {
      type: String,
      default: "/boarMeasure/measureday",
    },
  },
  computed: {
    computedClass() {
      return [
        "ear-tag-link",
        {
          clickable: this.clickable,
          disabled: !this.clickable,
        },
        this.customClass,
      ];
    },
  },
  methods: {
    handleClick() {
      if (!this.clickable) return;

      if (this.clickType === "navigate") {
        // 路由跳转
        this.navigateToDetail();
      } else if (this.clickType === "emit") {
        // 触发事件
        this.$emit("click", {
          earTag: this.earTag,
          nindex: this.nindex,
        });
      }
    },

    navigateToDetail() {
      const query = {
        mid: this.earTag,
      };

      // 如果有栏号，也传递栏号
      if (this.nindex) {
        query.nindex = this.nindex;
      }

      this.$router.push({
        path: this.routePath,
        query: query,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.ear-tag-link {
  display: inline-block;
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;

  &.clickable {
    cursor: pointer;

    &:hover {
      color: #66b1ff;
      text-decoration: underline;
      transform: translateY(-1px);
    }

    &:active {
      color: #3a8ee6;
      transform: translateY(0);
    }
  }

  &.disabled {
    color: #c0c4cc;
    cursor: not-allowed;
  }
}

/* 不同主题样式 */
.ear-tag-link.theme-success {
  color: #67c23a;

  &.clickable:hover {
    color: #85ce61;
  }
}

.ear-tag-link.theme-warning {
  color: #e6a23c;

  &.clickable:hover {
    color: #ebb563;
  }
}

.ear-tag-link.theme-danger {
  color: #f56c6c;

  &.clickable:hover {
    color: #f78989;
  }
}

.ear-tag-link.theme-info {
  color: #909399;

  &.clickable:hover {
    color: #a6a9ad;
  }
}
</style>
