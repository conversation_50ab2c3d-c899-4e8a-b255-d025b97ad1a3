<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('electric.metername')" prop="metername">
        <el-select
          v-model="queryParams.metername"
          :placeholder="$t('electric.enterMetername')"
          clearable
          size="small"
        >
          <el-option
            v-for="item in meterOptions"
            :key="item.id"
            :label="item.metername"
            :value="item.metername"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('liaota.gatewayId')" prop="switchid">
        <el-input
          v-model="queryParams.switchid"
          :placeholder="$t('liaota.enterGatewayId')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.uploadTime')" prop="uptime">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.uptime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          :placeholder="$t('common.chooseUploadTime')"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('electric.meterWorkStatus')" prop="slavenetwork">
        <el-select
          v-model="queryParams.slavenetwork"
          :placeholder="$t('electric.choosetMeterWorkingStatus')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="dict in slavenetworkList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="$t('electric.energy')" prop="energy">
        <el-input
          v-model="queryParams.energy"
          :placeholder="$t('electric.enterEnergy')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item :label="$t('huanKong.alarmInformation')" prop="alarmmsg">
        <el-input
          v-model="queryParams.alarmmsg"
          :placeholder="$t('huanKong.enterAlarmInformation')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:meter:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:meter:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:meter:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:meter:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="slaveList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('common.serialNumber')"
        align="center"
        prop="id"
      />
      <el-table-column
        :label="$t('electric.meterid')"
        align="center"
        prop="meterid"
      />
      <el-table-column
        :label="$t('electric.metername')"
        align="center"
        prop="metername"
        width="180"
      />
      <el-table-column
        :label="$t('liaota.gatewayId')"
        align="center"
        prop="switchid"
      />
      <el-table-column
        :label="$t('huanKong.pigFarmID')"
        align="center"
        prop="mfactory"
      />
      <el-table-column
        :label="$t('common.uploadTime')"
        align="center"
        prop="uptime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.uptime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('electric.meterWorkStatus')"
        align="center"
        prop="slavenetwork"
      >
        <template slot-scope="scope">
          <el-tag
            effect="dark"
            :type="scope.row.slavenetwork === 'ok' ? 'success' : 'danger'"
            disable-transitions
            >{{
              scope.row.slavenetwork === "ok"
                ? $t("liaota.online")
                : $t("liaota.offline")
            }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('electric.energy')"
        align="center"
        prop="energy"
      />
      <el-table-column
        :label="$t('huanKong.alarmInformation')"
        align="center"
        prop="alarmmsg"
        show-overflow-tooltip
      />
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:meter:edit']"
            >{{ $t("common.update") }}</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:meter:remove']"
            >{{ $t("common.delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【料塔历史数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('electric.meterid')" prop="meterid">
          <el-input
            v-model="form.meterid"
            :placeholder="$t('electric.meterid')"
          />
        </el-form-item>
        <el-form-item :label="$t('electric.metername')" prop="metername">
          <el-input
            v-model="form.metername"
            :placeholder="$t('electric.enterMetername')"
          />
        </el-form-item>
        <el-form-item :label="$t('liaota.gatewayId')" prop="switchid">
          <el-input
            v-model="form.switchid"
            :placeholder="$t('liaota.enterGatewayId')"
          />
        </el-form-item>
        <el-form-item :label="$t('common.uploadTime')" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 380px"
            v-model="form.uptime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.chooseUploadTime')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          :label="$t('electric.meterWorkStatus')"
          prop="slavenetwork"
        >
          <el-select
            v-model="form.slavenetwork"
            :placeholder="$t('electric.choosetMeterWorkingStatus')"
          >
            <el-option
              v-for="dict in slavenetworkList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('electric.energy')" prop="energy">
          <el-input
            v-model="form.energy"
            :placeholder="$t('electric.enterEnergy')"
          />
        </el-form-item>
        <el-form-item :label="$t('huanKong.alarmInformation')" prop="alarmmsg">
          <el-input
            v-model="form.alarmmsg"
            :placeholder="$t('huanKong.enterAlarmInformation')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMeter,
  getMeter,
  delMeter,
  addMeter,
  updateMeter,
  exportMeter,
} from "@/api/electric/meter";

export default {
  name: "Meter",
  components: {},
  data() {
    return {
      meterOptions: [],
      //设备状态
      slavenetworkList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【料塔历史数据】表格数据
      slaveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        meterid: null,
        metername: null,
        switchid: null,
        // homeid: null,
        uptime: null,
        slavenetwork: null,
        energy: null,
        alarmmsg: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        meterid: [
          {
            required: true,
            message: this.$t("electric.meterIdNotNull"),
            trigger: "blur",
          },
        ],
        metername: [
          {
            required: true,
            message: this.$t("electric.meterNameNotNull"),
            trigger: "blur",
          },
        ],
        switchid: [
          {
            required: true,
            message: this.$t("liaota.gatewayIdNotNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  mounted() {
    // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
    listMeter({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.meterOptions = response.rows;
      }
    );
  },
  created() {
    this.getList();
    this.getDicts("hk_work_status").then((response) => {
      this.slavenetworkList = response.data;
    });
  },
  methods: {
    /** 查询【料塔历史数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listMeter(this.queryParams).then((response) => {
        this.slaveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        meterid: null,
        metername: null,
        switchid: null,
        energy: null,
        uptime: null,
        slavenetwork: "ok",
        alarmmsg: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("electric.addMeterConfig");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMeter(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("electric.modMeterConfig");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateMeter(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            // this.form.facid = this.$store.state.settings.nowPigFarm;
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addMeter(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("electric.sureCancelMeterConfig") +
          '"' +
          ids +
          '"' +
          this.$t("liaota.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delMeter(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.deleteSuccess"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("electric.sureExportMeterConfig"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportMeter(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>
