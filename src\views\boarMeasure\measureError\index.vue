<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
        <el-input
          v-model="queryParams.mid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('boarMeasure.mrfid')" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('boarMeasure.feedingDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="cyan" @click="handleZeroQuery">{{
          $t("boarMeasure.zeroQuery")
        }}</el-button>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['boarMeasure:measure:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['boarMeasure:measure:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['boarMeasure:measure:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['boarMeasure:measure:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="measureList"
      @selection-change="handleSelectionChange"
      :cell-style="{ padding: '0' }"
    >
      <el-table-column type="selection" width="50" align="center" />
      <!-- <el-table-column label="序号" align="center" prop="indexn" /> -->

      <el-table-column
        :label="$t('boarMeasure.mid')"
        align="center"
        prop="mid"
        width="180"
      />
      <el-table-column
        :label="$t('boarMeasure.mrfid')"
        align="center"
        prop="mrfid"
        width="150"
      />
      <el-table-column
        :label="$t('boarMeasure.nindex')"
        align="center"
        prop="nindex"
        width="70"
      />

      <el-table-column
        :label="$t('boarMeasure.intakesStartTime')"
        align="center"
        prop="nstartdate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.nstartdate) }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('boarMeasure.intakesEndTime')"
        align="center"
        prop="nenddate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.nenddate) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.intakesTimesS')"
        align="right"
        prop="nseconds"
        width="120"
        ><template slot-scope="scope">
          <span>{{ scope.row.nseconds | formatThousands(0) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.nfriweight')"
        align="right"
        prop="nfriweight"
        ><template slot-scope="scope">
          <span>{{ scope.row.nfriweight | formatThousands(0) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.nsecweight')"
        align="right"
        prop="nsecweight"
        ><template slot-scope="scope">
          <span>{{ scope.row.nsecweight | formatThousands(0) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.ningestionG')"
        align="right"
        prop="ningestion"
        ><template slot-scope="scope">
          <span>{{ scope.row.ningestion | formatThousands(0) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.weightKg')"
        align="right"
        prop="nweight"
        ><template slot-scope="scope">
          <span>{{ scope.row.nweight | formatNumber(1) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.nnum')"
        align="right"
        prop="nnum"
      />
      <el-table-column
        :label="$t('boarMeasure.DPCG')"
        align="center"
        prop="ndpc"
      />
      <!-- <el-table-column :label="$t('boarMeasure.ntype')" align="center" prop="ntype">
        <template slot-scope="scope">
          <el-tag
            effect="dark"
            :type="scope.row.ntype === 1 ? 'success' : 'danger'"
            disable-transitions
            >{{ scope.row.ntype === 1 ? "在线" : "离线" }}</el-tag
          >
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
        width="50"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['boarMeasure:measure:edit']"
          />
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['boarMeasure:measure:remove']"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【采食数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="68px">
        <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
          <el-input
            v-model="form.mid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mrfid')" prop="mrfid">
          <el-input
            v-model="form.mrfid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mdorm')" prop="mdorm">
          <el-input
            v-model="form.mdorm"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
          <el-input
            v-model="form.nindex"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mname')" prop="mname">
          <el-input
            v-model="form.mname"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.naddress')" prop="naddress">
          <el-input
            v-model="form.naddress"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('boarMeasure.intakesStartTime')"
          prop="nstartdate"
        >
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.nstartdate"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.intakesEndTime')" prop="nenddate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.nenddate"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.intakesTimesS')" prop="nseconds">
          <el-input
            v-model="form.nseconds"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nfriweight')" prop="nfriweight">
          <el-input
            v-model="form.nfriweight"
            :placeholder="$t('common.pleaseInput')"
            @blur="formatToInteger('nfriweight')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nsecweight')" prop="nsecweight">
          <el-input
            v-model="form.nsecweight"
            :placeholder="$t('common.pleaseInput')"
            @blur="formatToInteger('nsecweight')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.ningestionG')" prop="ningestion">
          <el-input
            v-model="form.ningestion"
            :placeholder="$t('common.pleaseInput')"
            @blur="formatToInteger('ningestion')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.weightKg')" prop="nweight">
          <el-input
            v-model="form.nweight"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nnum')" prop="nnum">
          <el-input
            v-model="form.nnum"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.DPCG')" prop="ndpc">
          <el-input
            v-model="form.ndpc"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('menu.state')" prop="ntype">
          <el-select
            v-model="form.ntype"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in typeList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMeasure,
  getMeasure,
  delMeasure,
  addMeasure,
  updateMeasure,
  exportMeasure,
} from "@/api/system/measure";

export default {
  name: "MeasureError",
  components: {},
  data() {
    return {
      //测定状态
      typeList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【采食数据】表格数据
      measureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nstartdate: null,
        nenddate: null,
        nseconds: null,
        nfriweight: null,
        nsecweight: null,
        ningestion: null,
        nweight: null,
        nnum: null,
        ndpc: null,
        ntype: 2,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mid: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  created() {
    this.getDicts("ceding_measure_type").then((response) => {
      this.typeList = response.data;
    });
    this.getList();
  },
  methods: {
    /** 查询【采食数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listMeasure(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange, "n")
      ).then((response) => {
        this.measureList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nstartdate: null,
        nenddate: null,
        nseconds: null,
        nfriweight: null,
        nsecweight: null,
        ningestion: null,
        nweight: null,
        nnum: null,
        ndpc: null,
        ntype: 2,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleZeroQuery() {
      this.queryParams.mrfid = "000000000000000";
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("boarMeasure.addMeasure");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.indexn || this.ids;
      getMeasure(indexn).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("boarMeasure.updateMeasure");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.indexn != null) {
            updateMeasure(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addMeasure(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        this.$t("boarMeasure.sureCancelMeasure") +
          `"` +
          indexns +
          `"` +
          this.$t("boarMeasure.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delMeasure(indexns);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      // const queryParams = this.queryParams;

      this.$confirm(
        this.$t("boarMeasure.sureExportMeasure"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )

        .then(() => {
          return exportMeasure(
            this.addDateRangeRe(
              this.queryParams,
              this.queryParams.dateRange,
              "n"
            )
          );
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    // 格式化为整数（去掉小数位）
    formatToInteger(field) {
      if (this.form[field] && !isNaN(this.form[field])) {
        this.form[field] = Math.round(Number(this.form[field]));
      }
    },
  },
};
</script>
