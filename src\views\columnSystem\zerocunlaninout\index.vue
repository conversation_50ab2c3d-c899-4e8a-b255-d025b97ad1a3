<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
            <span>
              <el-select
                  v-model="queryParams.naddress"
                  :placeholder="$t('common.pleaseChoose')"
                  size="small"
                  @change="queryController"
              >
                <el-option
                    v-for="item in mNameOptions"
                    :key="item.naddress"
                    :label="item.naddress"
                    :value="item.naddress"
                    @click.native="queryController"
                ></el-option>
              </el-select>


            </span>
      </el-form-item>
    </el-form>

    <el-col :span="24" class="card-box">
      <el-row>
        <el-col :xs="24" :sm="24" :md="6" :lg="6" class="card-box-re">
          <el-card>
            <el-row style="display: flex">
              <el-col :span="8" style="text-align: center">
                <span class="demonstration card font_comom">{{
                    $t("columnSystem.cunlanNum")
                  }}</span>

                <el-image
                    style="height: 80px"
                    :src="require('@/assets/icons/png/pig.png')"
                ></el-image>
              </el-col>
              <el-col :span="1">
                <el-divider
                    style="height: 100%"
                    direction="vertical"
                ></el-divider>
              </el-col>

              <el-col :span="9" class="spanS">
                <span>{{ this.cunlannum }}</span>
              </el-col>
              <el-col :span="6" class="rowStyle">
                <span>头</span>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
    </el-col>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleChushi"
        >初始化</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['system:piginout:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:piginout:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:piginout:export']"
        >导出</el-button>
      </el-col>
	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="piginoutList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="舍号" align="center" prop="mdorm" />
      <el-table-column label="栏号" align="center" prop="nindex" />
      <el-table-column label="设备地址" align="center" prop="naddress" />
      <el-table-column label="进出数量" align="center" prop="inoutnum" />
      <el-table-column label="处理前头数" align="center" prop="prenum" />
      <el-table-column label="处理后头数" align="center" prop="backnum" />
      <el-table-column label="备注" align="center" prop="tip" />
      <el-table-column label="处理日期" align="center" prop="ndate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ndate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['system:piginout:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['system:piginout:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工厂" prop="mfactory" style = "display: none">
          <el-input v-model="form.mfactory" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="舍号" prop="mdorm">
          <el-input v-model="form.mdorm" placeholder="请输入" disabled/>
        </el-form-item>
        <el-form-item label="栏号" prop="nindex">
          <el-input v-model="form.nindex" placeholder="请输入栏号" disabled/>
        </el-form-item>
        <el-form-item label="设备名称" prop="mname">
          <el-input v-model="form.mname" placeholder="请输入设备名称" disabled/>
        </el-form-item>
        <el-form-item label="设备地址" prop="naddress">
          <el-input v-model="form.naddress" placeholder="请输入设备地址" disabled/>
        </el-form-item>
        <el-form-item label="主机地址" prop="naddressup">
          <el-input v-model="form.naddressup" placeholder="请输入主机地址" disabled/>
        </el-form-item>
        <el-form-item label="进出数量" prop="inoutnum">
          <el-input v-model="form.inoutnum" placeholder="请输入进出数量" v-on:change="changeNum" />
        </el-form-item>
        <el-form-item label="处理前头数" prop="prenum">
          <el-input v-model="form.prenum" placeholder="请输入处理前头数" disabled></el-input>
        </el-form-item>
        <el-form-item label="处理后头数" prop="backnum">
          <el-input v-model="form.backnum" placeholder="请输入处理后头数" disabled></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="tip">
          <el-input v-model="form.tip" placeholder="备注" />
        </el-form-item>
        <el-form-item label="处理日期" prop="ndate">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.ndate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择处理日期">
          </el-date-picker>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open1" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工厂" prop="mfactory" style = "display: none">
          <el-input v-model="form.mfactory" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="舍号" prop="mdorm">
          <el-input v-model="form.mdorm" placeholder="请输入" disabled/>
        </el-form-item>
        <el-form-item label="栏号" prop="nindex">
          <el-input v-model="form.nindex" placeholder="请输入栏号" disabled/>
        </el-form-item>
        <el-form-item label="设备名称" prop="mname">
          <el-input v-model="form.mname" placeholder="请输入设备名称" disabled/>
        </el-form-item>
        <el-form-item label="设备地址" prop="naddress">
          <el-input v-model="form.naddress" placeholder="请输入设备地址" disabled/>
        </el-form-item>
        <el-form-item label="主机地址" prop="naddressup">
          <el-input v-model="form.naddressup" placeholder="请输入主机地址" disabled/>
        </el-form-item>
        <el-form-item label="初始化头数" prop="backnum">
          <el-input v-model="form.backnum" placeholder="请输入处理后头数" ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="tip">
          <el-input v-model="form.tip" placeholder="备注" />
        </el-form-item>
        <el-form-item label="处理日期" prop="ndate">
          <el-date-picker clearable size="small" style="width: 200px"
                          v-model="form.ndate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="选择处理日期">
          </el-date-picker>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import { listPiginout, getPiginout, delPiginout, addPiginout, updatePiginout, exportPiginout } from "@/api/columnSystem/piginout";
import { isEmpty } from 'lodash'
import { updateControldaily,listControldaily } from "@/api/columnSystem/controldaily";

export default {
  name: "Piginout",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      //栏号数组
      mNameOptions: [],
      //选中的控制器
      selectContrllor: null,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      piginoutList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层
      open1: false,
      //存栏量
      cunlannum: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        inoutnum: null,
        prenum: null,
        backnum: null,
        tip: null,
        ndate: null,
        colString1: null,
        colString2: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mfactory: [
          { required: true, message: "工厂不能为空", trigger: "blur" }
        ],
      }
    };
  },
  watch: {

  },
  created() {

    listControldaily({
      ntype: 3,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.mNameOptions = response.rows;
      this.queryParams.naddress = this.mNameOptions[0].naddress;
      this.cunlannum = this.mNameOptions[0].cunlannum;
      this.selectContrllor = this.mNameOptions[0];
      this.getList({
        naddress: this.queryParams.naddress,
        mfactory: this.$store.state.settings.nowPigFarm,
      });
    });

  },
  methods: {
    queryController() {
      listControldaily({
        ntype: 3,
        mfactory: this.$store.state.settings.nowPigFarm,
        naddress: this.queryParams.naddress
      }).then((response) => {
            this.cunlannum = response.rows[0].cunlannum
            this.selectContrllor = response.rows[0]
            this.getList({
              naddress: this.queryParams.naddress,
              mfactory: this.$store.state.settings.nowPigFarm
            })
          });
    },
    changeNum(){
      this.form.backnum = parseInt(this.form.inoutnum) + parseInt(this.form.prenum);
    },
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listPiginout(this.queryParams).then(response => {
        this.piginoutList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.open1 = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mfactory: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        inoutnum: null,
        prenum: null,
        backnum: null,
        tip: null,
        ndate: null,
        colString1: null,
        colString2: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
      this.form.mfactory = this.$store.state.settings.nowPigFarm;
      this.form.mdorm = this.selectContrllor.mdorm;
      this.form.nindex = this.selectContrllor.nindex;
      this.form.naddress = this.selectContrllor.naddress;
      this.form.naddressup = this.selectContrllor.naddressup;
      this.form.prenum = this.selectContrllor.cunlannum;
    },

    handleChushi(){
      this.reset();
      this.open1 = true;
      this.title = "初始化头数";
      this.form.mfactory = this.$store.state.settings.nowPigFarm;
      this.form.mdorm = this.selectContrllor.mdorm;
      this.form.nindex = this.selectContrllor.nindex;
      this.form.naddress = this.selectContrllor.naddress;
      this.form.naddressup = this.selectContrllor.naddressup;
      this.form.tip = "初始化头数";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPiginout(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.mfactory = this.$store.state.settings.nowPigFarm;
          if (this.form.id != null) {
            updatePiginout(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.open1 = false;
              this.queryController();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addPiginout(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.open1 = false;
              this.queryController();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除【请填写功能名称】编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delPiginout(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有【请填写功能名称】数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportPiginout(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
<style scoped lang="scss">
.rowStyle {
  line-height: 70px;
  font-size: 30px;
  font-family: Helvetica;
  margin: auto;
  text-align: center;
  color: #82848a;
}
.spanS {
  line-height: 70px;
  font-size: 45px;
  font-family: Helvetica;
  margin: auto;
  text-align: center;
  color: #26d8e0e0;
}
</style>
