<template>
  <!-- <div v-if="!item.hidden"> -->
  <div>
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        !item.alwaysShow
      "
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{ 'submenu-title-noDropdown': !isNest }"
        >
          <img src="@/assets/icons/png/none.png" />
          <svg-icon
            :icon-class="onlyOneChild.meta && onlyOneChild.meta.icon"
            :style="item.hidden ? 'fill:#909399' : ''"
          />
          <el-tooltip
            :disabled="this.$store.state.app.type == 'zh'"
            :content="$t(onlyOneChild.meta.title)"
            placement="bottom-start"
          >
            <span :style="item.hidden ? 'color:#909399' : ''">{{
              $t(onlyOneChild.meta.title)
            }}</span>
          </el-tooltip>
          <!-- <item :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)" :title="onlyOneChild.meta.title" /> -->
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body
    >
      <template slot="title">
        <svg-icon
          :icon-class="item.meta && item.meta.icon"
          :style="item.hidden ? 'fill:#909399' : ''"
        />
        <!-- <img
          style="margin-left: -5px; align-items: center; /* 垂直居中 */"
          :style="item.meta && item.meta.icon == 'none' ? '' : 'width: 40%;'"
          :src="
            require(`@/assets/icons/png/${item.meta && item.meta.icon}.png`)
          "
        /> -->
        <el-tooltip
          :disabled="this.$store.state.app.type == 'zh'"
          :content="$t(item.meta.title)"
          placement="bottom-start"
        >
          <span :style="item.hidden ? 'color:#909399' : ''">{{
            $t(item.meta.title)
          }}</span>
        </el-tooltip>
        <!-- <item v-if="item.meta" :icon="item.meta && item.meta.icon" :title="item.meta.title" /> -->
      </template>
      <sidebar-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from "path";
import { isExternal } from "@/utils/validate";
import Item from "./Item";
import AppLink from "./Link";
import FixiOSBug from "./FixiOSBug";

export default {
  name: "SidebarItem",
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true,
    },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: "",
    },
  },
  data() {
    this.onlyOneChild = null;
    return {
      // disabled: true,
    };
  },
  // computed: {
  //   disabled() {
  //     return this.$store.state.app.type == "zh";
  //   },
  // },
  // watch: {
  //   disabled() {
  //     return this.$store.state.app.type == "zh";
  //   },
  // },

  methods: {
    hasOneShowingChild(children = [], parent) {
      if (!children) {
        children = [];
      }
      const showingChildren = children.filter((item) => {
        if (item.hidden) {
          return false;
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item;
          return true;
        }
      });

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true;
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: "", noShowingChildren: true };
        return true;
      }

      return false;
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath;
      }
      if (isExternal(this.basePath)) {
        return this.basePath;
      }
      return path.resolve(this.basePath, routePath);
    },
  },
};
</script>
