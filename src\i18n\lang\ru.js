const ru = {
  common: {
    exception: 'аномальный',
    systemInterface: 'системный интерфейс',
    timeout: 'Тайм-аут запроса системного интерфейса',
    networkError: 'Исключение подключения к серверному интерфейсу',
    reRegister: 'перерегистрировать',
    systemHint: 'системная подсказка',
    logInAgain: 'Ваш статус входа в систему истек, вы можете остаться на этой странице или войти снова',
    loadingResources: 'Идет загрузка. Ожидайте!',
    refresh: 'обновить',
    hideSearch: 'скрыть поиск',
    showSearch: 'показать поиск',
    previousPage: 'Предыдущая страница',
    nextPage: 'следущая страница',
    saveAll: 'Сохранить все',
    save: 'Сохранить',
    search: 'поиск',
    reset: 'сброс настроек',
    add: 'Добавить',
    update: 'Обновить',
    delete: 'Удалить',
    export: 'экспорт',
    import: 'импорт',
    batchUpdate: 'Пакетное обновление',
    serialNumber: 'серийный номер',
    operate: 'действовать',
    uploadTime: 'Время загрузки',
    chooseUploadTime: 'Выберите время загрузки',
    determine: 'Подтвердить',
    cancel: 'Отмена',
    modifiedSuccess: 'Успешно изменено',
    addSuccess: 'Успешно добавлено',
    deleteSuccess: 'Успешно удалено',
    warn: 'Предупреждение',
    close: 'закрытие',
    confirmClose: 'Подтвердить закрытие?',
    submit: 'Отправить',
    pleaseInput: "Введите",
    pleaseChoose: "Пожалуйста, выберите",
    notNull: 'не может быть пустым',
    dataItem: 'Данные?',
    startDate: 'Начальная дата',
    endDate: 'Начальная дата',
  },
  huanKong: {
    nowTem: "Текущая температура",
    targetTem: "Целевая температура",
    updateTime: "Обновить время",
    online: "онлайн",
    offline: 'не в сети',
    pigHouseName: 'Название свинарника',
    choosePigHouseName: 'Пожалуйста, введите ID свинарника',
    pigHouseID: 'ID фермы',
    choosePigHouseID: 'Пожалуйста, введите ID фермы',
    pigFarmID: 'идентификатор фермы',
    choosePigFarmID: 'Пожалуйста, введите идентификатор фермы',
    pigHouseNameNotNull: "Название свинарника не может быть пустым",
    pigHouseIDNotNull: "ID фермы не может быть пустым",
    addPigHouse: "Добавить свинарник",
    modifyPigHouse: "Изменить свинарник",
    sureCancelPigHouse: 'Вы уверены, что хотите удалили свинарник?',
    sureExportPigHouse: 'Вы уверены, что хотите экспортировать данные?',
    fanSwitchSet: 'Настройки переключателя вентилятора',
    minimumVentilationPhase: 'Минимальная ступень вентиляции',
    connectErr: 'bad',
    temperatureStage: 'Температура',
    editTip: 'Перед изменением параметров выполните операцию обновления, чтобы убедиться, что данные текущей страницы обновлены!',
    environmentalControl: "Контроль микроклимата",
    pigHousesReal: "Мониторинг свинофермы в режиме реального времени",
    controlConfiguration: "Конфигурация устройства контроля микроклимата",
    controlData: "Данные истории микроклимата",
    controlReport: "Сводный отчет по микроклимату",
    nowControlNotStarted: "Текущее устройство контроля микроклимата не запущено!",
    temperature: "Температура",
    humidity: " Влажность",
    waterLevel: "Уровень воды",
    energyUsed: "Потребление электроэнергии",
    temperatureCurve: "Температурная кривая",
    fixedFrequencyFan: "Вентилятор с постоянной частотой вращения",
    inverterFan: "Вентилятор с переменной частотой вращения",
    curtain1: "Приток 1",
    curtain2: "Приток 2",
    curtain3: "Приток 3",
    curtain4: "Приток 4",
    choosePigHouse: 'Выбор свинофермы',
    pigHouseUnit: 'Свинарник',
    gatewayId: 'ID для входа в систему',
    chooseGatewayId: 'Выбор ID для входа в систему',
    envControlId: 'ID для входа в меню микроклимат',
    chooseEnvControlId: 'Выбор ID для входа в меню микроклимат',
    envControlName: 'Параметр микроклимата',
    chooseEnvControlName: 'Вход в меню Параметр микроклимата',
    pigHouseID: 'ID Свинофермы',
    choosePigHouseID: 'Вход в ID Свинофермы',
    envWorkStatus: "Статус контроля микроклимата",
    chooseEnvWorkStatus: 'Выбор статуса контроля микроклимата',
    enterTemperature: 'Вход в меню Температура',
    enterHumidity: 'Вход в меню Влажность',
    water: 'Вода',
    enterWater: 'Вход в меню уровень воды',
    water1: 'Приток 1',
    water2: 'Приток 2',
    water3: 'Приток 3',
    water4: ' Приток 4',
    enterWater1: "Вход в меню Приток 1",
    enterWater2: 'Вход в меню Приток 2',
    enterWater3: 'Вход в меню Приток 3',
    enterWater4: 'Вход в меню Приток 4',
    fixedFan1: "Вентилятор с постоянной частотой вращения 1",
    fixedFan2: 'Вентилятор с постоянной частотой вращения 2',
    fixedFan3: "Вентилятор с постоянной частотой вращения 3",
    fixedFan4: 'Вентилятор с постоянной частотой вращения 4',
    fixedFan5: "Вентилятор с постоянной частотой вращения 5",
    fixedFan6: 'Вентилятор с постоянной частотой вращения 6',
    fixedFan7: "Вентилятор с постоянной частотой вращения 7",
    fixedFan8: 'Вентилятор с постоянной частотой вращения 8',
    fixedFan9: "Вентилятор с постоянной частотой вращения 9",
    fixedFan10: 'Вентилятор с постоянной частотой вращения 10',
    fixedFan11: "Вентилятор с постоянной частотой вращения 11",
    fixedFan12: 'Вентилятор с постоянной частотой вращения 12',
    fixedFan13: "Вентилятор с постоянной частотой вращения 13",
    fixedFan14: 'Вентилятор с постоянной частотой вращения 14',
    fixedFan15: "Вентилятор с постоянной частотой вращения 15",
    fixedFan16: 'Вентилятор с постоянной частотой вращения 16',
    fixedFan17: "Вентилятор с постоянной частотой вращения 17",
    fixedFan18: 'Вентилятор с постоянной частотой вращения 18',
    fixedFan19: "Вентилятор с постоянной частотой вращения 19",
    fixedFan20: 'Вентилятор с постоянной частотой вращения 20',
    fixedFan21: "Вентилятор с постоянной частотой вращения 21",
    fixedFan22: 'Вентилятор с постоянной частотой вращения 22',
    fixedFan23: "Вентилятор с постоянной частотой вращения 23",
    fixedFan24: 'Вентилятор с постоянной частотой вращения 24',
    enterFixedFan1: "Настройки Вентилятор с постоянной частотой вращения 1",
    enterFixedFan2: 'Настройки Вентилятор с постоянной частотой вращения 2',
    enterFixedFan3: "Настройки Вентилятор с постоянной частотой вращения 3",
    enterFixedFan4: 'Настройки Вентилятор с постоянной частотой вращения 4',
    enterFixedFan5: "Настройки Вентилятор с постоянной частотой вращения 5",
    enterFixedFan6: 'Настройки Вентилятор с постоянной частотой вращения 6',
    enterFixedFan7: "Настройки Вентилятор с постоянной частотой вращения 7",
    enterFixedFan8: 'Настройки Вентилятор с постоянной частотой вращения 8',
    enterFixedFan9: "Настройки Вентилятор с постоянной частотой вращения 9",
    enterFixedFan10: 'Настройки Вентилятор с постоянной частотой вращения 10',
    enterFixedFan11: "Настройки Вентилятор с постоянной частотой вращения 11",
    enterFixedFan12: 'Настройки Вентилятор с постоянной частотой вращения 12',
    enterFixedFan13: "Настройки Вентилятор с постоянной частотой вращения 13",
    enterFixedFan14: 'Настройки Вентилятор с постоянной частотой вращения 14',
    enterFixedFan15: "Настройки Вентилятор с постоянной частотой вращения 15",
    enterFixedFan16: 'Настройки Вентилятор с постоянной частотой вращения 16',
    fan1: "Вентилятор с переменной частотой вращения 1",
    fan2: 'Вентилятор с переменной частотой вращения 2',
    fan3: "Вентилятор с переменной частотой вращения 3",
    fan4: 'Вентилятор с переменной частотой вращения 4',
    fan5: "Вентилятор с переменной частотой вращения 5",
    fan6: 'Вентилятор с переменной частотой вращения 6',
    fan7: "Вентилятор с переменной частотой вращения 7",
    fan8: 'Вентилятор с переменной частотой вращения 8',
    enterFan1: "Настройки Вентилятор с переменной частотой вращения 1",
    enterFan2: 'Настройки Вентилятор с переменной частотой вращения 2',
    enterFan3: "Настройки Вентилятор с переменной частотой вращения 3",
    enterFan4: 'Настройки Вентилятор с переменной частотой вращения 4',
    alarmInformation: 'Сигнал тревоги',
    alarmNo: 'Нет информации о тревоге',
    enterAlarmInformation: 'Вход в меню Сигнал тревоги',
    envControlIdNotNull: 'ID для входа в меню контроля микроклиматом не может быть пустым',
    envControlNameNotNull: ' Параметр контроля микроклиматом не может быть пустым',
    gatewayIdNotNull: 'ID для входа в систему не может быть пустым',
    pigHouseIDNotNull: 'ID свинофермы не может быть пустым',
    addEnvSet: 'Добавить【Конфигурация устройства контроля микроклиматом】',
    modEnvSet: 'Изменить【Конфигурация устройства контроля микроклиматом】',
    sureCancelEnvSet: 'Подтвердить удаление [Конфигурация устройства контроля микроклимата] №',
    dataItem: 'Данные?',
    sureExportEnvSet: 'Вы действительно хотите экспортировать все элементы данных? 【Конфигурация устройства контроля микроклиматом】',
    addHistoryData: "Добавить [Данные истории]",
    modHistoryData: "Изменить [Данные истории]",
    sureCancelHistoryData: 'Подтвердить удаление данных № [Данные истории]',
    sureExportHistoryData: 'Вы действительно хотите экспортировать все элементы данных? [Данные истории]',
    selectDate: ' Выбор даты',
    startDate: ' Дата начала',
    endDate: ' Дата окончания',
    period: 'Период',
    choosePeriod: 'Выбор периода',
    viewMonitoring: 'Просмотр мониторинга в реальном времени',
    parameterRealTimeIssued: 'Параметры передаются в режиме реального времени',
    parameterIssuance: 'Установить параметры контроллера',
    parameterName: 'имя параметра',
    parameterNameENG: 'имя параметра(Eng)',
    parameterNameRUS: 'имя параметра(RUS)',
    enterParameterName: 'Пожалуйста, введите название параметра',
    parameterType: 'Тип параметра',
    enterParameterType: 'Пожалуйста, введите тип параметра',
    point: 'точка',
    enterPoint: 'Пожалуйста, введите точку',
    parameterValue: 'значение параметра',
    enterParameterValue: 'Пожалуйста, введите значение параметра',
    nowStage: 'Текущая Этап',
    stage: 'Этап',
    enterStage: 'Вход в этап',
    stageOrdering: 'Установка этапа',
    enterStageOrdering: 'Пожалуйста, введите порядок этапов',
    batchEditing: 'Изменить все',
    refresh: 'обновить',
    requestTimedOut: 'Установка задержки! ! !',
    stageNotNull: "Этап не может быть пустым",
    stageOrderingNotNull: "Порядок этапов не может быть пустой.",
    parameterNameNotNull: "Имя параметра не может быть пустым",
    pointNotNull: "Точка не может быть пустой",
    parameterTypeNotNull: "Тип не может быть нулевым",
    addParameterDictionary: "Добавьте 【Параметры контроллера】",
    modifyParameterDictionary: "Измените 【Параметры контроллера】",
    sureDeleteParameterDictionary: 'Подтвердить удаление параметров',
    sureExportParameterDictionary: "Экспортировать все данные параметров?",
    addStageParameters: "Добавить [параметр этапа]",
    modifyStageParameters: "Изменить [параметры этапа]",
    sureDeleteStageParameters: 'Экспортировать все данные параметров',
    sureExportStageParameters: "Подтвердите экспорт параметра?",
    querying: 'запрос',
    atLeastOne: 'Сначала выберите хотя бы один',
  },
  electric: {
    metername: 'Название счетчика',
    enterMetername: 'Пожалуйста, введите название счетчика',
    meterNameNotNull: "Имя счетчика не может быть пустым",
    meterIdNotNull: "Идентификатор счетчика не может быть пустым.",
    energy: 'Электричество',
    enterEnergy: 'Пожалуйста, введите батарею',
    meterid: 'Идентификатор счетчика',
    meterWorkStatus: 'Рабочее состояние счетчика',
    choosetMeterWorkingStatus: 'Пожалуйста, выберите рабочее состояние счетчика',
    addMeterConfig: 'Добавить [информацию о конфигурации счетчика]',
    modMeterConfig: 'Изменить [Информацию о конфигурации счетчика]',
    sureCancelMeterConfig: 'Нужно ли подтверждать удаление номера [информации о конфигурации счетчика].',
    sureExportMeterConfig: 'Нужно ли подтверждать экспорт всех элементов данных [Информация о конфигурации счетчика]?',
    addMeterHistoryConfig: 'Добавить 【Данные истории счетчиков】',
    modMeterHistoryConfig: 'Изменить 【Исторические данные счетчика электроэнергии】',
    sureCancelMeterHistoryConfig: 'Подтвердить удаление [Исторические данные измерителя] Нет.',
    sureExportMeterHistoryConfig: 'Нужно ли подтверждать экспорт всех элементов данных [Исторические данные счетчика электроэнергии]?',
    materUse: 'использование батареи '
  },
  liaota: {
    time: "раз",
    totalFeedAmount: "Общий объем подачи",
    totalDischargeAmount: "Общий объем выпуска",
    numberOfFeedings: "Количество подач",
    numberOfDischarges: "Количество выпусков",
    floorDataStatistics: 'Статистика использования этажа',
    floorDataTable: 'Таблица данных использования этажа',
    addfloorTableData: 'Добавить данные использования этажа',
    modfloorTableData: 'Изменить данные использования этажа',
    sureCancelfloorTableData: 'Вы уверены, что хотите удалить данные использования этажа с номером',
    sureExportfloorTableData: 'Вы уверены, что хотите экспортировать все данные использования этажа?',
    addfloor: 'Добавить [этаж]',
    modfloor: 'Изменить [этаж]',
    sureCancelfloor: 'Нужно ли подтверждать удаление номера [этажа]',
    sureExportfloor: 'Нужно ли подтверждать экспорт всех элементов данных [этаж]?',
    inOutTable: 'Загрузка и выгрузка данных',
    weightTable: 'данные о весе в режиме реального времени',
    inOutLine: 'Добавьте и разгрузите кривую',
    weightLine: 'кривая веса в реальном времени',
    uptimeNotNull: 'Необходимо указать время загрузки.',
    floorIdNotNull: 'Идентификатор этажа не может быть пустым',
    floorNotNull: 'Название этажа не может быть пустым',
    inoroutNotNull: 'Добавление и выписка не могут быть пустыми',
    weightInOutNotNull: 'Вес не может быть пустым',
    floorId: 'идентификатор этажа',
    enterfloorId: 'Пожалуйста, введите идентификатор этажа',
    floor: 'Название этажа',
    enterFloor: 'Пожалуйста, введите название этажа',
    addLiaoTaInOut: 'Добавить【Данные загрузки и выгрузки башни】',
    modLiaoTaInOut: 'Изменить [Данные загрузки и выгрузки башни]',
    sureCancelLiaoTaInOut: 'Нужно ли подтверждать удаление [Данные загрузки и выгрузки башни] Нет.',
    sureExportLiaoTaInOut: 'Нужно ли подтверждать экспорт всех элементов данных [Данные загрузки и выгрузки башни]?',
    inFeed: "Количество резки",
    outFeed: 'Сумма разряда',
    inorout: 'Поведение при кормлении и выгрузке',
    choosetInorout: 'Пожалуйста, выберите, добавить или удалить',
    weightInOut: "Вес (кг)",
    feedtypeNotNull: 'Тип фида не может быть пустым',
    volumeNotNull: 'Емкость не может быть пустой',
    feedType: 'Тип фида',
    enterFeedType: 'Пожалуйста, введите тип фида',
    volume: 'вместимость',
    enterVolume: 'Пожалуйста, введите вместимость',
    online: "онлайн",
    offline: 'не в сети',
    max: "максимальное значение",
    min: 'минимум',
    all: 'общее',
    towerUse: 'данные о весе в режиме реального времени',
    currentWeight: "нынешний вес",
    upperLimit: 'верхний предел',
    lowerLimit: 'Нижний предел',
    notConnected: 'Нет соединения',
    towerName: "название башни",
    enterTowerName: 'Пожалуйста, введите название башни',
    gatewayId: 'идентификатор шлюза',
    enterGatewayId: 'Пожалуйста, введите идентификатор шлюза',
    pigHouseId: 'идентификатор свинофермы',
    enterPigHouseId: 'Пожалуйста, введите идентификатор свинарника',
    towerWorkingStatus: 'Рабочее состояние башни материалов',
    choosetTowerWorkingStatus: 'Пожалуйста, выберите рабочий статус материальной башни',
    weight: 'Вес в реальном времени (кг)',
    enterWeight: 'Пожалуйста, введите вес',
    alarmInformation: 'Информация о тревоге',
    enterAlarmInformation: 'Пожалуйста, введите информацию о тревоге',
    towerId: 'идентификатор бункера',
    enterTowerId: 'Пожалуйста, введите идентификатор бункера',
    towerNameNotNull: 'Название башни не может быть пустым',
    gatewayIdNotNull: 'Идентификатор шлюза не может быть пустым',
    pigHouseIDNotNull: 'Укажите идентификатор свинофермы.',
    towerIdNotNull: 'Идентификатор бункера не может быть пустым.',
    addTowerConfig: 'Добавить【Информация о конфигурации материальной башни】',
    modTowerConfig: 'Изменить [Информацию о конфигурации материальной башни]',
    sureCancelTowerConfig: 'Подтверждать ли удаление [информации о конфигурации мачты материала] Нет.',
    sureExportTowerConfig: 'Нужно ли подтверждать экспорт всех элементов данных [Информация о конфигурации башни материала]?',
    dataItem: 'элемент данных?',
    addLiaoTaHistory: 'Добавить【Исторические данные материальной башни】',
    modLiaoTaHistory: 'Изменить【Исторические данные материальной башни】',
    sureCancelLiaoTaHistory: 'Нужно ли подтверждать удаление 【Исторические данные материальной башни】№.',
    sureExportLiaoTaHistory: 'Нужно ли подтверждать экспорт всех элементов данных [Исторические данные материальной башни]?',
    selectDate: 'выберите дату',
    startDate: 'Дата начала',
    endDate: 'Дата окончания',
    period: 'период',
    choosePeriod: 'Пожалуйста, выберите период времени',
  },
  content: {
    main: "вот содержание"
  },
  menu: {
    account: "учетная запись",
    password: "пароль",
    verificationCode: "проверочный код",
    rememberPassword: "напомнить пароль",
    logIn: "Авторизоваться",
    loggingIn: "Вход в систему...",
    register: "регистр",
    registrationCode: "Регистрационный код",
    enterRegistrationCode: "Пожалуйста, введите регистрационный код",
    registrationCodeNotNull: "Регистрационный код не может быть пустым",
    usernameNotNull: "Имя пользователя не может быть пустым",
    passwordNotNull: "пароль не может быть пустым",
    verificationCodeNotNull: "необходимо заполнить проверочный код",
    smartPigRaisingCloudPlatform: 'Авторизоваться',
    remainingDays: "Оставшиеся дни регистрации",
    day: "небо",
    currentFarm: "текущая ферма",
    layoutSize: "размер макета",
    switchFarms: "поменять ферму",
    switchLanguage: "смена языка",
    zh: "Китайский",
    en: "Английский",
    ru: "Русский",
    personalCenter: 'персональный центр',
    layoutSettings: "Настройки макета",
    signOut: "выход",
    themeStyleSettings: 'Настройки стиля темы',
    themeColor: "цвет темы",
    systemLayoutConfiguration: "Конфигурация схемы системы",
    turnOn: 'включить',
    fixed: "исправлено",
    show: "шоу",
    switchCurrentFarm: "Изменить текущую ферму",
    selectFarmName: "Выберите текущую ферму",
    farmNameNotNull: "Имя фермы не может быть пустым",
    nowFarmNameNotNull: 'Текущая ферма не должна быть пустой, выберите текущую ферму и подтвердите!',
    addPigFarm: "Добавьте 【Свиноферму】",
    increasePigFarm: "увеличить свиноферму",
    farmName: 'название фермы',
    enterFarmName: 'Пожалуйста, введите название фермы',
    hint: "Подсказка",
    logOut: 'Вы уверены, что хотите выйти и выйти из системы?',
    personalInformation: 'персональная информация',
    userName: "имя пользователя",
    phoneNumber: 'телефонный номер',
    userMailbox: 'Почтовый ящик пользователя',
    department: "Отдел",
    ownRole: 'Должность',
    dateCreated: 'Дата создания',
    basicInformation: "основная информация",
    changePassword: 'изменить пароль',
    userNickname: 'Ник пользователя',
    gender: "Пол",
    male: 'мужчина',
    female: 'женский',
    usernameCannotBeEmpty: "Имя пользователя не может быть пустым",
    phoneNumberCannotBeBlank: 'Укажите номер телефона.',
    emailAddressCannotBeEmpty: 'Адрес электронной почты не может быть пустым',
    enterCorrectEmail: 'Пожалуйста, введите правильный адрес электронной почты',
    enterCorrectPhone: 'Пожалуйста, введите правильный номер телефона',
    enterOldPassword: 'Пожалуйста, введите старый пароль',
    enterNewPassword: 'Пожалуйста, введите новый пароль',
    enterConfirmPassword: 'Пожалуйста, подтвердите свой пароль',
    sureTwoEnterMatch: 'Два введенных пароля не совпадают',
    lengthBetween: 'от 6 до 20 символов',
    clickUploadAvatar: 'Нажмите, чтобы загрузить аватар',
    choose: 'выберите',
    modifyAvatar: 'Изменить аватар',
    fileFormatError: 'Неверный формат файла, пожалуйста, загрузите тип изображения, например: JPG, файлы PNG с суффиксом.',
    oldPassword: "Старый пароль",
    newPassword: 'Новый пароль',
    confirmPassword: 'Подтвердить Пароль',
    oldPasswordCannotBeEmpty: "Старый пароль не может быть пустым",
    newPasswordCannotBeEmpty: 'Новый пароль не может быть пустым',
    confirmPasswordCannotBeEmpty: 'подтвердить пароль не может быть пустым',
    address: "адрес",
    scale: 'шкала',
    type: 'тип',
    state: "Состояние контроллера",
    breedingPig: 'Свиноматка',
    sow: "сеять",
    columns: 'Столбцы',
    tower: "башня",
    precisionFeeding: 'прецизионная подача',
    offline: 'не в сети',
    frontPage: "Главная страница",
    environmentalControl: "Контроль окружающей среды",
    pigHouseConfiguration: "Конфигурация свинарника",
    pigHouseRealtimeMonitoring: "Мониторинг свинарника в режиме реального времени",
    RingControlParameterAdjustment: "Настройка параметров управления кольцом",
    envConDeviceConfiguration: "Конфигурация устройства контроля окружающей среды",
    envConHistoricalData: "Архивные данные окружающей среды",
    envConSummaryReport: "Сводный отчет о контроле окружающей среды",
    stageParameterConfiguration: "Конфигурация параметров этапа",
    singleParameterConfiguration: "Конфигурация с одним параметром",
    videoRealtimeMonitoring: "Видеомониторинг в реальном времени",
    electricityMeter: "Электрический счетчик",
    realtimeMonitoringElectricityMeters: "Мониторинг показаний электросчётчика",
    meterDeviceConfiguration: "Конфигурация устройства электросчётчика",
    meterHistoryData: "Сбор данных о потреблении электроэнергии",
    meterSummaryReport: "Сводка тенденций потребления электроэнергии",
    waterSummaryReport: "Сводка тенденций потребления воды",
    realtimeMonitoringWater: "Мониторинг показаний водяного счётчика",
    waterHistoryData: "Сбор данных о потреблении воды",
    tower: "башня",
    realtimeMonitoringMaterialTower: "Мониторинг материальной башни в режиме реального времени",
    materialTowerEquipmentConfiguration: "Конфигурация оборудования башни материалов",
    materialTowerHistoricalData: "Исторические данные материальной башни",
    towerSummaryReport: "Сводный отчет башни",
    boarMeasure: "Определение свиноматок",
    breedPigdata: "Информация о свиньях-производителях",
    breedOverview: "Обзор свинарника",
    breedMeasureday: "Отчет по индивидуальным измерениям",
    breedDayReport: "Ежедневный отчет о замерах",
    breedMeasuredaygather: "Сводный отчет",
    breedSocialAnimal: "Отчет по коллективным измерениям",
    breedControl: "Настройка контроллера",
    breedControlV2: "Настройка контроллера V2",
    breedMeasure: "Данные по потреблению корма",
    breedFeeddata: "Данные по подаче корма",

    columnSystem: "育肥分栏模块",
    columnSystemOverview: "猪场总览",
    columnSystemDailyweightday: "日汇总",
    columnSystemDailyWeight: "分栏称重记录",
    columnSystemPigdatadaily: "分栏猪管理",
    columnSystemWeightdistribution: "体重分布",
    columnSystemTransactionprocessing: "事务处理",
    columnSystemControldaily: "设备管理",
    columnSystemPrediction: "上市预测",
    columnSystemStationGrowthReport: "分栏站生长报告",
    columnSystemIndividualGrowthReport: "个体生长报告",
    columnSystemZeroprediction: '零耳牌上市预测',
    columnSystemSametime: "同时开启",
    columnSystemAlarm: "报警记录",
  },
  boarMeasure: {
    dragFile: "Перетащите файл сюда",
    clickUpload: "Нажмите для загрузки",
    hintOnly: "Подсказка: Для импорта разрешены только файлы форматов 'xls' или 'xlsx'! Импортируемый файл должен быть обновленной версией на основе экспортированного файла, не изменяйте колонку 'Уникальный идентификатор'!",
    batchUpdateInfo: "Пакетное обновление и импорт информации о свиньях",
    changeMrfid: "Замена электронного ушного бирка",
    newMrfid: "Изменённый электронный ушной бирка",
    zeroQuery: "Запрос бренда Линь Эр",
    pleaseEnterColumn: "Пожалуйста, введите номер колонки",
    pleaseEnterColumnFirst: "Сначала введите номер столбца, затем введите номер отсутствующего уха или номер электронного уха",
    noRecords: "Записи для свиньи не найдены",
    noData: "Данные не найдены",
    feedingInterval: "Время между кормлениями (сек)",
    inFeed: "Количество подачи корма",
    feedingDate: "Дата кормления",
    feedTime: "Время подачи корма",
    unfed: "Неподкормленные",
    fed: "Подкормленные",
    zeroearDistribution: "Распределение нулевого номера уха",
    unpicked: "Не выбрано",
    timeout: "Время запроса истекло!!!",
    successfulLaunch: "Успешно отправлено, результат изменений. Обновите через 5 секунд",
    timingSuccess: "Синхронизация времени выполнена успешно",
    pleaseEnterCorrectRange: "Пожалуйста, введите правильный диапазон",
    inactive: "Неактивный",
    active: "Активный",
    stop: "Остановить",
    normal: "Нормальный",
    pleaseInputPigHouseName: "Пожалуйста, введите название свинарника",
    pleaseInput: "Пожалуйста, введите",
    pleaseChoose: "Пожалуйста, выберите",
    mid: "Номер отсутствующего уха",
    mrfid: "Номер электронного уха",
    mvariety: "Порода",
    bgender: "Пол",
    dbirthdate: "Дата рождения",
    mdorm: "Номер свинарника",
    nindex: "Номер строки",
    mname: "Станция измерения",
    naddress: "Адрес оборудования",
    dstartdate: "Дата начала измерения",
    denddate: "Дата окончания измерения",
    nweightStart: "Начальный вес (кг)",
    nweightEnd: "Конечный вес (кг)",
    nweight: "Вес свиньи (кг)",
    ntype: "Статус измерения",
    remarks: "Примечания",
    mensurationEnd: "Измерение завершено",
    mensurationNow: "Измерение в процессе",
    addPigdata: "Добавить информацию о свинье",
    updatePigdata: "Изменить информацию о свинье",
    sureCancelPigdata: "Вы уверены, что хотите удалить информацию о свинье с номером",
    dataItem: "?",
    cannotbeRecovered: "После удаления восстановление будет невозможно. Если вы хотите сохранить данные, измените статус измерения!",
    sureExportPigdata: "Вы уверены, что хотите экспортировать все данные о свиньях?",
    notNull: "не может быть пустым",
    sureMensurationEnd: "Вы уверены, что хотите завершить измерение?",
    mensurationEndSuccess: "Измерение успешно завершено",
    lessThanSomeGrams: "Количество свиней, съевших менее",
    pigIs: 'г корма вчера:',
    heads: "голов(ы)",
    midIs: "Номер отсутствующего уха:",
    nIngestionSToday: "Количество корма, съеденное сегодня (г)",
    nIngestionSLastday: "Количество корма, съеденное вчера (г)",
    nweightLastday: "Вес вчера (кг)",
    basicInformation: "Основная информация",
    measureDays: "Дни измерения",
    recordDays: 'дни записи',
    allNIngestionS: 'общее потребление(кг)',
    weightGrow: 'общий прирост веса(кг)',
    liaoRouBi: 'соотношение корма к мясу',
    dailyData: 'ежедневные данные',
    ningestion: 'потребление',
    weight: 'вес',
    dailyGainWeight: 'ежедневный прирост веса',
    dateOfDetermination: 'дата определения',
    ningestions: 'общее потребление в день(г)',
    weightKg: 'вес(кг)',
    nfeednum: 'количество кормлений в день',
    nseconds: 'общее время приема пищи в день(с)',
    nsecond: 'общее время приема пищи в день(ч)',
    ningestionG: 'потребление(г)',
    ningestionKG: 'потребление(кг)',
    dailyGainWeightKg: 'ежедневный прирост веса(кг)',
    weightMid: 'вес(среднее значение)(кг)',
    addMeasureday: "Добавить информацию о【индивидуальном измерении】",
    updateMeasureday: "Изменить информацию о【индивидуальном измерении】",
    sureCancelMeasureday: 'Вы уверены, что хотите удалить отчет о【индивидуальном измерении】 с идентификатором',
    sureExportMeasureday: 'Вы уверены, что хотите экспортировать все данные отчетов о【индивидуальном измерении】?',
    addDayReport: "Добавить отчет об【измерении за день】",
    updateDayReport: "Изменить отчет об【измерении за день】",
    sureCancelDayReport: 'Вы уверены, что хотите удалить отчет об【измерении за день】 с идентификатором',
    sureExportDayReport: 'Вы уверены, что хотите экспортировать все данные отчетов об【измерении за день】?',
    sureCancelGatherReport: 'Вы уверены, что хотите удалить отчет об【измерении за день】 с идентификатором',
    sureExportGatherReport: 'Вы уверены, что хотите экспортировать все данные отчетов о【Общем отчете】?',
    dataList: 'список данных',
    feedConsumptionChart: 'график потребления корма',
    averageWeightGraph: 'график среднего веса',
    dataAggregation: 'агрегация данных',
    measurePigs: 'измерение свиней',
    allNIngestionG: 'общее потребление(г)',
    intakesNumber: 'количество приемов пищи',
    intakesTimeH: 'время приема пищи(ч)',
    intakesStartTime: 'Время начала кормления',
    intakesEndTime: 'Время окончания кормления',
    averageWeightKg: 'средний вес(кг)',
    feedConsumptionG: 'потребление корма(г)',
    measureDaysT: 'дни измерений(дни)',
    averageDailyFeedIntake: 'средний дневной прием корма(кг)',
    allIntakesTimeS: "общее время приема пищи(с)",
    allIntakesTimeH: "общее время приема пищи(ч)",
    averageDailyFeedIntakeG: 'средний дневной прием корма(г)',
    feedConsumption: 'потребление корма',
    startAverageWeight: 'начальный средний вес(кг)',
    endAverageWeight: 'конечный средний вес(кг)',
    averageGrowth: 'средний прирост веса(кг)',
    averageDailyGrowth: 'средний дневной прирост веса(кг)',
    viewIndividualFeedIntakeData: 'просмотр данных о【индивидуальном потреблении корма】',
    manualSummary: 'ручная сводка',
    manualSummaryDate: 'дата ручной сводки',
    range: 'диапазон',
    columnFrom: 'колонка от',
    columnTo: 'колонка до',
    totalFeedIntake: 'общее потребление корма',
    totalWeightGain: 'общий прирост веса',
    productionPerformance: 'производственная эффективность',
    startDate: 'дата начала',
    endDate: 'дата окончания',
    startWeight: 'начальный вес(кг)',
    endWeight: 'конечный вес(кг)',
    nearWeight: 'Последний вес(кг)',
    weightGain: 'прирост веса(кг)',
    sureExportAllData: 'Вы уверены, что хотите экспортировать все данные?',
    parameterDistribution: 'распределение параметров',
    topologyNodeType: 'тип узла топологии',
    gatewayID: 'идентификатор шлюза',
    naddressup: 'адрес для загрузки',
    nserial: 'серийный номер устройства',
    nstatus: 'статус устройства',
    nversion: 'версия программного обеспечения',
    ncorrect: 'значение калибровки кормушки',
    ncorrectmin: 'минимальное значение калибровки кормушки',
    ncorrectmax: 'максимальное значение калибровки кормушки',
    DPCkg: 'DPC(кг)',
    DPCG: 'DPC(г)',
    DPC: 'DPC',
    DPCmin: 'минимальное DPC',
    DPCmax: 'максимальное DPC',
    nsurpluskg: 'минимальное количество оставшегося корма(кг)',
    nsurplus: 'минимальное количество оставшегося корма',
    npulse: 'значение импульса подачи корма',
    nindivkg: 'значение калибровки индивидуальных весов(кг)',
    nindiv: 'значение калибровки индивидуальных весов',
    nindivmin: 'минимальное значение калибровки индивидуальных весов',
    nindivmax: 'максимальное значение калибровки индивидуальных весов',
    nindivnull: 'пустой вес индивидуальных весов',
    ncorrectnull: 'пустой вес кормушки',
    addControl: "Добавить настройки【контроллера】",
    updateControl: "Изменить настройки【контроллера】",
    sureCancelControl: 'Вы уверены, что хотите удалить настройки контроллера с идентификатором',
    sureExportControl: 'Вы уверены, что хотите экспортировать все данные настроек контроллера?',
    onlyShowControl: 'показать только контроллер',
    refresh: 'обновить',
    saveDetails: 'измените один параметр сразу, нажмите сохранить√ после завершения изменений. Минимальный интервал между изменениями - 5 секунд. После изменения параметра подождите 5 секунд перед обновлением',
    addFeedData: "Добавить данные о【подаче корма】",
    updateFeedData: "Изменить данные о【подаче корма】",
    sureCancelFeedData: 'Вы уверены, что хотите удалить данные о【подаче корма】 с идентификатором',
    sureExportFeedData: 'Вы уверены, что хотите экспортировать все данные о【подаче корма】?',
    intakesTime: 'время приема пищи',
    intakesTimesS: 'продолжительность приема пищи(с)',
    nfriweight: 'начальный вес кормушки(г)',
    nsecweight: 'конечный вес кормушки(г)',
    tankInitialWeight: 'начальный вес бака',
    tankEndWeight: 'конечный вес бака',
    nnum: 'количество подачи',
    addMeasure: "Добавить данные о【потреблении корма】",
    updateMeasure: "Изменить данные о【потреблении корма】",
    sureCancelMeasure: 'Вы уверены, что хотите удалить данные о【потреблении корма】 с идентификатором',
    sureExportMeasure: 'Вы уверены, что хотите экспортировать все данные о【потреблении корма】?',
    sureExportBreedSocialAnimal: 'Вы уверены, что хотите экспортировать все данные отчетов о【социальном измерении стада】?',
    tizhongfanwei: 'Weight range',
  },
  columnSystem: {
    remark: '备注',
    columnMode: "分栏中",
    outLan: "出栏",
    sureOutLan: "是否确认出栏?",
    outLanSuccess: "出栏操作成功",
    nindex: "栏号",
    mname: "分栏站",
    dalarmdate: "报警日期",
    nalarmid: "报警ID",
    mmemo: "故障类型",
    nstate: "消警记录",
    notNull: '不能为空',
    addAlarm: '添加【设备报警记录】',
    updateAlarm: '修改【设备报警记录】',
    sureCancelAlarm: '是否确认删除【设备报警记录】编号为"',
    sureExportAlarm: '是否确认导出所有【设备报警记录】数据项?',

    mdorm: "猪舍号",
    pleaseEnterDeptName: "请输入猪舍名称",
    onlyShowControl: '只显示控制器',
    topologyNodeType: '拓扑节点类型',
    naddress: '设备地址',
    naddressup: '主机地址',
    nserial: '设备序列号',
    nstatus: '设备状态',
    nversion: '软件版本',
    nworktype: '工作模式',
    ndirect: '开门方向',
    nsceen: '上市筛选',
    ntrend: '挑选筛选',
    ntimeopen: '分栏门开启时间',
    ntimereopen: '入口门重开时间',
    ntimedelay: '入口门延迟时间',
    nweighttime: '称重时间',
    npignums: '存栏量',
    basicInform: '基本信息',
    switchid: '网关id',
    mmemo: '备注',
    timeParameter: '时间参数',
    pleaseEnterNtimeopen: '请输入分栏门开启时间(0.2-5秒，默认0.5秒)',
    ntimeclose: '分栏门关闭时间',
    pleaseEnterNtimeclose: '请输入分栏门关闭时间(0.2-5秒，默认0.5秒)',
    pleaseEnterNtimereopen: '请输入入口门重开时间(0.2-5秒，默认0.5秒)',
    pleaseEnterNtimedelay: '请输入入口门延迟时间(0-10秒默认1秒)',
    nweightdelay: '称重延时时间',
    pleaseEnterNweightdelay: '请输入称重延时时间(0-10秒，默认2秒)',
    pleaseEnterNweighttime: '请输入称重时间(0-60秒，默认30秒)',
    calibrationAndWeight: '校准及重量',
    nindivnull: '分栏秤空重',
    pleaseEnterNindivnull: '请输入分栏秤空重(单位0.1公斤，0-200)',
    nindivweight: '分栏秤校准重量',
    pleaseEnterNindivweight: '请输入分栏秤校准重量(单位公斤 10-100)',
    nindiv: '分栏秤校准值',
    pleaseEnterNindiv: '请输入分栏秤校准值(60-120)',
    nweightstart: '称重触发重量',
    pleaseEnterNweightstart: '请输入称重触发重量(5-200,默认10公斤)',
    columnProperties: '分栏属性',
    nworktypeValue: '分栏秤工作模式',
    ndirectValue: '分栏门开门方向',
    nsceenValue: '上市分栏筛选值',
    pleaseEnterNsceenValue: '请输入上市分栏筛选值(1-255默认10)',
    ntrendValue: '挑选分栏筛选值',
    columns: '分栏',
    ngroupweight1: '轻群体重',
    ngroupweight2: '重群体重',
    ncolumnpct: '轻群分栏重量百分比',
    ncolumnweight: '重群分栏重量百分比',
    fatteningClusters: '育肥群组',
    ngroupweight3: '上市体重',
    nmarketweight2: '挑选体重上限',
    nmarketweight1: '挑选体重下限',
    nlightpct: '轻群百分比',
    nmidweight: '中群百分比',
    nheavypct: '重群百分比',
    ncorrect: '料槽校准值',
    nsurplus: '饲料最低剩余量',
    npulse: '投料脉冲值',
    nindiv: '个体秤校准值',
    nindivnull: '个体秤空重',
    ncorrectnull: '料槽空重',
    pleaseEnterCorrectRange: "请输入正确的范围",
    timingSuccess: "校时成功",
    addControl: '添加【控制器配置】',
    parameterDistribution: '参数下发',
    requestTimedOut: '请求超时！！！',
    updateControl: '修改【控制器配置】',
    sureCancelControl: '是否确认删除【控制器配置】编号为"',
    sureExportControl: '是否确认导出所有【设备报警记录】数据项是否确认导出所有【控制器配置】数据项?',

    mid: '耳缺号',
    mrfid: "电子耳牌",
    weightDate: "称重日期",
    dataType: '数据类型',
    weight: "体重(kg)",
    ntemp: "温度",
    addDailyWeight: '添加【分栏称重记录】',
    updateDailyWeight: '修改【分栏称重记录】',
    sureCancelDailyWeight: '是否确认删除【分栏称重记录】编号为"',
    sureExportDailyWeight: '是否确认导出所有【分栏称重记录】数据项?',

    queryDate: "查询日期",
    groupSet: '分组设置',
    percentage: '百分比',
    percentageAnd: '百分比(%)',
    visits: '访问量',
    dailySummaryData: '日汇总数据',
    pleaseEnterWeight: '请输入体重：单位0.1公斤',
    Group1: '小于该值的为Group1',
    Group3: '大于该值的为Group3',
    cohort: '存栏量',
    total: '总量',
    pleaseFirstEnterWeightDate: '请先输入称重日期',
    pleaseFirstEnterColumn: '请先输入栏号和日期范围',
    addDailyWeightDay: '添加【日汇总】',
    updateDailyWeightDay: '修改【日汇总】',
    sureCancelDailyWeightDay: '是否确认删除【日汇总】编号为"',
    sureExportDailyWeightDay: '是否确认导出所有【日汇总】数据项?',
    titleGroupSet: '分组体重设置',

    measureDays: '测定天数',
    lowWeight: '最低体重',
    heightWeight: '最高体重',
    dailyGainWeight: '日增重',
    dailyData: '日数据',
    dateOfDetermination: '测定日期',
    weightTab: "体重",
    noPigsUnderThisPen: '该栏下没有分栏猪',
    addIndividualGrowthReport: '添加【个体生长报告】',
    updateIndividualGrowthReport: '修改【个体生长报告】',
    sureCancelIndividualGrowthReport: '是否确认删除【个体生长报告】编号为"',
    sureExportIndividualGrowthReport: '是否确认导出所有【个体生长报告】数据项?',
    noRecords: "的猪没有找到记录",
    pleaseEnterColumnFirst: "请先输入栏号，再输入耳缺号或者电子耳牌号",

    nowColumnCohort: "当前分栏站的存栏量为",
    lessThanFeedNum: '昨天采食次数低于',
    pigIs: '次的猪有',
    feedTimesToday: '今日进食次数',
    feedTimesYest: '昨日进食次数',
    nWeightToday: '今日称重(kg)',
    nWeightYest: '昨日称重(kg)',

    ntype: "分栏状态",
    ddateinkjet: "喷墨日期",
    nearWeight: '最近体重(kg)',
    transaction: "事务处理",
    mreasoninkjet: "处理原因",
    ninkjet: "是否喷墨",
    ndepart: "是否分离",
    addPigdataDaily: '添加【分栏猪数据】',
    updatePigdataDaily: '修改【分栏猪数据】',
    sureCancelPigdataDaily: '是否确认删除【分栏猪数据】编号为"',
    sureExportPigdataDaily: '是否确认导出所有【分栏猪数据】数据项?',
    transactionAddSuccess: "事务处理新增成功",

    targetWeight: '目标出栏体重',
    targetWeightLow: '最小体重',
    targetWeightHigh: '最大体重',
    filteringScope: '筛选范围',
    listingDate: '上市日',
    intervalDays: '间隔天数',
    growthCalculations: '生长计算依据',
    adgRelyDays: 'ADG值基于过去天数',
    isAdgStable: 'ADG固定值',
    digital: '数据',
    sure: '确认',
    targerDay: '上市日期',
    lowWeightNum: '较轻',
    targetNum: '上市',
    highWeightNum: '较重',
    lowWeightPct: '较轻(%)',
    targetWeightPct: '上市(%)',
    highWeightPct: '较重(%)',

    nsametimeFrom: '开始日期',
    nsametimeTo: '结束日期',
    recordList: '记录列表',
    nsametime: '开门时间',
    ntimes: '延续时间',
    accruedTime: '累积时间',
    addSameTime: '添加【同时开启】',
    updateSameTime: '修改【同时开启】',
    sureCancelSameTime: '是否确认删除【同时开启】编号为"',
    sureExportSameTime: '是否确认导出所有【同时开启】数据项?',

    date: '日期',
    dataList: '数据列表',
    midWeight: '中位值',
    lightWeight: '轻群',
    highWeight: '重群',
    report: '报告',
    firstInputColumnAndDate: '请先输入栏号和日期',
    midWeightKG: '中位值(kg)',
    lightWeightKG: '轻群(kg)',
    highWeightKG: '重群(kg)',

    launchDate: '启动日期',
    nexecdate: '处理日期',
    addTransactionProcessing: '添加【事务处理】',
    updateTransactionProcessing: '修改【事务处理】',
    sureCancelTransactionProcessing: '是否确认删除【事务处理】编号为"',
    sureExportTransactionProcessing: '是否确认导出所有【事务处理】数据项?',

    WeightDistributionData: '体重分布数据',
    enterWeighingDateFirst: '请先输入称重日期',
    addWeightdistribution: '添加【体重分布】',
    updateWeightdistribution: '修改【体重分布】',
    sureCancelWeightdistribution: '是否确认删除【体重分布】编号为"',
    sureExportWeightdistribution: '是否确认导出所有【体重分布】数据项?',
    noData: "未查到数据",
    enterDate: "入栏日期",
    enterWeight: "入栏体重",

    cunlanNum: '存栏数量',
    zeroyccunlan: '零耳牌预测存栏量',
    columnSystemZeroprediction: '零耳牌上市预测',
    targetWeight2: '目标体重',
    gjdays: '预计天数',
    gjdate: '预计上市日期',
  }
}
export default ru;
