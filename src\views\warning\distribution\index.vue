<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('warning.username')" prop="username">
        <el-input
          v-model="queryParams.username"
          :placeholder="$t('common.pleaseInput') + $t('warning.username')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="distributionList">
      <el-table-column
        :label="$t('warning.username')"
        align="center"
        prop="userName"
      />
      <el-table-column
        :label="$t('warning.nodeAssignment')"
        align="center"
        prop="nodeNames"
      >
        <template slot-scope="scope">
          <!-- 编辑状态 -->
          <el-select
            v-if="scope.row.isEditing"
            v-model="scope.row.selectedNodes"
            multiple
            filterable
            :placeholder="$t('warning.selectNodes')"
            style="width: 100%"
            :filter-method="filterNodes"
            @keydown.native.enter.prevent="handleSelectEnter($event)"
          >
            <!-- 操作选项组 -->
            <el-option-group
              v-if="currentSearchQuery && filteredNodes.length > 0"
              label="批量操作"
            >
              <el-option
                :key="'select-all'"
                :label="`全选搜索结果 (${filteredNodes.length}项)`"
                :value="null"
                :disabled="true"
                @click.native.stop="selectAllFilteredNodes(scope.row, $event)"
                style="
                  background-color: #f5f7fa;
                  font-weight: bold;
                  color: #409eff;
                  cursor: pointer;
                "
              >
                <i class="el-icon-check"></i> 全选搜索结果 ({{
                  filteredNodes.length
                }}项)
              </el-option>
              <el-option
                :key="'clear-all'"
                :label="清空所有选择"
                :value="null"
                :disabled="true"
                @click.native.stop="clearAllSelectedNodes(scope.row, $event)"
                style="
                  background-color: #fef0f0;
                  font-weight: bold;
                  color: #f56c6c;
                  cursor: pointer;
                "
              >
                <i class="el-icon-delete"></i> 清空所有选择
              </el-option>
            </el-option-group>
            <el-option
              v-for="node in filteredNodes"
              :key="node.id"
              :label="node.name"
              :value="node.id"
            >
            </el-option>
          </el-select>
          <!-- 显示状态 -->
          <span v-else>
            <el-tag
              v-for="nodeName in scope.row.nodeNames"
              :key="nodeName"
              size="small"
              style="margin-right: 5px; margin-bottom: 5px"
            >
              {{ nodeName }}
            </el-tag>
            <span
              v-if="!scope.row.nodeNames || scope.row.nodeNames.length === 0"
            >
              {{ $t("warning.unassigned") }}
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template slot-scope="scope">
          <!-- 编辑状态 -->
          <div v-if="scope.row.isEditing">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleSave(scope.row)"
              >{{ $t("common.save") }}</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-close"
              @click="handleCancel(scope.row)"
              >{{ $t("common.cancel") }}</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-cancel"
              @click="handleDelete(scope.row)"
              >{{ $t("common.delete") }}</el-button
            >
          </div>
          <!-- 显示状态 -->
          <div v-else>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              >{{ $t("common.update") }}</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listDistribution,
  updateDistribution,
  delDistribution,
  listAlarmSimple,
} from "@/api/warning/distribution";

export default {
  name: "Distribution",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分配列表数据
      distributionList: [],
      // 监控节点列表
      nodeList: [],
      // 过滤后的节点列表
      filteredNodes: [],
      // 当前搜索关键词
      currentSearchQuery: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: null,
      },
    };
  },
  created() {
    this.getList();
    this.getNodeList();
  },
  methods: {
    /** 查询分配列表 */
    getList() {
      this.loading = true;
      this.queryParams.faId = this.$store.state.settings.nowPigFarm;
      listDistribution(this.queryParams).then((response) => {
        console.log("Distribution API response:", response);
        this.distributionList = response.rows.map((item) => {
          console.log("Processing item:", item);

          // 处理eaList，提取节点ID，确保ID是数字类型
          let nodeIds = [];
          if (item.eaList && Array.isArray(item.eaList)) {
            nodeIds = item.eaList
              .map((ea) => {
                // 处理不同的数据格式
                let id = ea.eaId || ea.id || ea;
                // 确保ID是数字类型
                return typeof id === "string" ? parseInt(id) : id;
              })
              .filter((id) => !isNaN(id)); // 过滤掉无效的ID
          }

          console.log("Extracted nodeIds:", nodeIds);

          return {
            ...item,
            isEditing: false,
            selectedNodes: nodeIds,
            originalSelectedNodes: [...nodeIds],
            // 先设置为空，等nodeList加载完成后再更新
            nodeNames: [],
          };
        });
        this.total = response.total;
        this.loading = false;

        // 如果nodeList已经加载完成，立即更新nodeNames
        if (this.nodeList.length > 0) {
          this.updateNodeNames();
        }
      });
    },
    /** 获取监控节点列表 */
    getNodeList() {
      const params = {
        faId: this.$store.state.settings.nowPigFarm,
      };
      listAlarmSimple(params).then((response) => {
        console.log("NodeList API response:", response);
        this.nodeList = response.data.map((item) => ({
          id: typeof item.id === "string" ? parseInt(item.id) : item.id, // 确保ID是数字类型
          name: item.name,
        }));
        console.log("Processed nodeList:", this.nodeList);
        this.filteredNodes = [...this.nodeList];

        // 如果distributionList已经加载完成，立即更新nodeNames
        if (this.distributionList.length > 0) {
          this.updateNodeNames();
        }
      });
    },
    /** 更新节点名称显示 */
    updateNodeNames() {
      console.log("Updating node names...");
      console.log("distributionList:", this.distributionList);
      console.log("nodeList:", this.nodeList);

      this.distributionList.forEach((item, index) => {
        console.log(`Processing item ${index}:`, item);
        if (item.selectedNodes && item.selectedNodes.length > 0) {
          item.nodeNames = item.selectedNodes
            .map((nodeId) => {
              console.log(
                `Looking for nodeId: ${nodeId} (type: ${typeof nodeId})`
              );
              const node = this.nodeList.find((n) => {
                console.log(
                  `Comparing with node.id: ${n.id} (type: ${typeof n.id})`
                );
                return n.id === nodeId;
              });
              console.log(`Found node:`, node);
              return node ? node.name : null;
            })
            .filter((name) => name); // 过滤掉null值
          console.log(`Final nodeNames for item ${index}:`, item.nodeNames);
        } else {
          item.nodeNames = [];
        }
      });
    },
    /** 过滤节点 */
    filterNodes(query) {
      this.currentSearchQuery = query;
      if (query) {
        this.filteredNodes = this.nodeList.filter((node) =>
          node.name.toLowerCase().includes(query.toLowerCase())
        );
      } else {
        this.filteredNodes = [...this.nodeList];
      }
    },
    /** 处理下拉框回车事件 */
    handleSelectEnter(event) {
      // 阻止默认的回车行为
      event.preventDefault();
      event.stopPropagation();

      // 如果有搜索关键词且有搜索结果，提示用户使用批量操作
      if (this.currentSearchQuery && this.filteredNodes.length > 0) {
        this.$message.info('请点击"全选搜索结果"按钮进行批量选择');
      }
    },
    /** 全选搜索结果 */
    selectAllFilteredNodes(row, event) {
      // 防止事件冒泡
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      // 获取当前搜索结果中未选中的节点ID
      const filteredNodeIds = this.filteredNodes.map((node) => node.id);
      const unselectedIds = filteredNodeIds.filter(
        (id) => !row.selectedNodes.includes(id)
      );

      if (unselectedIds.length === 0) {
        this.$message.info("搜索结果中的所有节点都已选择");
        return;
      }

      // 将未选中的节点添加到已选择列表中
      row.selectedNodes = [...row.selectedNodes, ...unselectedIds];

      // 提示用户
      this.$message.success(`已选择 ${unselectedIds.length} 个搜索结果`);

      // 不清空搜索关键词，保持当前搜索状态
      // this.currentSearchQuery = "";
      // this.filteredNodes = [...this.nodeList];
    },
    /** 清空所有选择 */
    clearAllSelectedNodes(row, event) {
      // 防止事件冒泡
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      if (row.selectedNodes.length === 0) {
        this.$message.info("当前没有选择任何节点");
        return;
      }

      const selectedCount = row.selectedNodes.length;

      // 确认删除
      this.$confirm(
        `确定要清空所有已选择的 ${selectedCount} 个节点吗？`,
        "确认操作",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          // 清空所有选择
          row.selectedNodes = [];
          this.$message.success(`已清空 ${selectedCount} 个选择项`);
        })
        .catch(() => {
          // 用户取消操作
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 编辑按钮操作 */
    handleEdit(row) {
      row.isEditing = true;
      row.selectedNodes = [...row.originalSelectedNodes];
      this.filteredNodes = [...this.nodeList];
    },
    /** 取消按钮操作 */
    handleCancel(row) {
      row.isEditing = false;
      row.selectedNodes = [...row.originalSelectedNodes];
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm("确认删除分配项", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delDistribution(row);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 保存按钮操作 */
    handleSave(row) {
      console.log("row", row);
      //   "eaList": [
      //     {
      //         "eaId": 0,
      //         "eaName": "string"
      //     }
      // ],
      const params = {
        id: row.id,
        userId: row.userId,
        userName: row.userName,
        // eaList: row.selectedNodes,
        eaList: row.selectedNodes.map((eaId) => {
          const node = this.nodeList.find((n) => n.id === eaId);
          return {
            eaId: eaId,
            eaName: node ? node.name : `节点${eaId}`,
          };
        }),
        faId: this.$store.state.settings.nowPigFarm,
      };

      updateDistribution(params)
        .then(() => {
          this.msgSuccess(this.$t("warning.saveSuccess"));
          row.isEditing = false;
          row.originalSelectedNodes = [...row.selectedNodes];

          // 更新显示的节点名称
          row.nodeNames = row.selectedNodes
            .map((nodeId) => {
              const node = this.nodeList.find((n) => n.id === nodeId);
              return node ? node.name : "";
            })
            .filter((name) => name);

          row.nodeIds = [...row.selectedNodes];
        })
        .catch(() => {
          this.msgError(this.$t("warning.saveFailed"));
        });
    },
  },
};
</script>
