//报警设置
import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listAlarm(query) {
  return request({
    url: '/system/alarm/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getAlarm(id) {
  return request({
    url: '/system/alarm/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addAlarm(data) {
  return request({
    url: '/system/alarm',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateAlarm(data) {
  return request({
    url: '/system/alarm',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delAlarm(id) {
  return request({
    url: '/system/alarm/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportAlarm(query) {
  return request({
    url: '/system/alarm/export',
    method: 'get',
    params: query
  })
}