<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('columnSystem.dataType')" prop="ntype">
        <el-select
          v-model="queryParams.ntype"
          :placeholder="$t('common.pleaseChoose')"
        >
          <el-option
            v-for="dict in dataTypeList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mid')" prop="mid">
        <el-input
          v-model="queryParams.mid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item  :label="$t('columnSystem.mname')"  prop="mname">
        <el-input
          v-model="queryParams.mname"
           :placeholder="$t('common.pleaseInput')" 
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
        <el-input
          v-model="queryParams.naddress"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item :label="$t('columnSystem.weightDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>
      <!-- : 2历史数据 1正常上传 -->

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['columnSystem:dailyweight:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['columnSystem:dailyweight:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['columnSystem:dailyweight:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['columnSystem:dailyweight:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="dailyweightList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('columnSystem.mid')"
        align="center"
        prop="mid"
        width="180"
      />
      <el-table-column
        :label="$t('columnSystem.mrfid')"
        align="center"
        prop="mrfid"
        width="180"
      />
      <el-table-column
        :label="$t('columnSystem.mdorm')"
        align="center"
        prop="mdorm"
      />
      <el-table-column
        :label="$t('columnSystem.nindex')"
        align="center"
        prop="nindex"
      />
      <el-table-column
        :label="$t('columnSystem.mname')"
        align="center"
        prop="mname"
      />
      <el-table-column
        :label="$t('columnSystem.naddress')"
        align="center"
        prop="naddress"
      />
      <el-table-column
        :label="$t('columnSystem.nweighttime')"
        align="center"
        prop="ndate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ndate) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('columnSystem.weight')"
        align="center"
        prop="nweight"
      />
      <el-table-column
        :label="$t('columnSystem.ntemp')"
        align="center"
        prop="ntemp"
      />
      <!-- <el-table-column :label="$t('columnSystem.ndirect')" align="center" prop="nopendoor" /> -->
      <el-table-column
        :label="$t('columnSystem.dataType')"
        align="center"
        prop="ntype"
        :formatter="typeFormat"
      />
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['columnSystem:dailyweight:edit']"
            >{{ $t("common.update") }}</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['columnSystem:dailyweight:remove']"
            >{{ $t("common.delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【分栏称重记录】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="数据类型: 2历史数据 1正常上传" prop="boarid">
          <el-input
            v-model="form.boarid"
            placeholder="请输入数据类型: 2历史数据 1正常上传"
          />
        </el-form-item> -->
        <!-- <el-form-item :label="$t('columnSystem.mid')" prop="mid">
          <el-select
            v-model="form.mid"
            :placeholder="$t('common.pleaseChoose')"
            clearable
            size="small"
            @change="recodeBoarid($event, item)"
          >
            <el-option
              v-for="item in listPigOptions"
              :key="item.mid"
              :label="item.mid"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
          <el-select
            v-model="form.mrfid"
            :placeholder="$t('common.pleaseChoose')"
            clearable
            size="small"
            @change="recodeBoarid($event)"
          >
            <el-option
              v-for="item in listPigOptions"
              :key="item.mrfid"
              :label="item.mrfid"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
          <el-input
            v-model="form.mdorm"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
          <el-input
            v-model="form.nindex"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.mname')" prop="mname">
          <el-input
            v-model="form.mname"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
          <el-input
            v-model="form.naddress"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.nweighttime')" prop="ndate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ndate"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.weight')" prop="nweight">
          <el-input
            v-model="form.nweight"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.ntemp')" prop="ntemp">
          <el-input
            v-model="form.ntemp"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <!-- <el-form-item :label="$t('columnSystem.ndirect')" prop="nopendoor">
          <el-input v-model="form.nopendoor" placeholder="请输入开门方向" />
        </el-form-item> -->
        <el-form-item :label="$t('columnSystem.dataType')" prop="ntype">
          <el-select
            v-model="form.ntype"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in dataTypeList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDailyweight,
  getDailyweight,
  delDailyweight,
  addDailyweight,
  updateDailyweight,
  exportDailyweight,
} from "@/api/columnSystem/dailyweight";
import { listPigdatadaily } from "@/api/columnSystem/pigdatadaily";
export default {
  name: "Dailyweight",
  components: {},
  data() {
    return {
      listPigOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【分栏称重记录】表格数据
      dailyweightList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        boarid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        nweight: null,
        nopendoor: null,
        ntype: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      dataTypeList: [],
    };
  },
  created() {
    this.getList();
    //查询猪舍号
    listPigdatadaily({
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.listPigOptions = response.rows;
    });
    this.getDicts("fl_pigData_status").then((response) => {
      this.dataTypeList = response.data;
    });
  },
  methods: {
    // 字典状态字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.dataTypeList, row.ntype);
    },
    recodeBoarid(value) {
      this.form.boarid = value;
    },
    /** 查询【分栏称重记录】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      // this.addDateRangeRe(this.queryParams, this.queryParams.ndate)
      listDailyweight(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        this.dailyweightList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        boarid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        nweight: null,
        nopendoor: null,
        ntype: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("columnSystem.addDailyWeight");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDailyweight(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("columnSystem.updateDailyWeight");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDailyweight(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addDailyweight(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("columnSystem.sureCancelDailyWeight") +
          `"` +
          ids +
          `"` +
          this.$t("common.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delDailyweight(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("columnSystem.sureExportDailyWeight"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportDailyweight(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>