import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'



import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import './assets/common.less'
import App from './App' //./是同级目录
import store from './store'
import router from './router' //等同./router/index.js 因为默认会识别目录下的index.js文件
import permission from './directive/permission'

import './assets/icons' // icon
import './permission' // permission control
import {
  getDicts,
  updateData
} from "@/api/system/dict/data"; //data.js  js，vue后缀可以省略
import {
  getConfigKey
} from "@/api/system/config";
import {
  parseTime,
  resetForm,
  addDateRange,
  addDateRangeRe,
  selectDictLabel,
  selectDictLabels,
  download,
  handleTree
} from "@/utils/ruoyi";
import {
  formatNumber,
  formatThousands,
  formatPercent,
  formatFileSize,
  formatScientific,
  formatCurrency,
  clampNumber,
  isValidNumber,
  compareNumbers,
  secondsToHMS,
  secondsToReadable
} from "@/utils/filters";
import Pagination from "@/components/Pagination";
// 自定义表格工具扩展
import RightToolbar from "@/components/RightToolbar"
// 耳缺号链接组件
import EarTagLink from "@/components/EarTagLink"
// 代码高亮插件
import hljs from 'highlight.js'
import 'highlight.js/styles/github-gist.css'
import _ from 'lodash'
import VueSSE from 'vue-sse'
import BaiduMap from 'vue-baidu-map'
import dataV from '@jiaminghi/data-view'


const defaultSettings = require('./settings.js')
// import {
//   VueJsonp
// } from 'vue-jsonp'

// import axios from 'axios'
// Vue.prototype.$axios = axios
// axios.defaults.baseURL = '/api'  //关键代码



Vue.prototype._ = _

/**
 * 引入i18n国际化
 */
import i18n from './i18n/'
// 全局方法挂载


//过滤器 管道
Vue.filter('formatUTC', function (originVal) {
  const date = new Date(originVal);
  const y = date.getFullYear();
  const m = "0" + (date.getMonth() + 1);
  const d = "0" + date.getDate();
  const h = (date.getHours() + '').padStart(2, '0')
  const mm = (date.getMinutes() + '').padStart(2, '0')
  const ss = (date.getSeconds() + '').padStart(2, '0')
  return `${y}-${m.substring(m.length-2,m.length)}-${d.substring(d.length-2,d.length)} ${h}:${mm}:${ss}`; // 2021-09-14 19:36:10
  //return `${y}-${m.substring(m.length-2,m.length)}-${d.substring(d.length-2,d.length)}`;  //2021-09-14   
})

// 数值处理全局过滤器
Vue.filter('formatNumber', formatNumber)
Vue.filter('formatThousands', formatThousands)
Vue.filter('formatPercent', formatPercent)
Vue.filter('formatFileSize', formatFileSize)
Vue.filter('formatScientific', formatScientific)
Vue.filter('formatCurrency', formatCurrency)
Vue.filter('clampNumber', clampNumber)
Vue.filter('isValidNumber', isValidNumber)
Vue.filter('compareNumbers', compareNumbers)
Vue.filter('secondsToHMS', secondsToHMS)
Vue.filter('secondsToReadable', secondsToReadable)



Vue.prototype.getDicts = getDicts
Vue.prototype.updateData = updateData
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.addDateRangeRe = addDateRangeRe
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree

Vue.prototype.msgSuccess = function (msg) {
  this.$message({
    showClose: true,
    message: msg,
    type: "success"
  });
}

Vue.prototype.msgError = function (msg) {
  this.$message({
    showClose: true,
    message: msg,
    type: "error"
  });
}

Vue.prototype.msgInfo = function (msg) {
  this.$message.info(msg);
}

// 全局组件挂载
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('EarTagLink', EarTagLink)

Vue.use(permission)
Vue.use(hljs.vuePlugin);
Vue.use(VueSSE);
Vue.use(BaiduMap, {
  ak: 'u5DR6jX9tnmTfl4SgRnPNZGU3kNlgz3G'
})
Vue.use(dataV)
// Vue.use(VueJsonp)
// Vue.use(VueSSE, {
//   format: 'json',
//   polyfill: true,
//   url: '/my-events-server'
//   withCredentials: true,
// });

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value),
  size: Cookies.get('size') || 'medium' // set element-ui default size
})



Vue.config.productionTip = false

new Vue({
  el: '#app',
  i18n, //挂载
  router,
  store,
  render: h => h(App)
})
