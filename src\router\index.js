import Vue from 'vue'
import Router from 'vue-router'
const defaultSettings = require('../settings.js')
// const this = that

Vue.use(Router)
const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

// console.log("this",new Vue)

/* Layout */
import Layout from '@/layout'
import ParentView from '@/components/ParentView';

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [{
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
      path: '/redirect/:path(.*)',
      component: (resolve) => require(['@/views/redirect'], resolve)
    }]
  },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true
  },
  {
    path: '/charts',
    component: (resolve) => require(['@/views/monitor/test/charts'], resolve),
    hidden: true
  },
  {
    path: '/screen',
    component: (resolve) => require(['@/views/monitor/screen'], resolve),
    hidden: true
  },

  // {
  //   path: '/system/menu',
  //   component: (resolve) => require(['@/views//system/menu'], resolve),
  //   hidden: true
  // },


  // {
  //       path: '/test',
  //       component: (resolve) => require(['@/views/boarMeasure/overview'], resolve),
  //       hidden: true
  // },
  {
    path: '/user',
    component: Layout,
    // hidden: true,
    redirect: 'noredirect',
    children: [{
      path: 'profile',
      component: (resolve) => require(['@/views/system/user/profile/index'], resolve),
      name: 'Profile',
      meta: {
        title: 'menu.personalCenter',
        icon: 'user'
      }
    }]
  },
  {
    path: '/dict',
    component: Layout,
    hidden: true,
    children: [{
      path: 'type/data/:dictId(\\d+)',
      component: (resolve) => require(['@/views/system/dict/data'], resolve),
      name: 'Data',
      meta: {
        title: '字典数据',
        icon: ''
      }
    }]
  },
  {
    path: '/job',
    component: Layout,
    hidden: true,
    children: [{
      path: 'log',
      component: (resolve) => require(['@/views/monitor/job/log'], resolve),
      name: 'JobLog',
      meta: {
        title: '调度日志'
      }
    }]
  },
  {
    path: '/gen',
    component: Layout,
    hidden: true,
    children: [{
      path: 'edit/:tableId(\\d+)',
      component: (resolve) => require(['@/views/tool/gen/editTable'], resolve),
      name: 'GenEdit',
      meta: {
        title: '修改生成配置'
      }
    }]
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    hidden: true,
    //   children: [
    //     {
    //     path: 'index',
    //     component: (resolve) => require(['@/views/monitor/map'], resolve),
    //     name: 'index',
    //     meta: {
    //       title: 'menu.frontPage',
    //       icon: 'dashboard',
    //       noCache: true,
    //       affix: true
    //     }
    //   }

    // ]
    children: defaultSettings.defaultRouter
  },

  {
    name: "BoarMeasure",
    path: "/boarMeasure",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "menu.boarMeasure",
      icon: "tree-table",
      noCache: false
    },
    children: [{
        name: "Breed_pigdata",
        path: "breed_pigdata",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/pigdata/index'], resolve),
        meta: {
          title: "menu.breedPigdata",
          icon: "component",
          noCache: false
        }
      },
      {
        name: "Breed_overview",
        path: "breed_overview",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/overview/index'], resolve),
        meta: {
          title: "menu.breedOverview",
          icon: "dict",
          noCache: false
        }
      },
      {
        name: "Breed_measureday",
        path: "breed_measureday",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/measureday/index'], resolve),
        meta: {
          title: "menu.breedMeasureday",
          icon: "server",
          noCache: false
        }
      },
      {
        name: "Breed_dayReport",
        path: "breed_dayReport",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/dayReport/index'], resolve),
        meta: {
          title: "menu.breedDayReport",
          icon: "build",
          noCache: false
        }
      },
      {
        name: "Breed_mreasuredaygather",
        path: "breed_mreasuredaygather",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/mreasuredaygather/index'], resolve),
        meta: {
          title: "menu.breedMeasuredaygather",
          icon: "chart",
          noCache: false
        }
      },
      {
        name: "Breed_socialAnimal",
        path: "breed_socialAnimal",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/socialAnimal/index'], resolve),
        meta: {
          title: "menu.breedSocialAnimal",
          icon: "redis",
          noCache: false
        }
      },
      {
        name: "Breed_control",
        path: "breed_control",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/control/index'], resolve),
        meta: {
          title: "menu.breedControl",
          icon: "example",
          noCache: false
        }
      },
      {
        name: "Breed_controlV2",
        path: "breed_controlV2",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/controlV2/index'], resolve),
        meta: {
          title: "menu.breedControlV2",
          icon: "example",
          noCache: false
        }
      },
      {
        name: "Breed_measure",
        path: "breed_measure",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/measure/index'], resolve),
        meta: {
          title: "menu.breedMeasure",
          icon: "row",
          "noCache": false
        }
      }, {
        name: "breed_MeasureError",
        path: "breed_MeasureError",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/measureError/index'], resolve),
        meta: {
          title: "menu.measureError",
          icon: "question",
          "noCache": false
        }
      }, {
        name: "Breed_feeddata",
        path: "breed_feeddata",
        hidden: false,
        component: (resolve) => require(['@/views/boarMeasure/feeddata/index'], resolve),
        meta: {
          title: "menu.breedFeeddata",
          icon: "build",
          noCache: false
        }
      }
    ]
  },

  {
    name: "HuanKongBase",
    path: '/huanKong',
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: 'menu.environmentalControl',
      icon: "dashboard",
      noCache: false
    },
    children: [{
        name: "Home",
        path: "env_home",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/home'], resolve),
        meta: {
          title: 'menu.pigHouseConfiguration',
          icon: "tree-table",
          noCache: false
        }
      },
      {
        name: "HomeData",
        path: "env_homeData",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/homeData'], resolve),
        meta: {
          title: "menu.pigHouseRealtimeMonitoring",
          icon: "chart",
          noCache: false
        }
      },
      {
        name: "Env_control",
        path: "env_control",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/control'], resolve),
        meta: {
          title: "menu.RingControlParameterAdjustment",
          icon: "edit",
          noCache: false
        }
      },
      {
        name: "Deviceset",
        path: "env_deviceset",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/deviceSet'], resolve),
        meta: {
          title: "menu.envConDeviceConfiguration",
          icon: "system",
          noCache: false
        }
      },
      {
        name: "Historydata",
        path: "env_historydata",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/historyData'], resolve),
        meta: {
          title: "menu.envConHistoricalData",
          icon: "form",
          noCache: false
        }
      },
      {
        name: "Report",
        path: "env_report",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/report'], resolve),
        meta: {
          title: "menu.envConSummaryReport",
          icon: "chart",
          noCache: false
        }
      },
      {
        name: "Stage",
        path: "env_stage",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/stage'], resolve),
        meta: {
          title: "menu.stageParameterConfiguration",
          icon: "edit",
          noCache: false
        }
      },
      {
        name: "ControlDictionary",
        path: "env_controlDictionary",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/controlDictionary'], resolve),
        meta: {
          title: "menu.singleParameterConfiguration",
          icon: "edit",
          noCache: false
        }
      },
      {
        name: "Video",
        path: "env_video",
        hidden: false,
        component: (resolve) => require(['@/views/huanKong/video'], resolve),
        meta: {
          title: "menu.videoRealtimeMonitoring",
          icon: "eye-open",
          noCache: false
        }
      }
    ]
  },
  {
    name: "ElectricBase",
    path: "/electric",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "menu.electricityMeter",
      icon: "dict",
      noCache: false
    },
    children: [{
        name: "monitor",
        path: "ele_monitor",
        hidden: false,
        component: (resolve) => require(['@/views/electric/monitor'], resolve),
        meta: {
          title: "menu.realtimeMonitoringElectricityMeters",
          icon: "chart",
          noCache: false
        }
      },
      {
        name: "deviceConfig",
        path: "ele_deviceConfig",
        hidden: false,
        component: (resolve) => require(['@/views/electric/deviceConfig'], resolve),
        meta: {
          title: "menu.meterDeviceConfiguration",
          icon: "system",
          noCache: false
        }
      },
      {
        name: "history",
        path: "ele_history",
        hidden: false,
        component: (resolve) => require(['@/views/electric/history'], resolve),
        meta: {
          title: "menu.meterHistoryData",
          icon: "time",
          noCache: false
        }
      },
      {
        name: "report",
        path: "ele_report",
        hidden: false,
        component: (resolve) => require(['@/views/electric/report'], resolve),
        meta: {
          title: "menu.meterSummaryReport",
          icon: "table",
          noCache: false
        }
      },
      {
        name: "Water_report",
        path: "water_report",
        hidden: false,
        component: (resolve) => require(['@/views/electric/water_report'], resolve),
        meta: {
          title: "menu.waterSummaryReport",
          icon: "chart",
          noCache: false
        }
      },
      {
        name: "Water_monitor",
        path: "water_monitor",
        hidden: false,
        component: (resolve) => require(['@/views/electric/water_monitor'], resolve),
        meta: {
          title: "menu.realtimeMonitoringWater",
          icon: "druid",
          noCache: false
        }
      },
      {
        name: "Water_history",
        path: "water_history",
        hidden: false,
        component: (resolve) => require(['@/views/electric/water_history'], resolve),
        meta: {
          title: "menu.waterHistoryData",
          icon: "table",
          noCache: false
        }
      }

    ]
  },
  {
    name: "LiaotaBase",
    path: "/liaota",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "menu.tower",
      icon: "example",
      noCache: false
    },
    children: [{
        name: "homeData",
        path: "tower_homeData",
        hidden: false,
        component: (resolve) => require(['@/views/liaota/homeData'], resolve),
        meta: {
          title: "menu.realtimeMonitoringMaterialTower",
          icon: "druid",
          noCache: false
        }
      },
      {
        name: "config",
        path: "tower_config",
        hidden: false,
        component: (resolve) => require(['@/views/liaota/config'], resolve),
        meta: {
          title: "menu.materialTowerEquipmentConfiguration",
          icon: "date",
          noCache: false
        }
      },
      {
        name: "historyData",
        path: "tower_historyData",
        hidden: false,
        component: (resolve) => require(['@/views/liaota/historyData'], resolve),
        meta: {
          title: "menu.materialTowerHistoricalData",
          icon: "edit",
          noCache: false
        }
      },
      {
        name: "reportBase",
        path: "tower_report",
        hidden: false,
        component: (resolve) => require(['@/views/liaota/report'], resolve),
        meta: {
          title: "menu.towerSummaryReport",
          icon: "chart",
          noCache: false
        }
      }
    ]
  },
  {
    name: "ColumnSystemBase",
    path: "/columnSystem",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "menu.columnSystem",
      icon: "column",
      noCache: false
    },
    children: [{
        name: "Overview",
        path: "overview",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/overview'], resolve),
        meta: {
          title: "menu.columnSystemOverview",
          icon: "tree",
          noCache: false
        }
      },
      {
        name: "Dailyweightday",
        path: "dailyweightday",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/dailyweightday'], resolve),
        meta: {
          title: "menu.columnSystemDailyweightday",
          icon: "clipboard",
          noCache: false
        }
      },
      {
        name: "Dailyweight",
        path: "dailyweight",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/dailyweight'], resolve),
        meta: {
          title: "menu.columnSystemDailyWeight",
          icon: "documentation",
          noCache: false
        }
      },
      {
        name: "Pigdatadaily",
        path: "pigdatadaily",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/pigdatadaily'], resolve),
        meta: {
          title: "menu.columnSystemPigdatadaily",
          icon: "druid",
          noCache: false
        }
      },
      {
        name: "Weightdistribution",
        path: "weightdistribution",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/weightdistribution'], resolve),
        meta: {
          title: "menu.columnSystemWeightdistribution",
          icon: "build",
          noCache: false
        }
      },
      {
        name: "Sametime",
        path: "sametime",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/sametime'], resolve),
        meta: {
          title: "menu.columnSystemSametime",
          icon: "date",
          noCache: false
        }
      },
      {
        name: "Transactionprocessing",
        path: "transactionprocessing",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/transactionprocessing'], resolve),
        meta: {
          title: "menu.columnSystemTransactionprocessing",
          icon: "search",
          noCache: false
        }
      },
      {
        name: "Controldaily",
        path: "controldaily",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/controldaily'], resolve),
        meta: {
          title: "menu.columnSystemControldaily",
          icon: "tool",
          noCache: false
        }
      },
      {
        name: "Alarm",
        path: "alarm",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/alarm'], resolve),
        meta: {
          title: "menu.columnSystemAlarm",
          icon: "validCode",
          noCache: false
        }
      },
      {
        name: "Prediction",
        path: "prediction",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/prediction'], resolve),
        meta: {
          title: "menu.columnSystemPrediction",
          icon: "time-range",
          noCache: false
        }
      },
      {
        name: "Zeroprediction",
        path: "zeroprediction",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/zeroprediction'], resolve),
        meta: {
          title: "menu.columnSystemZeroprediction",
          icon: "time-range",
          noCache: false
        }
      },
      {
        name: "Stationgrowthreport",
        path: "stationgrowthreport",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/stationgrowthreport'], resolve),
        meta: {
          title: "menu.columnSystemStationGrowthReport",
          icon: "chart",
          noCache: false
        }
      },
      {
        name: "Individualgrowthreport",
        path: "individualgrowthreport",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/individualgrowthreport'], resolve),
        meta: {
          title: "menu.columnSystemIndividualGrowthReport",
          icon: "clipboard",
          noCache: false
        }
      },
      {
        name: "zerocunliannum",
        path: "zerocunliannum",
        hidden: false,
        component: (resolve) => require(['@/views/columnSystem/zerocunlaninout'], resolve),
        meta: {
          title: "menu.columnSystemZerocunlaninout",
          icon: "clipboard",
          noCache: false
        }
      }

    ]
  },




  // {
  //   name: "WarningBase",
  //   path: "/warning",
  //   hidden: false,
  //   redirect: "noRedirect",
  //   component: Layout,
  //   alwaysShow: true,
  //   meta: {
  //     title: "断电缺相模块",
  //     icon: "swagger",
  //     noCache: false
  //   },
  //   children: [{
  //     name: "OutageBase",
  //     path: "outage",
  //     hidden: false,
  //     component: (resolve) => require(['@/views/warning/outage'], resolve),
  //     meta: {
  //       title: "断电缺项",
  //       icon: "tool",
  //       noCache: false
  //     }
  //   }]
  // },


  {
    "name": "Warning",
    "path": "/warning",
    "hidden": false,
    "redirect": "noRedirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "断电缺相模块",
      "icon": "ddqx",
      "noCache": false
    },
    "children": [{
        "name": "Outage",
        "path": "outage",
        "hidden": false,
        "component": (resolve) => require(['@/views/warning/outage/index'], resolve),
        "meta": {
          "title": "节点历史数据",
          "icon": "form",
          "noCache": false
        }
      },
      {
        "name": "Config",
        "path": "config",
        "hidden": false,
        "component": (resolve) => require(['@/views/warning/config/index'], resolve),
        "meta": {
          "title": "监控节点配置",
          "icon": "system",
          "noCache": false
        }
      },
      {
        "name": "Distribution",
        "path": "distribution",
        "hidden": false,
        "component": (resolve) => require(['@/views/warning/distribution/index'], resolve),
        "meta": {
          "title": "监控节点分配",
          "icon": "peoples",
          "noCache": false
        }
      }
    ]
  },




]

// Router.selfaddRoutes = function (params){
//   Router.matcher = new Router().matcher;
//   Router.addRoutes(params)
// }

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})
