import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listHome(query) {
  return request({
    url: '/system/liaotahome/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getHome(id) {
  return request({
    url: '/system/liaotahome/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addHome(data) {
  return request({
    url: '/system/liaotahome',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateHome(data) {
  return request({
    url: '/system/liaotahome',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delHome(id) {
  return request({
    url: '/system/liaotahome/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportHome(query) {
  return request({
    url: '/system/liaotahome/export',
    method: 'get',
    params: query
  })
}