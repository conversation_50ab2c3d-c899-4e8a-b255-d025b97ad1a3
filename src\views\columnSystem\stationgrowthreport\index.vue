<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
        <span>
          <el-select
            v-model="queryParams.naddress"
            :placeholder="$t('common.pleaseChoose')"
            size="small"
          >
            <el-option
              v-for="item in mNameOptions"
              :key="item.naddress"
              :label="item.naddress"
              :value="item.naddress"
              @keyup.enter.native="handleQuery"
            ></el-option>
          </el-select>

          <div
            style="
              position: relative;
              display: inline-block;
              width: 80px;
              height: 10px;
            "
          >
            <i
              :class="{ icon: iconShowSmall }"
              class="el-icon-caret-top"
              style="
                font-size: 22px;
                position: absolute;
                top: -16px;
                color: #c0c4cc;
              "
              @click="handleSmall"
            ></i>
            <i
              :class="{ icon: iconShowBig }"
              class="el-icon-caret-bottom"
              style="font-size: 22px; position: absolute; color: #c0c4cc"
              @click="handleBig"
            ></i>
          </div>
        </span>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.date')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['columnSystem:stationgrowthreport:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        :label="$t('columnSystem.dataList')"
        name="first"
        :lazy="true"
      >
        <el-table
          v-loading="loading"
          :data="growReportsList"
          :cell-style="{ padding: '0' }"
        >
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column
            :label="$t('columnSystem.naddress')"
            align="center"
            prop="naddress"
          />
          <el-table-column
            :label="$t('columnSystem.date')"
            align="center"
            prop="ndate"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.ndate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('columnSystem.midWeight')"
            align="center"
            prop="midWeight"
          />
          <el-table-column
            :label="$t('columnSystem.lightWeight')"
            align="center"
            prop="lightWeight"
          />
          <el-table-column
            :label="$t('columnSystem.highWeight')"
            align="center"
            prop="highWeight"
          />
        </el-table>
        <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
        /> -->
      </el-tab-pane>

      <el-tab-pane :label="$t('columnSystem.report')" name="third" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div
            ref="category2"
            id="category2"
            style="height: 400px"
            v-if="'third' === activeName"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { growreports } from "@/api/columnSystem/stationgrowthreport";
import { listControldaily } from "@/api/columnSystem/controldaily";
import * as echarts from "echarts";
require("@/utils/walden"); // echarts theme

export default {
  name: "stationgrowthreport",
  components: {},
  // iconShowSmall: false,
  //  iconShowBig: false,
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true,
    },
    iconShowBig: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      //饲料总消耗量
      foodAll: "",
      //记录天数
      recordDays: "",
      //平均日采食量
      averageDailyFeed: "",
      //开始平均体重
      startAverageWeight: "",
      //结束平均体重
      endAverageWeight: "",
      //平均增重
      averageGrowth: "",
      //平均日增重
      averageDailyGrowth: "",
      orgOptions: null,
      activeName: "first",
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      //饲料消耗
      category1: null,
      //平均体重
      category2: null,
      //平均日增重
      category3: null,
      //栏号数组
      mNameOptions: [],
      //foodEchart
      foodEchartList: [],
      //weightEchart
      weightEchartList: [],
      weightGrowEchartList: [],
      //采食x轴数据
      xDataFood: [],
      //采食y轴数据
      yDataFood: [],
      //体重x轴数据
      xWeight: [],
      //体重y轴数据
      yWeight1: [],
      yWeight2: [],
      yWeight3: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【汇总报告】表格数据
      growReportsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        naddress: null,
        // pageNum: 1,
        // pageSize: 100,
        // nindex: null,
        // date: null,
        // pigNum: null,
        // nIngestionSSum: null,
        // feedSum: null,
        // nSecondSSum: null,
        // weightAvg: null,
        // datefrom: null,
        // dateto: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mid: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
        mrfid: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    "queryParams.naddress": {
      handler() {
        if (this.queryParams) {
          this.handleQuery();
        }
      },
      deep: true,
    },
    "queryParams.dateRange": {
      handler() {
        if (this.queryParams) {
          this.handleQuery();
        }
      },
      deep: true,
    },
  },
  created() {
    //查询栏号
    listControldaily({
      ntype: 3,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.mNameOptions = response.rows;
      this.queryParams.naddress = this.mNameOptions[0].naddress;
    });

    //this.getList();
  },
  mounted() {
    //渲染之后
    //this.getDatas();

    window.onresize = () => {
      //alert("sss");
      this.category1.resize(); //重新初始化echarts
      this.category2.resize(); //重新初始化echarts
    };
  },
  methods: {
    // //点击上移
    // clickUp(index) {
    //   console.log("this.mNameOptions", this.mNameOptions);
    //   // this.swapArray(this.tableData, index-1, index);
    // },
    // //点击下移
    // clickDown(index) {
    //   this.swapArray(this.tableData, index, index + 1);
    // },
    // //数组元素互换位置
    // swapArray(arr, index1, index2) {
    //   arr[index1] = arr.splice(index2, 1, arr[index1])[0];
    //   return arr;
    // },
    handleSmall() {
      if (this.queryParams.naddress) {
        let index = this.mNameOptions.findIndex(
          (item) => item.naddress == this.queryParams.naddress
        );
        if (index > 0) {
          this.queryParams.naddress = this.mNameOptions[index - 1].naddress;
        }
      } else {
        this.queryParams.naddress = this.mNameOptions[0].naddress;
      }
    },
    handleBig() {
      if (this.queryParams.naddress) {
        let index = this.mNameOptions.findIndex(
          (item) => item.naddress == this.queryParams.naddress
        );
        if (index < this.mNameOptions.length - 1) {
          this.queryParams.naddress = this.mNameOptions[index + 1].naddress;
        }
      } else {
        this.queryParams.naddress =
          this.mNameOptions[this.mNameOptions.length - 1].naddress;
      }
    },
    handleClick(tab, event) {
      if (tab.name == "first") this.getList();
      if (tab.name == "third") this.getList();
    },

    /** 查询【汇总报告】列表 */
    getList() {
      if (this.queryParams.naddress && this.queryParams.dateRange) {
        this.loading = true;
        this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
        growreports(
          this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
        ).then((response) => {
          this.growReportsList = response.data;
          if (this.activeName == "third" && this.growReportsList) {
            this.xWeight = [];
            this.yWeight1 = [];
            this.yWeight2 = [];
            this.yWeight3 = [];
            this.growReportsList.forEach((element) => {
              this.xWeight.push(element.ndate);
              this.yWeight1.push(element.midWeight);
              this.yWeight2.push(element.lightWeight);
              this.yWeight3.push(element.highWeight);
            });
            this.getWeightAgvData();
          } else {
            this.xWeight = [];
            this.yWeight1 = [];
            this.yWeight2 = [];
            this.yWeight3 = [];
          }

          // this.total = response.total;
          this.loading = false;
        });
      } else {
        this.msgInfo(this.$t("columnSystem.firstInputColumnAndDate"));
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        nindex: null,
        date: null,
        pigNum: null,
        nIngestionSSum: null,
        feedSum: null,
        nSecondSSum: null,
        weightAvg: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 导出按钮操作 */
    // handleExport() {
    //   const queryParams = this.queryParams;
    //   this.$confirm("是否确认导出所有【汇总报告】数据项?", this.$t("common.warn"), {
    //     confirmButtonText: this.$t("common.determine"),
    //     cancelButtonText: this.$t("common.cancel"),
    //     type: "warning",
    //   })
    //     .then(function () {
    //       return exportMeasureday(queryParams);
    //     })
    //     .then((response) => {
    //       this.download(response.msg);
    //     });
    // },

    getWeightAgvData() {
      this.category2 = this.$refs.category2
        ? echarts.init(this.$refs.category2, "walden")
        : "";
      // this.category2 = echarts.init(this.$refs.category2, "walden");
      this.category2 &&
        this.category2.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
          },
          legend: {
            data: [
              this.$t("columnSystem.midWeight"),
              this.$t("columnSystem.lightWeight"),
              this.$t("columnSystem.highWeight"),
            ],
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: this.xWeight,
            inverse: true,
          },
          yAxis: {
            type: "value",
            maxInterval: 5,
          },
          series: [
            {
              // symbol: "none", //取消折点圆圈
              name: this.$t("columnSystem.midWeightKG"),
              type: "line",
              // stack: this.$t("columnSystem.total"),
              data: this.yWeight1,
              inverse: true,
            },
            {
              // symbol: "none", //取消折点圆圈
              name: this.$t("columnSystem.lightWeightKG"),
              type: "line",
              // stack: this.$t("columnSystem.total"),
              data: this.yWeight2,
              inverse: true,
            },
            {
              // symbol: "none", //取消折点圆圈
              name: this.$t("columnSystem.highWeightKG"),
              type: "line",
              // stack: this.$t("columnSystem.total"),
              data: this.yWeight3,
              inverse: true,
            },
          ],
        });
    },
  },
};
</script>
<style scoped>
.icon:hover {
  color: #48d1cc !important;
}
</style>
