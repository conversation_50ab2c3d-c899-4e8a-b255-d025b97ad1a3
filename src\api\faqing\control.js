import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listControl(query) {
  return request({
    url: '/faqing/control/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getControl(id) {
  return request({
    url: '/faqing/control/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addControl(data) {
  return request({
    url: '/faqing/control',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateControl(data) {
  return request({
    url: '/faqing/control',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delControl(id) {
  return request({
    url: '/faqing/control/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportControl(query) {
  return request({
    url: '/faqing/control/export',
    method: 'get',
    params: query
  })
}



export function treeselect(query) {
  return request({
    url: '/faqing/control/treeselect',
    method: 'get',
    params: query
  })
}
