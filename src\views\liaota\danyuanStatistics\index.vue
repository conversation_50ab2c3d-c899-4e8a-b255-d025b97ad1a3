<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="单元名称" prop="danyuanname">
        <!-- <el-input
          v-model="queryParams.danyuanname"
          placeholder="请输入单元名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        /> -->
        <el-select
          v-model="queryParams.danyuanname"
          :placeholder="$t('liaota.enterTowerName')"
          size="small"
        >
          <el-option
            v-for="item in danyuanOptions"
            :key="item.id"
            :label="item.danyuanname"
            :value="item.danyuanname"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="料塔名称" prop="slavename">
        <!-- <el-input
          v-model="queryParams.slavename"
          placeholder="请输入料塔名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        /> -->

        <el-select
          v-model="queryParams.slavename"
          :placeholder="$t('liaota.enterTowerName')"
          size="small"
        >
          <el-option
            v-for="item in slaveOptions"
            :key="item.id"
            :label="item.slavename"
            :value="item.slavename"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="行为" prop="msg">
        <el-select v-model="queryParams.msg" placeholder="请选择加放料行为">
          <el-option
            v-for="dict in towerInOutList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="打料量" prop="weight">
        <el-input
          v-model="queryParams.weight"
          placeholder="请输入打料量"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="打料开始时间" prop="begintime">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.begintime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择打料开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="打料结束时间" prop="enddtime">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.enddtime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择打料结束时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:tongji:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:tongji:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:tongji:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:tongji:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tongjiList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="单元名称" align="center" prop="danyuanname" />
      <el-table-column label="料塔名称" align="center" prop="slavename" />
      <el-table-column
        label="打料开始时间"
        align="center"
        prop="begintime"
        width="180"
      >
        <template slot-scope="scope">
          {{ scope.row.begintime | formatUTC }}
          <!-- <span>{{
              parseTime(scope.row.begintime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}</span> -->
        </template>
      </el-table-column>
      <el-table-column
        label="打料结束时间"
        align="center"
        prop="enddtime"
        width="180"
      >
        <template slot-scope="scope">
          {{ scope.row.enddtime | formatUTC }}
          <!-- <span>{{
              parseTime(scope.row.enddtime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}</span> -->
        </template>
      </el-table-column>
      <el-table-column
        label="打料开始重量(T)"
        align="center"
        prop="beginweight"
      />
      <el-table-column
        label="打料结束重量(T)"
        align="center"
        prop="endweight"
      />
      <el-table-column label="打料量(T)" align="center" prop="weight" />
      <!-- 进料放料 -->
      <el-table-column
        label="行为"
        align="center"
        prop="msg"
        :formatter="towerInOutListFormat"
      />
      <el-table-column label="饲料类型" align="center" prop="feedtype" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:tongji:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:tongji:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【单元用料统计】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="猪场id" prop="mfactory">
          <el-input v-model="form.mfactory" placeholder="请输入猪场id" />
        </el-form-item> -->
        <el-form-item label="单元id" prop="danyuanid">
          <el-input v-model="form.danyuanid" placeholder="请输入单元id" />
        </el-form-item>
        <el-form-item label="单元名称" prop="danyuanname">
          <el-input v-model="form.danyuanname" placeholder="请输入单元名称" />
        </el-form-item>
        <el-form-item label="料塔id" prop="slaveid">
          <el-input v-model="form.slaveid" placeholder="请输入料塔id" />
        </el-form-item>
        <el-form-item label="料塔名称" prop="slavename">
          <el-input v-model="form.slavename" placeholder="请输入料塔名称" />
        </el-form-item>
        <el-form-item label="打料开始时间" prop="begintime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.begintime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择打料开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="打料结束时间" prop="enddtime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.enddtime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择打料结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="打料开始重量" prop="beginweight">
          <el-input
            v-model="form.beginweight"
            placeholder="请输入打料开始重量"
          />
        </el-form-item>
        <el-form-item label="打料结束重量" prop="endweight">
          <el-input v-model="form.endweight" placeholder="请输入打料结束重量" />
        </el-form-item>
        <el-form-item label="打料量" prop="weight">
          <el-input v-model="form.weight" placeholder="请输入打料量" />
        </el-form-item>
        <el-form-item label="行为" prop="msg">
          <el-select v-model="form.msg" placeholder="请选择加放料行为">
            <el-option
              v-for="dict in towerInOutList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="饲料类型" prop="feedtype">
          <el-input v-model="form.feedtype" placeholder="请输入饲料类型" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listTongji,
  getTongji,
  delTongji,
  addTongji,
  updateTongji,
  exportTongji,
} from "@/api/liaota/danyuanStatistics";
import { listSlave } from "@/api/liaota/slave";
import { listDanyuan } from "@/api/liaota/danyuan";
export default {
  name: "Tongji",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【单元用料统计】表格数据
      tongjiList: [],
      towerInOutList: [],
      slaveOptions: [],
      danyuanOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        danyuanid: null,
        danyuanname: null,
        slaveid: null,
        slavename: null,
        begintime: null,
        enddtime: null,
        beginweight: null,
        endweight: null,
        weight: null,
        msg: null,
        feedtype: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mfactory: [
          { required: true, message: "猪场id不能为空", trigger: "blur" },
        ],
        danyuanid: [
          { required: true, message: "单元id不能为空", trigger: "blur" },
        ],
        slaveid: [
          { required: true, message: "料塔id不能为空", trigger: "blur" },
        ],
        beginweight: [
          { required: true, message: "打料开始重量不能为空", trigger: "blur" },
        ],
        endweight: [
          { required: true, message: "打料结束重量不能为空", trigger: "blur" },
        ],
        weight: [
          { required: true, message: "打料量不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getDicts("tower_in_out").then((response) => {
      this.towerInOutList = response.data;
    });
    listSlave({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.slaveOptions = response.rows;
      }
    );
    listDanyuan({ mfactory: this.$store.state.settings.nowPigFarm }).then(
      (response) => {
        this.danyuanOptions = response.rows;
      }
    );
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  methods: {
    towerInOutListFormat(row, column) {
      return this.selectDictLabel(this.towerInOutList, row.msg);
    },
    /** 查询【单元用料统计】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listTongji(this.queryParams).then((response) => {
        this.tongjiList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mfactory: null,
        danyuanid: null,
        danyuanname: null,
        slaveid: null,
        slavename: null,
        begintime: null,
        enddtime: null,
        beginweight: null,
        endweight: null,
        weight: null,
        msg: null,
        feedtype: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【单元用料统计】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getTongji(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【单元用料统计】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTongji(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addTongji(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【单元用料统计】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delTongji(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【单元用料统计】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportTongji(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>