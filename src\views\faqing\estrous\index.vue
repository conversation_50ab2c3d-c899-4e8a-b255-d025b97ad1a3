<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item label="耳缺号" prop="mid">
        <el-input
          v-model="queryParams.mid"
          placeholder="请输入耳缺号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="电子耳牌" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          placeholder="请输入电子耳牌"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="猪舍号" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          placeholder="请输入猪舍号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="栏号" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          placeholder="请输入栏号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <template v-if="activeName == 'second'">
        <el-form-item label="胎次" prop="nparity">
          <el-input
            v-model="queryParams.nparity"
            placeholder="请输入胎次"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="接触时间" prop="nduration">
          <el-input
            v-model="queryParams.nduration"
            placeholder="请输入接触时间"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="发情指数" prop="nestrousindex">
          <el-input
            v-model="queryParams.nestrousindex"
            placeholder="请输入发情指数"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
      </template>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-col :span="24" class="card-box">
      <el-card>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane
            label="发情数据"
            name="first"
            :lazy="true"
            style="padding-bottom: 10px"
          >
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleAdd"
                  v-hasPermi="['system:estrous:add']"
                  >新增</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="single"
                  @click="handleUpdate"
                  v-hasPermi="['system:estrous:edit']"
                  >修改</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['system:estrous:remove']"
                  >删除</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="warning"
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExport"
                  v-hasPermi="['system:estrous:export']"
                  >导出</el-button
                >
              </el-col>
              <right-toolbar
                :showSearch.sync="showSearch"
                @queryTable="getList"
              ></right-toolbar>
            </el-row>

            <el-table
              v-loading="loading"
              :data="estrousList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="id" />
              <el-table-column label="猪场号" align="center" prop="mfactory" />
              <el-table-column label="耳缺号" align="center" prop="mid" />
              <el-table-column label="电子耳牌" align="center" prop="mrfid" />
              <el-table-column label="猪舍号" align="center" prop="mdorm" />
              <el-table-column label="栏号" align="center" prop="nindex" />
              <el-table-column
                label="设备地址"
                align="center"
                prop="naddress"
              />
              <el-table-column label="胎次" align="center" prop="nparity" />
              <el-table-column
                label="开始时间"
                align="center"
                prop="ndatebegin"
                width="180"
              >
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.ndatebegin, "{y}-{m}-{d}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="结束时间"
                align="center"
                prop="ndateend"
                width="180"
              >
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.ndateend, "{y}-{m}-{d}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="接触时间"
                align="center"
                prop="nduration"
              />
              <el-table-column label="数据类型" align="center" prop="ntype" />
              <el-table-column label="备注" align="center" prop="tag" />
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:estrous:edit']"
                    >修改</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['system:estrous:remove']"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-tab-pane>
          <el-tab-pane
            label="日汇总"
            name="second"
            :lazy="true"
            style="padding-bottom: 10px"
          >
            <el-row :gutter="10" class="mb8">
              <!-- <el-col :span="1.5">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleAddDay"
                  v-hasPermi="['system:estrousday:add']"
                  >新增</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="single"
                  @click="handleUpdateDay"
                  v-hasPermi="['system:estrousday:edit']"
                  >修改</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleDeleteDay"
                  v-hasPermi="['system:estrousday:remove']"
                  >删除</el-button
                >
              </el-col> -->
              <el-col :span="1.5">
                <el-button
                  type="warning"
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExportDay"
                  v-hasPermi="['system:estrousday:export']"
                  >导出</el-button
                >
              </el-col>
              <right-toolbar
                :showSearch.sync="showSearch"
                @queryTable="getListDay"
              ></right-toolbar>
            </el-row>

            <el-table
              v-loading="loadingDay"
              :data="estrousdayList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="id" />
              <el-table-column label="猪场号" align="center" prop="mfactory" />
              <el-table-column label="耳缺号" align="center" prop="mid" />
              <el-table-column label="电子耳牌" align="center" prop="mrfid" />
              <el-table-column label="猪舍号" align="center" prop="mdorm" />
              <el-table-column label="栏号" align="center" prop="nindex" />
              <el-table-column
                label="设备地址"
                align="center"
                prop="naddress"
              />
              <el-table-column label="胎次" align="center" prop="nparity" />
              <el-table-column
                label="日期"
                align="center"
                prop="ndate"
                width="180"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.ndate, "{y}-{m}-{d}") }}</span>
                </template>
              </el-table-column>
              <el-table-column label="次数" align="center" prop="ntimes" />
              <el-table-column
                label="接触时间"
                align="center"
                prop="ndurationall"
              />
              <el-table-column
                label="发情指数"
                align="center"
                prop="nestrousindex"
              />
              <el-table-column label="备注" align="center" prop="tag" />
              <!-- <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:estrousday:edit']"
                    >修改</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['system:estrousday:remove']"
                    >删除</el-button
                  >
                </template>
              </el-table-column> -->
            </el-table>

            <pagination
              v-show="totalDay > 0"
              :total="totalDay"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getListDay"
            />
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </el-col>

    <!-- 添加或修改【发情数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="猪场号" prop="mfactory">
          <el-input v-model="form.mfactory" placeholder="请输入猪场号" />
        </el-form-item>
        <el-form-item label="耳缺号" prop="mid">
          <el-input v-model="form.mid" placeholder="请输入耳缺号" />
        </el-form-item>
        <el-form-item label="电子耳牌" prop="mrfid">
          <el-input v-model="form.mrfid" placeholder="请输入电子耳牌" />
        </el-form-item>
        <el-form-item label="猪舍号" prop="mdorm">
          <el-input v-model="form.mdorm" placeholder="请输入猪舍号" />
        </el-form-item>
        <el-form-item label="栏号" prop="nindex">
          <el-input v-model="form.nindex" placeholder="请输入栏号" />
        </el-form-item>
        <el-form-item label="设备地址" prop="naddress">
          <el-input v-model="form.naddress" placeholder="请输入设备地址" />
        </el-form-item>
        <el-form-item label="胎次" prop="nparity">
          <el-input v-model="form.nparity" placeholder="请输入胎次" />
        </el-form-item>
        <el-form-item label="开始时间" prop="ndatebegin">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ndatebegin"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="ndateend">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ndateend"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="接触时间" prop="nduration">
          <el-input v-model="form.nduration" placeholder="请输入接触时间" />
        </el-form-item>
        <!-- 2历史数据 1实时数据 0猪离开  -1已淘汰 -2未回复下料信息 -->
        <el-form-item label="数据类型" prop="ntype">
          <el-select v-model="form.ntype" placeholder="请选择数据类型">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="tag">
          <el-input v-model="form.tag" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import {
  listEstrous,
  getEstrous,
  delEstrous,
  addEstrous,
  updateEstrous,
  exportEstrous,
} from "@/api/faqing/estrous";

import {
  listEstrousday,
  getEstrousday,
  delEstrousday,
  addEstrousday,
  updateEstrousday,
  exportEstrousday,
} from "@/api/faqing/estrousday";

export default {
  name: "Estrous",
  components: {},
  data() {
    return {
      activeName: "first",
      // 遮罩层
      loading: true,
      loadingDay: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      totalDay: 0,
      // 【发情数据】表格数据
      estrousList: [],
      estrousdayList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        naddress: null,
        nparity: null,
        ndatebegin: null,
        ndateend: null,
        nduration: null,
        ntype: null,
        colStr1: null,
        colStr2: null,
        colStr3: null,
        tag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mrfid: [
          {
            required: true,
            message: "不能为空",
            trigger: "blur",
          },
        ],
        ndatebegin: [
          {
            required: true,
            message: "不能为空",
            trigger: "blur",
          },
        ],
        nduration: [
          {
            required: true,
            message: "不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【发情数据】列表 */
    getList() {
      this.loading = true;
      listEstrous(this.queryParams).then((response) => {
        this.estrousList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询【发情类总】列表 */
    getListDay() {
      this.loadingDay = true;
      listEstrousday(this.queryParams).then((response) => {
        this.estrousdayList = response.rows;
        this.totalDay = response.total;
        this.loadingDay = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mfactory: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        naddress: null,
        nparity: null,
        ndatebegin: null,
        ndateend: null,
        nduration: null,
        ntype: null,
        colStr1: null,
        colStr2: null,
        colStr3: null,
        tag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.activeName == "first") {
        this.getList();
      } else if (this.activeName == "second") {
        this.getListDay();
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【发情数据】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEstrous(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【发情数据】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEstrous(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEstrous(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【发情数据】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delEstrous(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    handleClick(tab, event) {
      if (tab.name == "first") this.getList();
      if (tab.name == "second") {
        this.getListDay();
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【发情数据】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportEstrous(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },

    /** 导出按钮操作 */
    handleExportDay() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【发情类总】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportEstrousday(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>