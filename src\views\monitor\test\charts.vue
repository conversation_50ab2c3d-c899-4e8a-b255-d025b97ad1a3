<template>
  <div class="app-container" >

    <el-row>
      <el-col :xs="24" :sm="24" :md="12" :lg="6" class="card-box">
        <el-card>
          <el-row>
            <el-tag
                color=""
                effect="dark"
                class="tagLine">
              温度
            </el-tag>
          </el-row>
          <el-divider class="lineS"></el-divider>
          <el-row class="rowStyle">
            <el-col :span="8" style="text-align: left">日龄:220</el-col>
            <el-col :span="8">过度通风</el-col>
            <el-col :span="8" style="text-align: right">等级57</el-col>
          </el-row>
          <el-row class="rowStyle">
            <el-col :span="24" style="text-align: right">2020-12-29 10:26</el-col>
          </el-row>
          <el-row class="card-box">
            <el-row>
              <el-tabs>
                <el-tab-pane label="环境">
                  <el-row class="rowStyle">
                    <el-col :span="8">温度</el-col>
                    <el-col :span="8">气压</el-col>
                    <el-col :span="8">气流</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">温度</el-col>
                    <el-col :span="8">气压</el-col>
                    <el-col :span="8">气流</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>

                </el-tab-pane>
                <el-tab-pane label="       控制       ">
                  <el-row class="rowStyle">
                    <el-col :span="8">电压</el-col>
                    <el-col :span="8">电流</el-col>
                    <el-col :span="8">电感</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">电压</el-col>
                    <el-col :span="8">电流</el-col>
                    <el-col :span="8">电感</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                </el-tab-pane>

              </el-tabs>
            </el-row>
            <el-row>

            </el-row>
            <el-row>

            </el-row>
          </el-row>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="6" class="card-box">
        <el-card>
          <el-row>
            <el-tag
                color=""
                
                effect="dark"
                class="tagLine">
              温度
            </el-tag>
          </el-row>
          <el-divider class="lineS"></el-divider>
          <el-row class="rowStyle">
            <el-col :span="8" style="text-align: left">日龄:220</el-col>
            <el-col :span="8">过度通风</el-col>
            <el-col :span="8" style="text-align: right">等级57</el-col>
          </el-row>
          <el-row class="rowStyle">
            <el-col :span="24" style="text-align: right">2020-12-29 10:26</el-col>
          </el-row>
          <el-row class="card-box">
            <el-row>
              <el-tabs>
                <el-tab-pane label="环境">
                  <el-row class="rowStyle">
                    <el-col :span="8">温度</el-col>
                    <el-col :span="8">气压</el-col>
                    <el-col :span="8">气流</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">温度</el-col>
                    <el-col :span="8">气压</el-col>
                    <el-col :span="8">气流</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>

                </el-tab-pane>
                <el-tab-pane label="       控制       ">
                  <el-row class="rowStyle">
                    <el-col :span="8">电压</el-col>
                    <el-col :span="8">电流</el-col>
                    <el-col :span="8">电感</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">电压</el-col>
                    <el-col :span="8">电流</el-col>
                    <el-col :span="8">电感</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                </el-tab-pane>

              </el-tabs>
            </el-row>
            <el-row>

            </el-row>
            <el-row>

            </el-row>
          </el-row>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="6" class="card-box">
        <el-card>
          <el-row>
            <el-tag
                color=""
                
                effect="dark"
                class="tagLine">
              温度
            </el-tag>
          </el-row>
          <el-divider class="lineS"></el-divider>
          <el-row class="rowStyle">
            <el-col :span="8" style="text-align: left">日龄:220</el-col>
            <el-col :span="8">过度通风</el-col>
            <el-col :span="8" style="text-align: right">等级57</el-col>
          </el-row>
          <el-row class="rowStyle">
            <el-col :span="24" style="text-align: right">2020-12-29 10:26</el-col>
          </el-row>
          <el-row class="card-box">
            <el-row>
              <el-tabs>
                <el-tab-pane label="环境">
                  <el-row class="rowStyle">
                    <el-col :span="8">温度</el-col>
                    <el-col :span="8">气压</el-col>
                    <el-col :span="8">气流</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">温度</el-col>
                    <el-col :span="8">气压</el-col>
                    <el-col :span="8">气流</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>

                </el-tab-pane>
                <el-tab-pane label="       控制       ">
                  <el-row class="rowStyle">
                    <el-col :span="8">电压</el-col>
                    <el-col :span="8">电流</el-col>
                    <el-col :span="8">电感</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">电压</el-col>
                    <el-col :span="8">电流</el-col>
                    <el-col :span="8">电感</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                </el-tab-pane>

              </el-tabs>
            </el-row>
            <el-row>

            </el-row>
            <el-row>

            </el-row>
          </el-row>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="6" class="card-box">
        <el-card>
          <el-row>
            <el-tag
                effect="dark"
                class="tagLine">
              温度
            </el-tag>
          </el-row>
          <el-divider class="lineS"></el-divider>
          <el-row class="rowStyle">
            <el-col :span="8" style="text-align: left">日龄:220</el-col>
            <el-col :span="8">过度通风</el-col>
            <el-col :span="8" style="text-align: right">等级57</el-col>
          </el-row>
          <el-row class="rowStyle">
            <el-col :span="24" style="text-align: right">2020-12-29 10:26</el-col>
          </el-row>
          <el-row class="card-box">
            <el-row>
              <el-tabs>
                <el-tab-pane label="环境">
                  <el-row class="rowStyle">
                    <el-col :span="8">温度</el-col>
                    <el-col :span="8">气压</el-col>
                    <el-col :span="8">气流</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">温度</el-col>
                    <el-col :span="8">气压</el-col>
                    <el-col :span="8">气流</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>

                </el-tab-pane>
                <el-tab-pane label="       控制       ">
                  <el-row class="rowStyle">
                    <el-col :span="8">电压</el-col>
                    <el-col :span="8">电流</el-col>
                    <el-col :span="8">电感</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">电压</el-col>
                    <el-col :span="8">电流</el-col>
                    <el-col :span="8">电感</el-col>
                  </el-row>
                  <el-row class="rowStyle">
                    <el-col :span="8">90</el-col>
                    <el-col :span="8">80</el-col>
                    <el-col :span="8">80</el-col>
                  </el-row>
                </el-tab-pane>

              </el-tabs>
            </el-row>
            <el-row>

            </el-row>
            <el-row>

            </el-row>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :xs="24" :sm="24" :md="24" :lg="12" class="card-box">
        <el-card>
          <div slot="header"><span>温度曲线</span></div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="category13" style="height: 300px" />
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="24" :lg="12" class="card-box">
        <el-row>
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <el-card>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category2" id="category2" style="height: 150px" />
              </div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <el-card>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category3" id="category3" style="height: 150px" />
              </div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <el-card>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category4" id="category4" style="height: 150px" />
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <el-card>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category5" id="category5" style="height: 150px" />
              </div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <el-card>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category6" id="category6" style="height: 150px" />
              </div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <el-card>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category7" id="category7" style="height: 150px" />
              </div>
            </el-card>
          </el-col>
        </el-row>

      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts";


export default {
  name: "charts",
  data(){
    return{
      category13: null,
      category2: null,
      category3: null,
      category4: null,
      category5: null,
      category6: null,
      category7: null,
    };
  },
  mounted() {
    this.getDatas();
    window.onresize = () => {
      //alert("sss");
      this.category13.resize();  //重新初始化echarts
      this.category2.resize();
      this.category3.resize();
      this.category4.resize();
      this.category5.resize();
      this.category6.resize();
      this.category7.resize();
    }
  },
  methods:{
    getDatas() {

      this.category13 = echarts.init(this.$refs.category13);
      //alert(this.document.getElementById('#category1'));

      this.category13.setOption({
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['温度', '湿度', '气流', '气压', '电压']
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '气温',
            type: 'line',
            stack: '总量',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '气压',
            type: 'line',
            stack: '总量',
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: '电流',
            type: 'line',
            stack: '总量',
            data: [150, 232, 201, 154, 190, 330, 410]
          },
          {
            name: '电压',
            type: 'line',
            stack: '总量',
            data: [320, 332, 301, 334, 390, 330, 320]
          },
          {
            name: '温度',
            type: 'line',
            stack: '总量',
            data: [820, 932, 901, 934, 1290, 1330, 1320]
          }
        ]
      });

      var option = {
            title: {
              text: '温度1',
              left:'center', //标题位置
              bottom:'10%',
              textStyle:{
                color:"#999",
                fontSize:14
              },
            },
            tooltip: {
              formatter: '{a} <br/>{b} : {c}%'
            },
            toolbox: {
              feature: {
                restore: {},
                saveAsImage: {}
              }
            },
            series: [
              {
                min: 0,
                max: 160,
                splitNumber: 4,
                axisLine: {            // 坐标轴线
                  lineStyle: {       // 属性lineStyle控制线条样式
                    width: 10
                  }
                },
                axisTick: {            // 坐标轴小标记
                  length: 15,        // 属性length控制线长
                  lineStyle: {       // 属性lineStyle控制线条样式
                    color: 'auto'
                  }
                },
                splitLine: {           // 分隔线
                  length: 20,         // 属性length控制线长
                  lineStyle: {       // 属性lineStyle（详见lineStyle）控制线条样式
                    color: 'auto'
                  }
                },
                pointer: {
                  width: 2,
                  shadowColor: '#fff', //默认透明
                  shadowBlur: 5
                },

                startAngle: 210,
                endAngle: -30,
                name: '温度',
                type: 'gauge',
                detail: {formatter: '30'},
                data: [{value: 50, name: ''}]


              }
            ]
          };

      this.category2 = echarts.init(this.$refs.category2).setOption(option,true);
      this.category3=  echarts.init(this.$refs.category3).setOption(option,true);
      this.category4 = echarts.init(this.$refs.category4).setOption(option,true);
      this.category5 = echarts.init(this.$refs.category5).setOption(option,true);
      this.category6 = echarts.init(this.$refs.category6).setOption(option,true);
      this.category7 = echarts.init(this.$refs.category7).setOption(option,true);

    }
  }
}
</script>

<style scoped>
.tagLine{
  border-radius: 100px;
  width: 100%;
  text-align: center;
  height: 40px;
  line-height: 40px;
  background-color:#79bbff;
  border: none;
  font-size: 14px;
}
.rowStyle{
  font-size: 12px;
  padding-top: 8px;
  text-align: center;
}
.lineS{
  margin: 5px 0px;
  height: 0.3px;
}

</style>