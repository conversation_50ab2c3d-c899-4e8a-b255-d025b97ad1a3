import request from '@/utils/request'

// /system/ingestion/everdaylist
// dateLine
// 查询【请填写功能名称】列表
export function listIngestion(query) {
  return request({
    url: '/system/ingestion/everdaylist',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getIngestion(indexn) {
  return request({
    url: '/system/ingestion/' + indexn,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addIngestion(data) {
  return request({
    url: '/system/ingestion',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateIngestion(data) {
  return request({
    url: '/system/ingestion',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delIngestion(indexn) {
  return request({
    url: '/system/ingestion/' + indexn,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportIngestion(query) {
  return request({
    url: '/system/ingestion/export',
    method: 'get',
    params: query
  })
}