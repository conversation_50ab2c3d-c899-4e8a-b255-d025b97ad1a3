<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="选择日期" prop="enddtime">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.enddtime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择当前日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="行为" prop="msg">
        <!-- <el-select v-model="queryParams.msg" placeholder="请选择进放料行为">
          <el-option
            v-for="dict in towerInOutList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
            @keyup.enter.native="handleQuery"
          ></el-option>
        </el-select> -->
        <el-radio v-model="queryParams.msg" label="in">加料</el-radio>
        <el-radio v-model="queryParams.msg" label="out">放料</el-radio>
      </el-form-item>

      <!-- <el-form-item label="网络" prop="network">
          <el-input
            v-model="queryParams.network"
            placeholder="请输入网络"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="网关id" prop="switchid">
          <el-input
            v-model="queryParams.switchid"
            placeholder="请输入网关id"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> 
        <el-form-item label="备注" prop="text">
          <el-input
            v-model="queryParams.text"
            placeholder="请输入备注"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>-->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:dayreport:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="loucengList">
      <!-- <el-table-column type="selection" width="55" align="center" />        @selection-change="handleSelectionChange"-->
      <el-table-column label="饲养单元名称" align="center" prop="danyuanname" />
      <el-table-column label="料塔名称" align="center" prop="slavename" />
      <el-table-column label="单位" align="center" prop="danwei">
        <span>吨</span>
      </el-table-column>
      <el-table-column label="饲料类型" align="center" prop="feedtype" />
      <el-table-column label="打料量(T)" align="center" prop="dayweight" />
      <!-- 进料放料 -->
      <!-- <el-table-column
        label="行为"
        align="center"
        prop="msg"
        :formatter="towerInOutListFormat"
      /> -->
      <el-table-column label="日期" align="center" prop="datetime">
        <template slot-scope="scope">
          <!-- {{ scope.row.datetime | formatUTC }} -->
          <span>{{ parseTime(scope.row.datetime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
  
  <script>
import { listDayreport, exportDayreport } from "@/api/liaota/dayreport";
import { formatDay } from "@/utils";

export default {
  name: "Dayreport",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      towerInOutList: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【楼层用料数据】表格数据
      loucengList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: null,
        pageSize: null,
        enddtime: null,
        msg: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    var preDate = formatDay(new Date().getTime());
    this.queryParams.enddtime = preDate;
    this.getDicts("tower_in_out").then((response) => {
      this.towerInOutList = response.data;
      this.queryParams.msg =
        this.towerInOutList && this.towerInOutList[0].dictValue;
      this.getList();
    });
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  methods: {
    towerInOutListFormat(row, column) {
      return this.selectDictLabel(this.towerInOutList, row.msg);
    },
    /** 查询【楼层用料数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;

      listDayreport(this.queryParams).then((response) => {
        this.loucengList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【打料统计日报】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportDayreport(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>