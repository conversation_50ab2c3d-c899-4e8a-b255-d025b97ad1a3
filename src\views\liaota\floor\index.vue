<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
        label-width="68px"
    >
      <el-form-item :label="$t('liaota.floor')" prop="homename">
        <el-input
          v-model="queryParams.homename"
          :placeholder="$t('liaota.enterFloor')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('huanKong.pigFarmID')" prop="mfactory">
        <el-input
          v-model="queryParams.mfactory"
          :placeholder="$t('huanKong.choosePigFarmID')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="猪场id" prop="colname">
        <el-input
          v-model="queryParams.colname"
          placeholder="请输入猪场id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:home:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:home:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:home:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:home:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="homeList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('liaota.floorId')"
        align="center"
        prop="id"
      />
      <el-table-column
        :label="$t('liaota.floor')"
        align="center"
        prop="homename"
      />
      <el-table-column
        :label="$t('huanKong.pigFarmID')"
        align="center"
        prop="mfactory"
      />
      <!-- <el-table-column label="猪场id" align="center" prop="colname" /> -->
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:home:edit']"
            >{{ $t("common.update") }}</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:home:remove']"
            >{{ $t("common.delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules"   label-width="68px">
        <el-form-item :label="$t('liaota.floor')" prop="homename">
          <el-input
            v-model="form.homename"
            :placeholder="$t('liaota.enterFloor')"
          />
        </el-form-item>
        <el-form-item :label="$t('huanKong.pigFarmID')" prop="mfactory">
          <el-input
            v-model="form.mfactory"
            :placeholder="$t('huanKong.choosePigFarmID')"
            disabled
          />
        </el-form-item>
        <!-- <el-form-item label="猪场id" prop="colname">
          <el-input v-model="form.colname" placeholder="请输入猪场id" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHome,
  getHome,
  delHome,
  addHome,
  updateHome,
  exportHome,
} from "@/api/liaota/floor";

export default {
  name: "Home",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      homeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        homename: null,
        mfactory: null,
        colname: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        homename: [
          {
            required: true,
            message: this.$t("liaota.floorNotNull"),
            trigger: "blur",
          },
        ],
        mfactory: [
          {
            required: true,
            message: this.$t("liaota.floorIdNotNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listHome(this.queryParams).then((response) => {
        this.homeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        homename: null,
        mfactory: this.$store.state.settings.nowPigFarm,
        colname: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("liaota.addfloor");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getHome(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("liaota.modfloor");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateHome(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addHome(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("liaota.sureCancelfloor") +
          `"` +
          ids +
          `"` +
          this.$t("huanKong.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delHome(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("liaota.sureExportfloor"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportHome(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>
