<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <span v-if="this.activeName == 'second'">
        <el-form-item label="选择日期" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            size="small"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @keyup.enter.native="handleQuery"
          ></el-date-picker>
        </el-form-item>
      </span>
      <span v-if="this.activeName == 'first'">
        <el-form-item label="楼层" prop="floor">
          <el-select
            v-model="queryParams.floor"
            :placeholder="$t('liaota.enterFloor')"
            clearable
            size="small"
          >
            <el-option
              v-for="item in floorList"
              :key="item.id"
              :label="item.homename"
              :value="item.homename"
              @keyup.enter.native="handleQuery"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="打料时间" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="queryParams.uptime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择打料时间"
          >
          </el-date-picker>
        </el-form-item>
      </span>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        :label="$t('liaota.floorDataStatistics')"
        name="second"
        :lazy="true"
      > -->
    <el-row :gutter="30" style="margin-top: 25px">
      <el-col
        :xs="24"
        :sm="24"
        :md="12"
        :lg="6"
        v-for="(item, index) in floorNewList"
        v-bind:key="index"
      >
        <el-card body-style="padding:5px 10px" class="card-box">
          <el-row style="display: flex">
            <el-col :span="11">
              <div style="color: #8492a6; font-size: 20px">
                <strong style="font-size: 30px; color: #5a5e66">{{
                  item.homename
                }}</strong
                >层实时用料
              </div>
              <el-image
                style="width: 60%; margin: 20px"
                :src="require('@/assets/icons/liaota/floor.png')"
              ></el-image>
            </el-col>
            <el-col :span="1">
              <el-divider
                style="height: 100%"
                direction="vertical"
              ></el-divider>
            </el-col>
            <el-col :span="12">
              <div class="text">
                <ul>
                  <li>
                    保育:
                    <span style="color: #26d8e0e0">{{ item.baoyu || 0 }}</span>
                    kg
                  </li>
                  <li>
                    育肥:
                    <span style="color: #26d8e0e0">{{ item.yufei || 0 }}</span>
                    kg
                  </li>
                  <li>
                    分娩:
                    <span style="color: #26d8e0e0">{{
                      item.fenmian || 0
                    }}</span>
                    kg
                  </li>
                  <li>
                    妊娠:
                    <span style="color: #26d8e0e0">{{
                      item.rensheng || 0
                    }}</span>
                    kg
                  </li>
                </ul>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <!-- </el-tab-pane> -->
    <!-- <el-tab-pane
        :label="$t('liaota.floorDataTable')"
        name="first"
        :lazy="true"
      >
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:louceng:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:louceng:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:louceng:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:louceng:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="loucengList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="网关id" align="center" prop="id" />
          <el-table-column label="楼层" align="center" prop="floor" />
          <el-table-column
            label="打料时间"
            align="center"
            prop="uptime"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.uptime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="保育" align="center" prop="baoyu" />
          <el-table-column label="育肥" align="center" prop="yufei" />
          <el-table-column label="分娩" align="center" prop="fenmian" />
          <el-table-column label="妊娠" align="center" prop="rensheng" />
          <el-table-column label="网络" align="center" prop="network" />
          <el-table-column label="网关id" align="center" prop="switchid" />
          <el-table-column label="备注" align="center" prop="text" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:louceng:edit']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:louceng:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane> -->
    <!-- </el-tabs> -->
    <!-- 添加或修改【料塔历史数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="楼层" prop="floor">
          <el-input v-model="form.floor" placeholder="请输入楼层" />
        </el-form-item>
        <el-form-item label="打料时间" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.uptime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择打料时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保育" prop="baoyu">
          <el-input v-model="form.baoyu" placeholder="请输入保育" />
        </el-form-item>
        <el-form-item label="育肥" prop="yufei">
          <el-input v-model="form.yufei" placeholder="请输入育肥" />
        </el-form-item>
        <el-form-item label="分娩" prop="fenmian">
          <el-input v-model="form.fenmian" placeholder="请输入分娩" />
        </el-form-item>
        <el-form-item label="妊娠" prop="rensheng">
          <el-input v-model="form.rensheng" placeholder="请输入妊娠" />
        </el-form-item>
        <el-form-item label="网络" prop="network">
          <el-input v-model="form.network" placeholder="请输入网络" />
        </el-form-item>
        <el-form-item label="网关id" prop="switchid">
          <el-input v-model="form.switchid" placeholder="请输入网关id" />
        </el-form-item>
        <el-form-item label="备注" prop="text">
          <el-input v-model="form.text" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listLouceng,
  getLouceng,
  addLouceng,
  updateLouceng,
  delLouceng,
  exportLouceng,
  listLoucengDate,
} from "@/api/liaota/floorStatisticsData";
import { listHome } from "@/api/liaota/floor";
// import { isEmpty } from "lodash";

export default {
  name: "Slave",
  components: {},
  data() {
    return {
      inSum: 0,
      outSum: 0,
      inSumTimes: 0,
      outSumTimes: 0,
      activeName: "second",
      //料塔值集
      slaveOptions: [],
      //设备状态
      slavenetworkList: [],
      //加放料
      towerInOutList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【料塔历史数据】表格数据
      loucengList: [],
      // 【料塔进放料数据】表格数据
      floorStatisticsDataList: [],
      //楼层
      floorList: [],
      floorNewList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        floor: null,
        uptime: null,
        baoyu: null,
        yufei: null,
        fenmian: null,
        rensheng: null,
        double1: null,
        double2: null,
        double3: null,
        double4: null,
        network: null,
        switchid: null,
        text: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  mounted() {
    this.getlistHome();
  },
  created() {
    this.getlistHome();
    this.getList();
    this.getDicts("hk_work_status").then((response) => {
      this.slavenetworkList = response.data;
    });
    this.getDicts("tower_in_out").then((response) => {
      this.towerInOutList = response.data;
    });
  },

  methods: {
    getlistHome() {
      listHome({ mfactory: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.floorList = response.rows;
        }
      );
    },
    changeHomename(value) {
      this.form.homename = this.floorListFormat(value);
    },
    floorListFormat(value) {
      let arrObjFilter = this.floorList
        .filter((ele) => ele.id == value)
        .map((ele) => {
          return ele.homename;
        });
      return arrObjFilter[0];
    },
    // 字典状态字典翻译
    towerInOutListFormat(row, column) {
      return this.selectDictLabel(this.towerInOutList, row.inorout);
    },
    handleClick(tab, event) {
      if (tab.name == "first") this.getList();
      if (tab.name == "second") this.getList();
    },
    /** 查询【料塔历史数据】列表 */
    async getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      if (this.activeName == "first") {
        listLouceng(this.queryParams).then((response) => {
          this.loucengList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else if (this.activeName == "second") {
        await listHome({
          mfactory: this.$store.state.settings.nowPigFarm,
        }).then((response) => {
          this.floorList = response.rows;
        });
        listLoucengDate(
          this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
        ).then((response) => {
          this.floorStatisticsDataList = response.rows;

          this.floorStatisticsDataList.map((i) => {
            this.floorList.map((item) => {
              if (item.homename == i.floor) {
                let { id, ...other } = i;
                Object.assign(item, other);
              }
            });
          });
          this.floorNewList = this.floorList;

          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        slaveid: null,
        slavename: null,
        switchid: null,
        homeid: null,
        uptime: null,
        slavenetwork: null,
        weight: null,
        alarmmsg: null,
        inorout: null,
        homename: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;

      if (this.activeName == "first") {
        this.title = this.$t("liaota.addfloorTableData");
      } else if (this.activeName == "second") {
        this.title = this.$t("liaota.addfloorTableData");
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      if (this.activeName == "first") {
        getLouceng(id).then((response) => {
          this.form = response.data;
          this.open = true;
          this.title = this.$t("liaota.modfloorTableData");
        });
      } else if (this.activeName == "second") {
        getLouceng(id).then((response) => {
          this.form = response.data;
          this.open = true;
          this.title = this.$t("liaota.modfloorTableData");
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      if (this.activeName == "first") {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            if (this.form.id != null) {
              updateLouceng(this.form).then((response) => {
                this.msgSuccess(this.$t("common.modifiedSuccess"));
                this.open = false;
                this.getList();
              });
            } else {
              this.form.mfactory = this.$store.state.settings.nowPigFarm;
              addLouceng(this.form).then((response) => {
                this.msgSuccess(this.$t("common.addSuccess"));
                this.open = false;
                this.getList();
              });
            }
          }
        });
      } else if (this.activeName == "second") {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            if (this.form.id != null) {
              updateLouceng(this.form).then((response) => {
                this.msgSuccess(this.$t("common.modifiedSuccess"));
                this.open = false;
                this.getList();
              });
            } else {
              this.form.mfactory = this.$store.state.settings.nowPigFarm;
              addLouceng(this.form).then((response) => {
                this.msgSuccess(this.$t("common.addSuccess"));
                this.open = false;
                this.getList();
              });
            }
          }
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      if (this.activeName == "first") {
        this.$confirm(
          this.$t("liaota.sureCancelfloorTableData") +
            '"' +
            ids +
            '"' +
            this.$t("liaota.dataItem"),
          this.$t("common.warn"),
          {
            confirmButtonText: this.$t("common.determine"),
            cancelButtonText: this.$t("common.cancel"),
            type: "warning",
          }
        )
          .then(function () {
            return delLouceng(ids);
          })
          .then(() => {
            this.getList();
            this.msgSuccess(this.$t("common.deleteSuccess"));
          });
      } else if (this.activeName == "second") {
        this.$confirm(
          this.$t("liaota.sureCancelfloorTableData") +
            '"' +
            ids +
            '"' +
            this.$t("liaota.dataItem"),
          this.$t("common.warn"),
          {
            confirmButtonText: this.$t("common.determine"),
            cancelButtonText: this.$t("common.cancel"),
            type: "warning",
          }
        )
          .then(function () {
            return delLouceng(ids);
          })
          .then(() => {
            this.getList();
            this.msgSuccess(this.$t("common.deleteSuccess"));
          });
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      if (this.activeName == "first") {
        this.$confirm(
          this.$t("liaota.sureExportfloorTableData"),
          this.$t("common.warn"),
          {
            confirmButtonText: this.$t("common.determine"),
            cancelButtonText: this.$t("common.cancel"),
            type: "warning",
          }
        )
          .then(function () {
            return exportLouceng(queryParams);
          })
          .then((response) => {
            this.download(response.msg);
          });
      } else if (this.activeName == "second") {
        this.$confirm(
          this.$t("liaota.sureExportfloorTableData"),
          this.$t("common.warn"),
          {
            confirmButtonText: this.$t("common.determine"),
            cancelButtonText: this.$t("common.cancel"),
            type: "warning",
          }
        )
          .then(function () {
            return exportLouceng(queryParams);
          })
          .then((response) => {
            this.download(response.msg);
          });
      }
    },
  },
};
</script>
<style scoped>
.box {
  border-radius: 6px;
  height: 200px;
  color: #fff;
  font-size: 25px;
  box-sizing: border-box;
  margin: 20px 0px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  background-color: #1ecd98;
}
ul,
li {
  list-style: none;
  padding: 5px 10px;
  margin: 0px;
}

.el-divider--vertical {
  height: 100%;
}
.box1 {
  background-color: #1ecd98;
}
.box2 {
  background-color: #409eff;
}
.box3 {
  background-color: #e6a23c;
}
.box4 {
  background-color: #f56c6c;
}
.text {
  /* padding: 20px 35px; */
  font-size: 20px;
  text-align: left;
  color: #8492a6;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}
.el-card__body {
  padding: 5px 20px 5px;
}
</style>
