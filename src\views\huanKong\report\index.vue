<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('huanKong.pigHouseUnit')" prop="value">
        <el-cascader
          :required="true"
          v-model="queryParams.value"
          :options="options"
          size="small"
          @change="handleNodeClick"
          :placeholder="$t('huanKong.choosePigHouse')"
        ></el-cascader>
      </el-form-item>
      <el-form-item :label="$t('huanKong.selectDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('huanKong.startDate')"
          :end-placeholder="$t('huanKong.endDate')"
          @change="handleDateClick"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('huanKong.period')" prop="dayLine">
        <el-select
          v-model="queryParams.dayLine"
          :placeholder="$t('huanKong.choosePeriod')"
          @change="handleDateClick"
        >
          <el-option
            v-for="dict in dateLineList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <!-- <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        > -->
      </el-form-item>
    </el-form>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        :label="$t('huanKong.temperature')"
        name="first"
        :lazy="true"
      >
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div
            ref="category1"
            id="category1"
            style="height: 500px"
            v-if="'first' === activeName"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('huanKong.humidity')" name="second" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div
            ref="category2"
            id="category2"
            style="height: 500px"
            v-if="'second' === activeName"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('huanKong.waterLevel')" name="third" :lazy="true">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <div
            ref="category3"
            id="category3"
            style="height: 500px"
            v-if="'third' === activeName"
          />
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane
        :label="$t('huanKong.energyUsed')"
        name="fourth"
        :lazy="true"
      >
        <div class="el-table el-table--enable- row-hover el-table--medium">
          <div
            ref="category4"
            id="category4"
            style="height: 500px"
            v-if="'fourth' === activeName"
          />
        </div>
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import { listHistory } from "@/api/huanKong/history";
import { treeselectEnv } from "@/api/huanKong/slave";
import * as echarts from "echarts";
require("@/utils/walden"); // echarts theme

export default {
  name: "report",
  components: {},
  data() {
    return {
      dateLineList: [],
      activeName: "first",
      options: null,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      //温度节点
      category1: null,
      //温度数据
      temperatureList: [],
      //温度x轴数据
      xAxisList: [],
      //温度y轴数据
      yAxisList: [],

      //湿度节点
      category2: null,
      //湿度数据
      humidityList: [],
      //湿度x轴数据
      xAxisList2: [],
      //湿度y轴数据
      yAxisList2: [],

      //水位节点
      category3: null,
      //水位数据
      waterList: [],
      //水位x轴数据
      xAxisList3: [],
      //水位y轴数据
      yAxisList3: [],

      //用电量节点
      category4: null,
      //用电量数据
      electricList: [],
      //用电量x轴数据
      xAxisList4: [],
      //用电量y轴数据
      yAxisList4: [],

      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 弹出层标题
      title: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: null,
        id: null,
        homeid: null,
        dayLine: null,
        value: [],
        dateRange: [],
      },
    };
  },
  // watch: {
  //   queryParams: {
  //     handler() {
  //       if (this.activeName == "first") this.getList1();
  //       if (this.activeName == "second") this.getList2();
  //       if (this.activeName == "third") this.getList3();
  //       if (this.activeName == "fourth") this.getList4();
  //     },
  //     deep: true,
  //   },
  // },
  mounted() {
    this.getTree();
  },
  created() {
    this.getDicts("hk_date_duan").then((response) => {
      this.dateLineList = response.data;
      this.queryParams.dayLine =
        this.dateLineList && this.dateLineList[0].dictValue;
    });
    // if (this.queryParams.slaveid) {
    //   this.getList1();
    // }
    // this.getList1();
    window.onresize = () => {
      //alert("sss");
      this.category1.resize(); //重新初始化echarts
      this.category2.resize(); //重新初始化echarts
      this.category3.resize(); //重新初始化echarts
      this.category4.resize(); //重新初始化echarts
    };
  },
  watch: {
    "$i18n.locale"(newValue) {
      // 不需要重新设置配置项，只需要手动触发一下setOption()
      this.handleQuery();
    },
  },

  methods: {
    handleClick(tab, event) {
      if (tab.name == "first") this.getList1();
      if (tab.name == "second") this.getList2();
      if (tab.name == "third") this.getList3();
      if (tab.name == "fourth") {
        this.getList4();
        // this.$nextTick()在页面交互，尤其是从后台获取数据后重新生成dom对象之后的操作有很大的优势
        // this.$nextTick()将回调延迟到下次 DOM 更新循环之后执行。在修改数据之后立即使用它，然后等待 DOM 更新。它跟全局方法 Vue.nextTick 一样，不同的是回调的 this 自动绑定到调用它的实例上。
        // this.$nextTick(() => {
        //   this.category4.resize();
        // });
      }
    },
    // 表单重置
    reset() {
      this.form = {
        nindex: null,
        date: null,
        pigNum: null,
        nIngestionSSum: null,
        feedSum: null,
        nSecondSSum: null,
        weightAvg: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.activeName == "first") this.getList1();
      if (this.activeName == "second") this.getList2();
      if (this.activeName == "third") this.getList3();
      if (this.activeName == "fourth") this.getList4();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询【请填写功能名称】列表 */
    getTree() {
      treeselectEnv({ facid: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.options = response.data;
          this.queryParams.value = [
            this.options[0].value,
            this.options[0].children[0].value,
          ];
          this.queryParams.slaveid = this.options[0].children[0].value;
          this.queryParams.homeid = this.options[0].value;
          this.queryParams.facid = this.$store.state.settings.nowPigFarm;
          this.getList1();
        }
      );
    },
    // 节点单击事件
    handleNodeClick(data) {
      if (this.queryParams) {
        this.queryParams.slaveid = data[1];
        this.queryParams.homeid = data[0];
        if (this.activeName == "first") this.getList1();
        if (this.activeName == "second") this.getList2();
        if (this.activeName == "third") this.getList3();
        if (this.activeName == "fourth") this.getList4();
      }
    },
    handleDateClick(data) {
      // Object.prototype.toString.call(a) === '[object Array]';//true
      if (Object.prototype.toString.call(data) === "[object Array]") {
        this.queryParams.dayLine = null;
      } else {
        this.queryParams.dateRange = null;
      }
      if (this.activeName == "first") this.getList1();
      if (this.activeName == "second") this.getList2();
      if (this.activeName == "third") this.getList3();
      if (this.activeName == "fourth") this.getList4();
    },
    /** 查询【请填写功能名称】列表 */
    getList1() {
      if (this.queryParams.homeid) {
        this.loading = true;
        this.queryParams.pageNum = 1;
        listHistory(
          this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
        ).then((response) => {
          this.loading = false;
          if (response && response.rows) {
            this.temperatureList = response.rows;
            this.xAxisList = [];
            this.yAxisList = [];
            this.temperatureList.forEach((element) => {
              this.xAxisList.push(element.uptime);
              this.yAxisList.push(element.temperature);
            });
          } else {
            this.temperatureList = [];
            this.xAxisList = [];
            this.yAxisList = [];
          }
          this.getDatas1();
        });
      }
    },

    getList2() {
      listHistory(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        if (response && response.rows) {
          this.humidityList = response.rows;
          this.xAxisList2 = [];
          this.yAxisList2 = [];
          this.humidityList.forEach((element) => {
            this.xAxisList2.push(element.uptime);
            this.yAxisList2.push(element.humidity);
          });
        } else {
          this.humidityList = [];
          this.xAxisList2 = [];
          this.yAxisList2 = [];
        }
        this.getDatas2();
      });
    },
    getList3() {
      listHistory(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        if (response && response.rows) {
          this.waterList = response.rows;
          this.xAxisList3 = [];
          this.yAxisList3 = [];
          this.waterList.forEach((element) => {
            this.xAxisList3.push(element.uptime);
            this.yAxisList3.push(element.water);
          });
        } else {
          this.waterList = [];
          this.xAxisList3 = [];
          this.yAxisList3 = [];
        }
        this.getDatas3();
      });
    },
    getList4() {
      listHistory(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        if (response && response.rows) {
          this.electricList = response.rows;
          this.xAxisList4 = [];
          this.yAxisList4 = [];
          this.electricList.forEach((element) => {
            this.xAxisList4.push(element.uptime);
            this.yAxisList4.push(element.temperature);
          });
        } else {
          this.electricList = [];
          this.xAxisList4 = [];
          this.yAxisList4 = [];
        }
        this.getDatas4();
      });
    },

    getDatas1() {
      this.category1 = echarts.init(this.$refs.category1, "walden");
      this.category1.setOption({
        // title: {
        //   text: "",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [`${this.$t("huanKong.temperature")}`],
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },

        xAxis: {
          type: "category",
          data: this.xAxisList.reverse(),
          nameLocation: "end", //坐标轴名称显示位置。
          // axisLabel: {
          //   // interale: 0,
          //   // rotate: -40, //设置日期显示样式（倾斜度）
          //   formatter: function (value) {
          //     //在这里写你需要的时间格式
          //     var t_date = new Date(value);
          //     return [
          //       t_date.getFullYear(),
          //       t_date.getMonth() + 1,
          //       t_date.getDate(),
          //     ].join("-");
          //     // + " " + [t_date.getHours(), t_date.getMinutes()].join(':'); 时分
          //   },
          // },
          // axisLine: {
          //   symbol: 'none',
          // },
          // boundaryGap: false,
        },
        yAxis: {
          type: "value",
          min: "0",
          max: "40",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },

            smooth: true,
            name: this.$t("huanKong.temperature"),
            type: "line",
            stack: "总量",
            // data: this.yAxisList,
            data: this.yAxisList.reverse(),
            // color: "#0bd2cb",
            // areaStyle: {
            //   color: {
            //     type: "linear",
            //     x: 0,
            //     y: 0,
            //     x2: 0,
            //     y2: 1,

            //     colorStops: [
            //       {
            //         offset: 0,
            //         color: "#0bd2cb", // 100% 处的颜色acf598
            //       },
            //       {
            //         offset: 1,
            //         color: "#96e6e8", //   0% 处的颜色0a861e
            //       },
            //     ],
            //     global: false, // 缺省为 false
            //   },
            // },
          },
        ],
      });
    },
    getDatas2() {
      this.category2 = echarts.init(this.$refs.category2, "walden");
      this.category2.setOption({
        // title: {
        //   text: "",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [`${this.$t("huanKong.humidity")}`],
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },

        xAxis: {
          type: "category",
          nameLocation: "end", //坐标轴名称显示位置。
          // axisLabel: {
          //   //坐标轴刻度标签的相关设置。
          //   interval: 8,
          //   rotate: "70",
          // },
          // axisLine: {
          //   symbol: 'none',
          // },
          // boundaryGap: false,
          data: this.xAxisList2.reverse(),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },

            smooth: true,
            name: this.$t("huanKong.humidity"),
            type: "line",
            stack: "总量",
            data: this.yAxisList2.reverse(),
            // color: "#0bd2cb",
            // areaStyle: {
            //   color: {
            //     type: "linear",
            //     x: 0,
            //     y: 0,
            //     x2: 0,
            //     y2: 1,

            //     colorStops: [
            //       {
            //         offset: 0,
            //         color: "#0bd2cb", // 100% 处的颜色acf598
            //       },
            //       {
            //         offset: 1,
            //         color: "#96e6e8", //   0% 处的颜色0a861e
            //       },
            //     ],
            //     global: false, // 缺省为 false
            //   },
            // },
          },
        ],
      });
    },
    getDatas3() {
      this.category3 = echarts.init(this.$refs.category3, "walden");
      this.category3.setOption({
        // title: {
        //   text: "",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [`${this.$t("huanKong.waterLevel")}`],
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },

        xAxis: {
          type: "category",
          nameLocation: "end", //坐标轴名称显示位置。
          // axisLabel: {
          //   //坐标轴刻度标签的相关设置。
          //   interval: 8,
          //   rotate: "70",
          // },
          // axisLine: {
          //   symbol: 'none',
          // },
          // boundaryGap: false,
          data: this.xAxisList3.reverse(),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },

            smooth: true,
            name: this.$t("huanKong.waterLevel"),
            type: "line",
            stack: "总量",
            data: this.yAxisList3.reverse(),
            // color: "#0bd2cb",
          },
        ],
      });
    },
    getDatas4() {
      this.category4 = echarts.init(this.$refs.category4, "walden");
      this.category4.setOption({
        // title: {
        //   text: "",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["用电量"],
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },

        xAxis: {
          type: "category",
          nameLocation: "end", //坐标轴名称显示位置。
          // axisLabel: {
          //   //坐标轴刻度标签的相关设置。
          //   interval: 8,
          //   rotate: "70",
          // },
          // axisLine: {
          //   symbol: 'none',
          // },
          // boundaryGap: false,
          data: this.xAxisList4.reverse(),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            symbol: "none", //取消折点圆圈
            markPoint: {
              data: [
                {
                  name: "最大值",
                  type: "max",
                },
                {
                  name: "最小值",
                  type: "min",
                },
              ],
            },

            smooth: true,
            name: "用电量",
            type: "line",
            stack: "总量",
            data: this.yAxisList4.reverse(),
            // color: "#0bd2cb",
            // areaStyle: {
            //   color: {
            //     type: "linear",
            //     x: 0,
            //     y: 0,
            //     x2: 0,
            //     y2: 1,

            //     colorStops: [
            //       {
            //         offset: 0,
            //         color: "#0bd2cb", // 100% 处的颜色acf598
            //       },
            //       {
            //         offset: 1,
            //         color: "#96e6e8", //   0% 处的颜色0a861e
            //       },
            //     ],
            //     global: false, // 缺省为 false
            //   },
            // },
          },
        ],
      });
    },
  },
};
</script>
