<template >
  <el-dialog
    title="切换当前猪场"
    :visible.sync="show"
    width="30%"
    :before-close="handleClose"
  >
    <slot></slot>
    <span class="dialog-footer">
      <el-button @click="show = false">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
  
</template>

<script>
export default {
  name: "SwitchPanel",
  computed: {
    show: {
      get() {
        return this.$store.state.settings.showSwitching;
      },
      set(val) {
        this.$store.dispatch("settings/changeSwitching", {
          key: "showSwitching",
          value: val,
        });
      },
    },
  },

  methods: {
    handleOk() {
      this.show = false;
      this.$store.dispatch("tagsView/delAllViews");
      this.$router.push({ path: "/index" });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
  },
};
</script>


