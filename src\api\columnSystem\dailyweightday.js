//存栏量 访问量     、、分栏站生长报告
import request from '@/utils/request'

// /system/dailyweightday/listDayByCunlan 存栏量日汇总
// /system/dailyweightday/listDayByRate 体重比日汇总
// /system/dailyweightday/listDayByFangwen 访问量日汇总





// /system/dailyweightday/listdays   日汇总日期查询
export function listdays(query) {
  return request({
    url: '/system/dailyweightday/listdays',
    method: 'get',
    params: query
  })
}

export function listDayByCunlan(query) {
  return request({
    url: '/system/dailyweightday/listDayByCunlan',
    method: 'post',
    data: query
  })
}
export function listDayByRate(query) {
  return request({
    url: '/system/dailyweightday/listDayByRate',
    method: 'post',
    data: query
  })
}
export function listDayByFangwen(query) {
  return request({
    url: '/system/dailyweightday/listDayByFangwen',
    method: 'post',
    data: query
  })
}



// 查询【请填写功能名称】列表
export function listDailyweightday(query) {
  return request({
    url: '/system/dailyweightday/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】列表
export function weightDaylist(query) {
  return request({
    url: '/system/dailyweightday/weightDaylist',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getDailyweightday(id) {
  return request({
    url: '/system/dailyweightday/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addDailyweightday(data) {
  return request({
    url: '/system/dailyweightday',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateDailyweightday(data) {
  return request({
    url: '/system/dailyweightday',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delDailyweightday(id) {
  return request({
    url: '/system/dailyweightday/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportDailyweightday(query) {
  return request({
    url: '/system/dailyweightday/export',
    method: 'get',
    params: query
  })
}
