<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="采食日期" prop="dateLine">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.dateLine"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择采食日期"
        >
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="采食日期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item> -->
      <el-form-item label="猪舍id" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          placeholder="请输入猪舍id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="栏号" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          placeholder="请输入栏号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="母猪耳缺号" prop="mid">
        <el-input
          v-model="queryParams.mid"
          placeholder="请输入母猪耳缺号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电子耳牌" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          placeholder="请输入电子耳牌"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="采食开始时间" prop="ndate">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.ndate"
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择采食开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="采食结束时间" prop="ndateout">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.ndateout"
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择采食结束时间"
        >
        </el-date-picker>
      </el-form-item> -->

      <!-- <el-form-item label="饲料剩余量>=" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          placeholder="请输入饲料剩余量>="
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:ingestion:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:ingestion:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:ingestion:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:ingestion:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="ingestionList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="indexn" />
      <el-table-column label="母猪耳缺号" align="center" prop="mid" />
      <el-table-column label="电子耳牌" align="center" prop="mrfid" />
      <el-table-column label="猪舍id" align="center" prop="mdorm" />
      <el-table-column label="栏号" align="center" prop="nindex" />
      <el-table-column label="饲喂站名称" align="center" prop="mname" />
      <el-table-column label="饲喂曲线" align="center" prop="ncurveindex" />
      <el-table-column label="体况" align="center" prop="ncondition" />
      <el-table-column label="应饲喂量" align="center" prop="nfeedall" />
      <el-table-column label="本次饲喂量" align="center" prop="nfeedsur" />
      <el-table-column label="已饲喂量" align="center" prop="nfeedcur" />
      <el-table-column
        label="采食开始时间"
        align="center"
        prop="ndate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ndate, "{y}-{m}-{d} {h}:{i}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="采食结束时间"
        align="center"
        prop="ndateout"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.ndateout, "{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column label="胎次" align="center" prop="nparity" />
      <el-table-column label="生产状态" align="center" prop="nstate" />
      <el-table-column label="下料次数" align="center" prop="nnum" />
      <el-table-column label="已下料次数" align="center" prop="nnum1" />
      <el-table-column label="状态" align="center" prop="ntype" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:ingestion:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:ingestion:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="猪场id" prop="mfactory">
          <el-input v-model="form.mfactory" placeholder="请输入猪场id" />
        </el-form-item>
        <el-form-item label="母猪耳缺号" prop="mid">
          <el-input v-model="form.mid" placeholder="请输入母猪耳缺号" />
        </el-form-item>
        <el-form-item label="电子耳牌" prop="mrfid">
          <el-input v-model="form.mrfid" placeholder="请输入电子耳牌" />
        </el-form-item>
        <el-form-item label="采食开始时间" prop="ndate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ndate"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择采食开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="采食结束时间" prop="ndateout">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ndateout"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择采食结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="胎次" prop="nparity">
          <el-input v-model="form.nparity" placeholder="请输入胎次" />
        </el-form-item>
        <el-form-item label="应饲喂量" prop="nfeedall">
          <el-input v-model="form.nfeedall" placeholder="请输入应饲喂量" />
        </el-form-item>
        <el-form-item label="本次饲喂量" prop="nfeedsur">
          <el-input v-model="form.nfeedsur" placeholder="请输入本次饲喂量" />
        </el-form-item>
        <el-form-item label="已饲喂量" prop="nfeedcur">
          <el-input v-model="form.nfeedcur" placeholder="请输入已饲喂量" />
        </el-form-item>
        <el-form-item label="饲喂站名称" prop="mname">
          <el-input v-model="form.mname" placeholder="请输入饲喂站名称" />
        </el-form-item>
        <el-form-item label="猪舍id" prop="mdorm">
          <el-input v-model="form.mdorm" placeholder="请输入猪舍id" />
        </el-form-item>
        <el-form-item label="栏号" prop="nindex">
          <el-input v-model="form.nindex" placeholder="请输入栏号" />
        </el-form-item>
        <el-form-item label="饲喂曲线" prop="ncurveindex">
          <el-input v-model="form.ncurveindex" placeholder="请输入饲喂曲线" />
        </el-form-item>
        <el-form-item label="体况" prop="ncondition">
          <el-input v-model="form.ncondition" placeholder="请输入体况" />
        </el-form-item>
        <el-form-item label="生产状态" prop="nstate">
          <el-input v-model="form.nstate" placeholder="请输入生产状态" />
        </el-form-item>
        <el-form-item label="下料次数" prop="nnum">
          <el-input v-model="form.nnum" placeholder="请输入下料次数" />
        </el-form-item>
        <el-form-item label="已下料次数" prop="nnum1">
          <el-input v-model="form.nnum1" placeholder="请输入已下料次数" />
        </el-form-item>
        <el-form-item label="状态" prop="ntype">
          <el-select v-model="form.ntype" placeholder="请选择状态">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import {
  listIngestion,
  getIngestion,
  delIngestion,
  addIngestion,
  updateIngestion,
  exportIngestion,
} from "@/api/sowfeed/ingestion";
import { formatDay } from "@/utils";

export default {
  name: "Ingestion",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      ingestionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        mid: null,
        mrfid: null,
        ndate: null,
        dateLine: null,
        ndateout: null,
        nparity: null,
        nfeedall: null,
        nfeedsur: null,
        nfeedcur: null,
        mname: null,
        mdorm: null,
        nindex: null,
        ncurveindex: null,
        ncondition: null,
        nstate: null,
        nnum: null,
        nnum1: null,
        ntype: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mfactory: [
          { required: true, message: "猪场id不能为空", trigger: "blur" },
        ],
        mrfid: [
          { required: true, message: "电子耳牌不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    var preDate = formatDay(new Date().getTime() - 24 * 60 * 60 * 1000);
    this.queryParams.dateLine = preDate;
    this.getList();
  },
  methods: {
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;

      listIngestion(
        this.queryParams
        // this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        this.ingestionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        mfactory: null,
        mid: null,
        mrfid: null,
        ndate: null,
        ndateout: null,
        nparity: null,
        nfeedall: null,
        nfeedsur: null,
        nfeedcur: null,
        mname: null,
        mdorm: null,
        nindex: null,
        ncurveindex: null,
        ncondition: null,
        nstate: null,
        nnum: null,
        nnum1: null,
        ntype: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【请填写功能名称】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.indexn || this.ids;
      getIngestion(indexn).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【请填写功能名称】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.indexn != null) {
            updateIngestion(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addIngestion(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        '是否确认删除【请填写功能名称】编号为"' + indexns + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delIngestion(indexns);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【请填写功能名称】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportIngestion(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>