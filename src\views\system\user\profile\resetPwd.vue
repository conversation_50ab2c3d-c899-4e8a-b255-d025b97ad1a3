<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="80px">
    <el-form-item :label="$t('menu.oldPassword')" prop="oldPassword">
      <el-input v-model="user.oldPassword" :placeholder="$t('menu.enterOldPassword')" type="password" />
    </el-form-item>
    <el-form-item :label="$t('menu.newPassword')" prop="newPassword">
      <el-input v-model="user.newPassword" :placeholder="$t('menu.enterNewPassword')" type="password" />
    </el-form-item>
    <el-form-item :label="$t('menu.confirmPassword')" prop="confirmPassword">
      <el-input v-model="user.confirmPassword" :placeholder="$t('menu.enterConfirmPassword')" type="password" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">{{$t('common.save')}}</el-button>
      <el-button type="danger" size="mini" @click="close">{{$t('common.close')}}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserPwd } from "@/api/system/user";

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error(this.$t('menu.sureTwoEnterMatch')));
      } else {
        callback();
      }
    };
    return {
      test: "1test",
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: this.$t('menu.oldPasswordCannotBeEmpty'), trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: this.$t('menu.newPasswordCannotBeEmpty'), trigger: "blur" },
          { min: 6, max: 20, message: this.$t('menu.lengthBetween'), trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message:this.$t('menu.confirmPasswordCannotBeEmpty'), trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(
            response => {
              this.msgSuccess(this.$t('common.addSuccess'));
            }
          );
        }
      });
    },
    close() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/index" });
    }
  }
};
</script>
