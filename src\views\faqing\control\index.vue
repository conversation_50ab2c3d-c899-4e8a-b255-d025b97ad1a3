
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="猪舍号" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          placeholder="请输入猪舍号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="栏号" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          placeholder="请输入栏号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发情测定站" prop="mname">
        <el-input
          v-model="queryParams.mname"
          placeholder="请输入发情测定站"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="3" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入猪舍名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="21" :xs="24">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['columnSystem:controldaily:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['columnSystem:controldaily:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['columnSystem:controldaily:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['columnSystem:controldaily:export']"
                >导出</el-button
              > -->
          </el-col>

          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
          <el-col :span="1.5" style="margin: 0 auto">
            <el-tooltip content="只显示控制器" placement="top">
              <el-switch
                style="
                  position: absolute;
                  top: 50%;
                  transform: translateY(-50%);
                "
                v-model="viewSwitch"
                active-color="#409eff"
                inactive-color="#dcdfe6"
                active-value="3"
                inactive-value="0"
                @change="changeSwitch"
              >
              </el-switch>
            </el-tooltip>
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="controldailyList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="拓扑节点类型" align="center" prop="ntype" />
          <el-table-column label="猪舍号" align="center" prop="mdorm" />
          <el-table-column label="栏号" align="center" prop="nindex" />
          <el-table-column label="发情测定站" align="center" prop="mname" />
          <el-table-column label="设备地址" align="center" prop="naddress" />
          <el-table-column label="设备状态" align="center" prop="nstatus" />
          <el-table-column label="软件版本" align="center" prop="nversion" />
          <el-table-column label="备注" align="center" prop="tag" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['columnSystem:controldaily:edit']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['columnSystem:controldaily:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="110px">
        <el-row>
          <el-form-item label="拓扑节点类型" prop="ntype" :rules="rules.ntype">
            <el-select
              v-model="form.ntype"
              placeholder="请选择拓扑节点类型"
              @change="changeNtype"
            >
              <el-option
                v-for="item in ntypeOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="猪舍号" prop="mdorm" :rules="rules.mdorm">
            <el-input
              v-model="form.mdorm"
              placeholder="请输入猪舍号"
              type="number"
            />
          </el-form-item>

          <el-form-item
            label="栏号"
            prop="nindex"
            :rules="form.ntype === 1 ? [{ required: false }] : rules.nindex"
          >
            <el-input
              v-model="form.nindex"
              placeholder="请输入栏号"
              :disabled="form.ntype === 1"
              type="number"
            />
          </el-form-item>

          <el-form-item
            label="发情测定站"
            prop="mname"
            type="number"
            :rules="
              form.ntype === 1 || form.ntype === 2
                ? [{ required: false }]
                : rules.mname
            "
          >
            <el-input
              v-model="form.mname"
              placeholder="请输入发情测定站"
              :disabled="form.ntype === 1 || form.ntype === 2"
            />
          </el-form-item>

          <el-form-item
            label="设备地址"
            prop="naddress"
            v-show="form.ntype === 3"
          >
            <el-input v-model="form.naddress" placeholder="请输入设备地址" />
          </el-form-item>

          <el-form-item
            label="设备状态"
            prop="nstatus"
            v-show="form.ntype === 3"
          >
            <el-radio-group v-model="form.nstatus">
              <el-radio
                v-for="dict in statusOptions"
                :key="dict.dictValue"
                :label="dict.dictValue"
                >{{ dict.dictLabel }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item
            label="软件版本"
            prop="nversion"
            v-show="form.ntype === 3"
          >
            <el-input v-model="form.nversion" placeholder="请输入软件版本" />
          </el-form-item>

          <el-form-item label="备注" prop="tag" v-show="form.ntype === 3">
            <el-input v-model="form.tag" placeholder="请输入备注" />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import { treeselect } from "@/api/columnSystem/overview";
import {
  listControl,
  getControl,
  delControl,
  addControl,
  updateControl,
  exportControl,
} from "@/api/faqing/control";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Controldaily",
  components: {},
  data() {
    return {
      viewSwitch: "3",
      ntypeOptions: [
        {
          value: 1,
          name: "猪舍号",
        },
        {
          value: 2,
          name: "栏号",
        },
        {
          value: 3,
          name: "发情测定站",
        },
      ],
      // 状态数据字典
      statusOptions: [
        {
          dictValue: 0,
          dictLabel: "停用",
        },
        {
          dictValue: 1,
          dictLabel: "正常",
        },
      ],
      deptName: undefined,
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 猪舍树选项
      deptOptions: undefined,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      nowId: "",
      // 类型
      ruleType: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 【控制器配置】表格数据
      controldailyList: [],
      // 弹出层标题
      title: "",
      submitLoading: false,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        ntype: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nserial: null,
        nstatus: null,
        nversion: null,
        tag: null,
      },
      // 表单参数
      form: {},
      rules: {
        ntype: [
          { required: true, message: "拓扑节点类型不能为空", trigger: "blur" },
        ],
        mdorm: [{ required: true, message: "猪舍号不能为空", trigger: "blur" }],
        nindex: [
          {
            required: true,
            message: "栏号不能为空",
            trigger: "blur",
          },
        ],
        mname: [
          {
            required: true,
            message: "发情测定站不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },

  created() {
    this.getList();
    this.getTreeselect();
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  beforeDestroy() {},
  methods: {
    changeNtype(val) {
      if (val === 1) {
        this.form.nindex = null;
        this.form.mname = null;
      } else if (val === 2) {
        this.form.mname = null;
      }
    },
    /** 查询【控制器配置】列表 */
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.nstatus);
    },
    // 节点单击事件
    handleNodeClick(data) {
      // this.queryParams.pageNum = 1;
      // this.queryParams.indexn = data.id;
      // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      // this.getList();
      this.queryParams.mname = null;
      this.queryParams.nindex = null;
      this.queryParams.mdorm = null;
      listControl({
        pageNum: 1,
        indexn: data.id,
        mfactory: this.$store.state.settings.nowPigFarm,
      }).then((response) => {
        // ntype类型  mdorm猪舍号 nindex栏号 mname发情测定站
        this.queryParams.pageNum = 1;
        this.queryParams.indexn = data.id;
        this.queryParams.ntype = response.rows[0].ntype;
        if (response.rows && response.rows[0].ntype == 3) {
          this.queryParams.mname = response.rows[0].mname;
          this.queryParams.nindex = response.rows[0].nindex;
          this.queryParams.mdorm = response.rows[0].mdorm;
        } else if (response.rows && response.rows[0].ntype == 2) {
          this.queryParams.nindex = response.rows[0].nindex;
          this.queryParams.mdorm = response.rows[0].mdorm;
        } else if (response.rows && response.rows[0].ntype == 1) {
          this.queryParams.mdorm = response.rows[0].mdorm;
        }
        this.getList();
        this.loading = false;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    /** 查询猪舍结构树 */
    getTreeselect() {
      treeselect({ mfactory: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.deptOptions = response.data;
        }
      );
    },
    //控制器配置
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      this.queryParams.ntype = this.viewSwitch == 3 ? 3 : null;
      listControl(this.queryParams).then((response) => {
        this.controldailyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        mfactory: null,
        ntype: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        nserial: null,
        nstatus: 0,
        nversion: null,
        nactivate: null,
        ntimeopen: null,
        ntimeclose: null,
        ntimereopen: null,
        ntimedelay: null,
        nweightdelay: null,
        nweighttime: null,
        nworktype: null,
        ndirect: null,
        nsceen: null,
        ntrend: null,
        nindivnull: null,
        nindivweight: null,
        nindiv: null,
        nweightstart: null,
        ngroupweight1: null,
        ngroupweight2: null,
        ngroupweight3: null,
        nmarketweight1: null,
        nmarketweight2: null,
        ncolumnpct: null,
        ncolumnweight: null,
        noffsetweight: null,
        nlightpct: null,
        nmidweight: null,
        nheavypct: null,
        tag: null,
        ngroupweight4: null,
        npignums: null,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.deptName = undefined;
      this.queryParams.indexn = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.ruleType = !(selection.length === 1 && selection[0].ntype === 3);

      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【控制器配置】";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.id || this.ids;
      getControl(indexn).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【控制器配置】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.submitLoading = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let nowForm = {
            ...this.form,
            mfactory: this.$store.state.settings.nowPigFarm,
          };

          if (this.form.indexn != null) {
            updateControl(nowForm).then((response) => {
              if (response) {
                this.msgSuccess("修改成功");
                this.submitLoading = false;
                this.open = false;
                this.getList();
                this.getTreeselect();
              } else {
                this.submitLoading = false;
              }
            });
            this.submitLoading = false;
          } else {
            addControl(nowForm).then((response) => {
              if (response) {
                this.msgSuccess("新增成功");
                this.submitLoading = false;
                // this.open = false;
                if (nowForm.ntype === 1) {
                  this.form.mdorm = Number(nowForm.mdorm) + 1;
                } else if (nowForm.ntype === 2) {
                  this.form.nindex = Number(nowForm.nindex) + 1;
                } else if (nowForm.ntype === 3) {
                  this.form.mname = Number(nowForm.mname) + 1;
                }
                this.getList();
                this.getTreeselect();
              } else {
                this.submitLoading = false;
              }
            });
            this.submitLoading = false;
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        '是否确认删除【控制器配置】编号为"' + indexns + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delControl(indexns);
        })
        .then(() => {
          this.getList();
          this.getTreeselect();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【控制器配置】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportControl(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    changeSwitch(val) {
      this.getList();
    },
  },
};
</script>
  