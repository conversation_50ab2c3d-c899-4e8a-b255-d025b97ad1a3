<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
        <span>
          <el-select
            v-model="queryParams.nindex"
            :placeholder="$t('common.pleaseChoose')"
            size="small"
          >
            <el-option
              v-for="item in nIndexOptions"
              :key="item.nindex"
              :label="item.nindex"
              :value="item.nindex"
              @keyup.enter.native="handleQuery"
            ></el-option>
          </el-select>

          <div
            style="
              position: relative;
              display: inline-block;
              width: 80px;
              height: 10px;
            "
          >
            <i
              :class="{ icon: iconShowSmall }"
              class="el-icon-caret-top"
              style="
                font-size: 22px;
                position: absolute;
                top: -16px;
                color: #c0c4cc;
              "
              @click="handleSmall"
            ></i>
            <i
              :class="{ icon: iconShowBig }"
              class="el-icon-caret-bottom"
              style="font-size: 22px; position: absolute; color: #c0c4cc"
              @click="handleBig"
            ></i>
          </div>
        </span>
      </el-form-item>
      <el-form-item :label="$t('boarMeasure.dateOfDetermination')" prop="ndate">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.ndate"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="$t('common.pleaseChoose')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['boarMeasure:dayReport:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['boarMeasure:dayReport:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['boarMeasure:dayReport:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['boarMeasure:dayReport:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-setting"
          size="mini"
          :disabled="multiple"
          @click="handleDayGather"
          v-hasPermi="['boarMeasure:dayReport:dayGather']"
          >{{ $t("boarMeasure.manualSummary") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-setting"
          size="mini"
          @click="handleDistribution"
          v-hasPermi="['boarMeasure:dayReport:distribution']"
          >{{ $t("boarMeasure.zeroearDistribution") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="measuredayList"
      @selection-change="handleSelectionChange"
      :cell-style="{ padding: '0' }"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('boarMeasure.nindex')"
        align="center"
        prop="nindex"
        width="70"
      />
      <el-table-column
        :label="$t('boarMeasure.mid')"
        align="center"
        prop="mid"
        width="180"
      />
      <el-table-column
        :label="$t('boarMeasure.mrfid')"
        align="center"
        width="150"
        prop="mrfid"
      >
        <template slot-scope="scope">
          <span
            :style="
              Number(scope.row.ningestions) <= 0
                ? 'color: #f05d18 '
                : 'color: black '
            "
          >
            {{ scope.row.mrfid }}</span
          >
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('boarMeasure.mdorm')" align="center" prop="mdorm" width="60" />
      <el-table-column :label="$t('boarMeasure.nindex')" align="center" prop="nindex" width="50" />
      <el-table-column :label="$t('boarMeasure.mname')" align="center" prop="mname" width="60" />
      <el-table-column
        :label="$t('boarMeasure.naddress')"
        align="center"
        prop="naddress"
        width="80"
      /> -->
      <el-table-column
        :label="$t('boarMeasure.dateOfDetermination')"
        align="center"
        prop="ndate"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ndate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.ningestions')"
        align="right"
        prop="ningestions"
        sortable
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ningestions | formatThousands(0) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.nfeednum')"
        align="right"
        prop="nfeednum"
        sortable
      >
        <template slot-scope="scope">
          <span
            :style="
              Number(scope.row.nfeednum) <= 0
                ? 'color: #f05d18'
                : 'color: black '
            "
          >
            {{
              Number(scope.row.nfeednum) === 0
                ? $t("boarMeasure.unpicked")
                : scope.row.nfeednum
            }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.nsecond')"
        align="right"
        prop="nseconds"
        sortable
      >
        <template slot-scope="scope">
          <span>{{ scope.row.nseconds | secondsToHMS }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('boarMeasure.weightKg')"
        align="right"
        prop="nmidweight"
        sortable
      >
        <template slot-scope="scope">
          <span>{{ scope.row.nmidweight | formatNumber(1) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改【测定日报告】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
          <el-input
            v-model="form.mid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mrfid')" prop="mrfid">
          <el-input
            v-model="form.mrfid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.mdorm')" prop="mdorm">
          <el-input
            v-model="form.mdorm"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
          <el-input
            v-model="form.nindex"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.naddress')" prop="naddress">
          <el-input
            v-model="form.naddress"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('boarMeasure.dateOfDetermination')"
          prop="ndate"
        >
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ndate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.ningestions')" prop="ningestions">
          <el-input
            v-model="form.ningestions"
            :placeholder="$t('common.pleaseInput')"
            @blur="formatToInteger('ningestions')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nfeednum')" prop="nfeednum">
          <el-input
            v-model="form.nfeednum"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.nseconds')" prop="nseconds">
          <el-input
            v-model="form.nseconds"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.weightMid')" prop="nmidweight">
          <el-input
            v-model="form.nmidweight"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>

    <!-- 零耳牌分配对话框 -->
    <el-dialog
      :title="$t('boarMeasure.zeroearDistribution')"
      :visible.sync="openDistribute"
      width="1500px"
      append-to-body
      @close="handleDistributeClose"
    >
      <!-- 零耳牌分配操作区域 -->
      <div class="distribute-container">
        <!-- 操作说明 -->
        <!-- <div class="operation-guide">
          <i class="el-icon-info"></i>
          <span>将零耳牌测定数据分配给指定猪只</span>
        </div> -->

        <!-- 输入区域 -->
        <div class="input-section">
          <el-row :gutter="16" type="flex" align="middle">
            <el-col :span="12">
              <div class="input-group">
                <label class="input-label">目标耳缺号：</label>
                <el-input
                  v-model="distributeForm.mid"
                  placeholder="请输入耳缺号"
                  clearable
                  @input="handleInputChange"
                  @clear="handleClearInput"
                  @keyup.enter.native="handleSearchPigData"
                >
                  <el-button
                    slot="append"
                    type="primary"
                    icon="el-icon-search"
                    @click="handleSearchPigData"
                    :loading="pigDataLoading"
                    :disabled="
                      !distributeForm.mid || distributeForm.mid.trim() === ''
                    "
                  >
                    查询
                  </el-button>
                </el-input>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 当前猪只测定数据显示区域 -->
        <div
          v-if="currentPigData"
          style="
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 6px;
            background-color: #1ecd980d;
          "
        >
          <div style="margin-bottom: 10px">
            <span style="font-weight: 600; color: #303133">
              <i
                class="el-icon-data-line"
                style="margin-right: 8px; color: #1ecd98"
              ></i>
              当前猪只测定数据
            </span>
          </div>
          <el-table :data="currentPigData" :cell-style="{ padding: '0' }">
            <el-table-column
              :label="$t('boarMeasure.mid')"
              align="center"
              prop="mid"
              width="180"
            />
            <el-table-column
              :label="$t('boarMeasure.mrfid')"
              align="center"
              prop="mrfid"
              width="150"
            />
            <el-table-column
              :label="$t('boarMeasure.nindex')"
              align="center"
              prop="nindex"
              width="70"
            />

            <el-table-column
              :label="$t('boarMeasure.intakesStartTime')"
              align="center"
              prop="nstartdate"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.nstartdate) }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('boarMeasure.intakesEndTime')"
              align="center"
              prop="nenddate"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.nenddate) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.intakesTimesS')"
              align="right"
              prop="nseconds"
              width="100"
              ><template slot-scope="scope">
                <span>{{ scope.row.nseconds | formatThousands(0) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.nfriweight')"
              align="right"
              prop="nfriweight"
              ><template slot-scope="scope">
                <span>{{ scope.row.nfriweight | formatThousands(0) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.nsecweight')"
              align="right"
              prop="nsecweight"
              ><template slot-scope="scope">
                <span>{{ scope.row.nsecweight | formatThousands(0) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.ningestionG')"
              align="right"
              prop="ningestion"
              ><template slot-scope="scope">
                <span>{{ scope.row.ningestion | formatThousands(0) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.weightKg')"
              align="right"
              prop="nweight"
              ><template slot-scope="scope">
                <span>{{ scope.row.nweight | formatNumber(1) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.nnum')"
              align="right"
              prop="nnum"
            />
            <el-table-column
              :label="$t('boarMeasure.DPCG')"
              align="center"
              prop="ndpc"
            />
          </el-table>
          <!-- <el-row :gutter="20">
              <el-col :span="4">
                <div class="data-item">
                  <span class="data-label">耳缺号：</span>
                  <span class="data-value">{{ currentPigData.mid }}</span>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="data-item">
                  <span class="data-label">RFID：</span>
                  <span class="data-value">{{ currentPigData.mrfid }}</span>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="data-item">
                  <span class="data-label">日采食量：</span>
                  <span class="data-value highlight"
                    >{{ currentPigData.ndayingestion || 0 }}g</span
                  >
                </div>
              </el-col>
              <el-col :span="4">
                <div class="data-item">
                  <span class="data-label">总采食量：</span>
                  <span class="data-value highlight"
                    >{{ currentPigData.nallIngestion || 0 }}g</span
                  >
                </div>
              </el-col>
              <el-col :span="4">
                <div class="data-item">
                  <span class="data-label">体重：</span>
                  <span class="data-value highlight"
                    >{{ currentPigData.nweight || 0 }}kg</span
                  >
                </div>
              </el-col>
            </el-row> -->
        </div>

        <!-- 提示信息 -->
        <div v-else-if="searchAttempted && !pigDataLoading" class="empty-tip">
          <i class="el-icon-warning-outline"></i>
          <span>{{ getEmptyTipText() }}</span>
        </div>

        <div
          v-if="measureList"
          style="
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 6px;
            background-color: #1ecd980d;
          "
        >
          <div style="margin-bottom: 10px">
            <el-col :span="12">
              <span style="font-weight: 600; color: #303133">
                <i
                  class="el-icon-data-line"
                  style="margin-right: 8px; color: #1ecd98"
                ></i>
                当前零耳牌测定数据
              </span>
            </el-col>
            <el-col :span="12">
              <div class="status-info">
                <el-tag
                  v-if="zeroIds.length > 0"
                  type="success"
                  size="small"
                  effect="light"
                >
                  <i class="el-icon-check"></i>
                  已选择 {{ zeroIds.length }} 条记录
                </el-tag>
                <el-tag v-else type="info" size="small" effect="light">
                  <i class="el-icon-info"></i>
                  请选择零耳牌记录
                </el-tag>
              </div>
            </el-col>
          </div>
          <!-- 数据表格 -->
          <el-table
            ref="zeroDistributeTable"
            :data="measureList"
            @selection-change="handleZeroSelectionChange"
            :cell-style="{ padding: '0' }"
            v-loading="measureListLoading"
            style="width: 100%"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              :label="$t('boarMeasure.mid')"
              align="center"
              prop="mid"
              width="180"
            />
            <el-table-column
              :label="$t('boarMeasure.mrfid')"
              align="center"
              prop="mrfid"
              width="150"
            />
            <el-table-column
              :label="$t('boarMeasure.nindex')"
              align="center"
              prop="nindex"
              width="70"
            />

            <el-table-column
              :label="$t('boarMeasure.intakesStartTime')"
              align="center"
              prop="nstartdate"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.nstartdate) }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('boarMeasure.intakesEndTime')"
              align="center"
              prop="nenddate"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.nenddate) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.intakesTimesS')"
              align="right"
              prop="nseconds"
              width="100"
              ><template slot-scope="scope">
                <span>{{ scope.row.nseconds | formatThousands(0) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.nfriweight')"
              align="right"
              prop="nfriweight"
              ><template slot-scope="scope">
                <span>{{ scope.row.nfriweight | formatThousands(0) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.nsecweight')"
              align="right"
              prop="nsecweight"
              ><template slot-scope="scope">
                <span>{{ scope.row.nsecweight | formatThousands(0) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.ningestionG')"
              align="right"
              prop="ningestion"
              ><template slot-scope="scope">
                <span>{{ scope.row.ningestion | formatThousands(0) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.weightKg')"
              align="right"
              prop="nweight"
              ><template slot-scope="scope">
                <span>{{ scope.row.nweight | formatNumber(1) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('boarMeasure.nnum')"
              align="right"
              prop="nnum"
            />
            <el-table-column
              :label="$t('boarMeasure.DPCG')"
              align="center"
              prop="ndpc"
            />
          </el-table>
        </div>
      </div>

      <!-- 对话框底部操作按钮 -->
      <div slot="footer" class="dialog-footer">
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <div style="color: #909399; font-size: 13px">
            <i class="el-icon-warning" style="margin-right: 5px"></i>
            提示：选择记录后点击"确认分配"完成零耳牌分配操作
          </div>
          <div>
            <el-button @click="handleDistributeCancel">
              {{ $t("common.cancel") }}
            </el-button>
            <el-button
              type="primary"
              @click="handleDistributeConfirm"
              :disabled="zeroIds.length === 0"
              :loading="distributeLoading"
            >
              <i class="el-icon-check" style="margin-right: 5px"></i>
              确认分配 {{ zeroIds.length > 0 ? `(${zeroIds.length})` : "" }}
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!--手动汇总-->
    <el-dialog
      :title="$t('boarMeasure.manualSummary')"
      :visible.sync="openDayGather"
      width="600px"
      append-to-body
      @close="handleGatherDialogClose"
    >
      <!-- 汇总信息概览 -->
      <div class="gather-overview">
        <div class="overview-content">
          <div class="info-item">
            <span class="label">汇总日期：</span>
            <span class="value">{{ formDayGather.nenddate | formatDate }}</span>
          </div>
          <div class="info-item">
            <span class="label">选择猪只：</span>
            <span class="value">{{ selectedPigCount }} 头</span>
          </div>
        </div>
      </div>

      <!-- 选中的猪只列表 -->
      <div class="selected-pigs-section">
        <div class="section-title">
          <i class="el-icon-menu"></i>
          <span>待汇总猪只列表</span>
        </div>
        <div class="pigs-container">
          <div
            v-for="(mid, index) in selectedMids"
            :key="index"
            class="pig-tag"
            :class="{
              processing:
                gatherProgress.processing &&
                gatherProgress.currentIndex === index,
              completed: gatherProgress.completed.includes(index),
              failed: gatherProgress.failed.includes(index),
            }"
          >
            <span class="pig-id">{{ mid }}</span>
            <i
              v-if="
                gatherProgress.processing &&
                gatherProgress.currentIndex === index
              "
              class="el-icon-loading processing-icon"
            ></i>
            <i
              v-else-if="gatherProgress.completed.includes(index)"
              class="el-icon-check success-icon"
            ></i>
            <i
              v-else-if="gatherProgress.failed.includes(index)"
              class="el-icon-close error-icon"
            ></i>
          </div>
        </div>
      </div>

      <!-- 进度信息 -->
      <div v-if="gatherProgress.processing" class="progress-section">
        <div class="progress-info">
          <span
            >正在汇总：{{ gatherProgress.currentIndex + 1 }} /
            {{ selectedMids.length }}</span
          >
          <span class="progress-detail">
            成功：{{ gatherProgress.completed.length }} | 失败：{{
              gatherProgress.failed.length
            }}
          </span>
        </div>
        <el-progress
          :percentage="gatherProgressPercentage"
          :status="gatherProgress.failed.length > 0 ? 'exception' : 'success'"
          :show-text="false"
        ></el-progress>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitFormDayGather"
          :loading="gatherProgress.processing"
          :disabled="selectedMids.length === 0"
        >
          <i class="el-icon-data-analysis"></i>
          {{
            gatherProgress.processing
              ? "汇总中..."
              : `开始汇总 (${selectedMids.length}头)`
          }}
        </el-button>
        <el-button
          @click="cancelDayGather"
          :disabled="gatherProgress.processing"
        >
          {{ $t("common.cancel") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMeasureday,
  getMeasureday,
  delMeasureday,
  addMeasureday,
  updateMeasureday,
  exportMeasureday,
  gatherOneDayData,
  gatherOneDayOnePigData,
} from "@/api/system/measureday";
import { listControl } from "@/api/system/control";
import { formatDay, addDate, addTime, formatDate } from "@/utils";
import { listMeasure, getMeasure, updateMeasure } from "@/api/system/measure";
import { listPigdata } from "@/api/system/pigdata";
import { isEmpty, debounce } from "lodash";

export default {
  name: "DayReport",
  components: {},
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true,
    },
    iconShowBig: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      //栏号数组
      nIndexOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      mrfids: [],
      mids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      //零耳牌对话框多选属性
      zeroIds: [],
      zeroMrfids: [],
      zroMeasure: {},
      // 非单个禁用
      zeroSingle: true,
      // 非多个禁用
      zeroMultiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【测定日报告】表格数据
      measuredayList: [],
      measureList: [],
      measureNewList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 零耳牌弹出层
      openDistribute: false,
      openDayGather: false,
      // 零耳牌分配相关loading状态
      measureListLoading: false,
      distributeLoading: false,
      pigDataLoading: false,
      searchAttempted: false,
      // 零耳牌分配表单
      distributeForm: {
        mid: "",
        mrfid: null,
      },
      // 当前猪只采食数据
      currentPigData: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        naddress: null,
        ndate: null,
        ningestions: null,
        nfeednum: null,
        nseconds: null,
        nmidweight: null,
      },
      formDistribute: {
        radio: 1,
      },
      formDayGather: {
        // nenddate: null,
      },
      // 多头猪汇总相关数据
      selectedMids: [], // 选中的耳缺号列表
      gatherProgress: {
        processing: false,
        currentIndex: -1,
        completed: [],
        failed: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mid: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        mrfid: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
      },
      rulesDayGather: {
        nenddate: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  computed: {
    /** 选中猪只数量 */
    selectedPigCount() {
      return this.selectedMids.length;
    },
    /** 汇总进度百分比 */
    gatherProgressPercentage() {
      if (this.selectedMids.length === 0) return 0;
      const total = this.selectedMids.length;
      const processed =
        this.gatherProgress.completed.length +
        this.gatherProgress.failed.length;
      return Math.round((processed / total) * 100);
    },
  },
  filters: {
    /** 格式化日期 */
    formatDate(value) {
      if (!value) return "";
      return formatDay(value);
    },
  },
  created() {
    var preDate = formatDay(new Date().getTime() - 24 * 60 * 60 * 1000);
    this.queryParams.ndate = preDate;
    listControl({
      ntype: 2,
      mfactory: this.$store.state.settings.nowPigFarm,
    }).then((response) => {
      this.nIndexOptions = response.rows;
      this.queryParams.nindex =
        this.nIndexOptions && this.nIndexOptions[0].nindex;
      this.getList();
    });
    // this.getList();
  },
  methods: {
    handleNewMeasureQuery() {
      const { ndate, nindex } = this.queryParams;

      const queryParams = {
        nstartdate: ndate,
        nenddate: addDate(ndate, 1),
        mid: this.distributeForm.mid.trim(),
        nindex,
        mfactory: this.$store.state.settings.nowPigFarm,
        ntype: 1,
      };

      listMeasure(queryParams).then((response) => {
        this.currentPigData = response.rows;
      });
    },

    handleSmall() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index > 0) {
          this.queryParams.nindex = this.nIndexOptions[index - 1].nindex;
        }
      } else {
        this.queryParams.nindex = this.nIndexOptions[0].nindex;
      }
    },
    handleBig() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index < this.nIndexOptions.length - 1) {
          this.queryParams.nindex = this.nIndexOptions[index + 1].nindex;
        }
      } else {
        this.queryParams.nindex =
          this.nIndexOptions[this.nIndexOptions.length - 1].nindex;
      }
    },
    /** 查询【测定日报告】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;

      listMeasureday(this.queryParams)
        .then((response) => {
          this.measuredayList = response.rows;
          this.total = response.total;
        })
        .catch((error) => {
          console.error("获取测定日报告列表失败:", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelDistribute() {
      this.openDistribute = false;
    },
    cancelDayGather() {
      this.openDayGather = false;
      this.formDayGather = {
        nenddate: null,
      };
      this.resetForm("formDayGather");
    },

    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        naddress: null,
        ndate: null,
        ningestions: null,
        nfeednum: null,
        nseconds: null,
        nmidweight: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.mrfids = selection.map((item) => item.mrfid);
      this.mids = selection.map((item) => item.mid);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    // 零耳牌分配界面多选框选中数据
    handleZeroSelectionChange(selection) {
      this.zeroIds = selection.map((item) => item.indexn);
      this.zeroMrfids = selection.map((item) => item.mrfid);
      this.zeroSingle = selection.length >= 1 ? false : true;
      // this.zeroMultiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("boarMeasure.addDayReport");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.indexn || this.ids;

      getMeasureday(indexn)
        .then((response) => {
          this.form = response.data;
          this.open = true;
          this.title = this.$t("boarMeasure.updateDayReport");
        })
        .catch((error) => {
          console.error("获取测定日报告详情失败:", error);
        });
    },

    /** 零耳牌分配 */
    handleDistribution() {
      // 清除上次的选择状态
      this.clearDistributeSelection();

      // 打开对话框
      this.openDistribute = true;

      // 加载数据（只调用一次）
      this.loadMeasureListForDistribute();
    },

    /** 加载零耳牌分配的测定数据 */
    loadMeasureListForDistribute() {
      this.measureListLoading = true;
      const { ndate, nindex } = this.queryParams;

      const queryParams = {
        nstartdate: ndate,
        nenddate: addDate(ndate, 1),
        nindex,
        ntype: 1,
        mrfid: "000000000000000",
        mfactory: this.$store.state.settings.nowPigFarm,
      };

      listMeasure(queryParams)
        .then((response) => {
          this.measureList = response.rows;
          this.totalMeasure = response.total;
          this.measureListLoading = false;
        })
        .catch(() => {
          this.measureListLoading = false;
          this.$message.error("加载测定数据失败");
        });
    },

    /** 清除分配选择状态 */
    clearDistributeSelection() {
      this.zeroIds = [];
      this.zeroMrfids = [];
      this.zeroSingle = true;
      // 清除表格选择状态
      this.$nextTick(() => {
        if (this.$refs.zeroDistributeTable) {
          this.$refs.zeroDistributeTable.clearSelection();
        }
      });
    },

    /** 获取空数据提示文本 */
    getEmptyTipText() {
      if (!this.distributeForm.mid || this.distributeForm.mid.trim() === "") {
        return "请输入耳缺号查询当天测定数据";
      }
      return `未找到耳缺号"${this.distributeForm.mid}"当天的测定数据`;
    },

    /** 处理输入框清空事件 */
    handleClearInput() {
      this.currentPigData = null;
      this.searchAttempted = false;
    },

    /** 输入变化处理（防抖） */
    handleInputChange: debounce(function (value) {
      if (!value || value.trim() === "") {
        this.handleClearInput();
      } else {
        // 自动查询功能可以根据需要开启
        this.handleSearchPigData();
      }
    }, 300),

    /** 查询猪只当天测定数据 */
    handleSearchPigData() {
      // 输入验证
      const trimmedMid = this.distributeForm.mid?.trim();
      if (!trimmedMid) {
        this.$message.warning("请输入耳缺号");
        return;
      }

      // 设置加载状态
      this.pigDataLoading = true;
      this.searchAttempted = true;
      this.currentPigData = null;

      // 构建查询参数
      const { ndate, nindex } = this.queryParams;
      const pigDataParams = {
        mid: trimmedMid,
        mfactory: this.$store.state.settings.nowPigFarm,
        ntype: 1,
      };
      const measureParams = {
        nstartdate: ndate,
        nenddate: addDate(ndate, 1),
        mid: trimmedMid,
        nindex,
        mfactory: this.$store.state.settings.nowPigFarm,
        ntype: 1,
      };

      // 查询猪只基本信息
      listPigdata(pigDataParams)
        .then((response) => {
          if (response && response.rows && response.rows.length > 0) {
            this.distributeForm.mrfid = response.rows[0].mrfid;
            this.distributeForm.mid = response.rows[0].mid;
          }
        })
        .catch((error) => {
          console.warn("查询猪只基本信息失败:", error);
        });

      // 查询测定数据
      listMeasure(measureParams)
        .then((response) => {
          this.handleMeasureQuerySuccess(response, trimmedMid);
        })
        .catch((error) => {
          this.handleMeasureQueryError(error, trimmedMid);
        })
        .finally(() => {
          this.pigDataLoading = false;
        });
    },

    /** 处理测定数据查询成功 */
    handleMeasureQuerySuccess(response, earTag) {
      if (response && response.rows && response.rows.length > 0) {
        this.currentPigData = response.rows;
        this.$message.success(`成功查询到耳缺号"${earTag}"的测定数据`);
      } else {
        this.currentPigData = null;
        this.$message.warning(`未找到耳缺号"${earTag}"当天的测定数据`);
      }
    },

    /** 处理测定数据查询错误 */
    handleMeasureQueryError(error, earTag) {
      console.error("查询测定数据失败:", error);
      this.currentPigData = null;
      this.$message.error(`查询耳缺号"${earTag}"的测定数据失败`);
    },

    /** 零耳牌分配对话框关闭事件 */
    handleDistributeClose() {
      this.clearDistributeSelection();
      this.measureListLoading = false;
      this.distributeLoading = false;
      this.pigDataLoading = false;
      this.searchAttempted = false;
      this.currentPigData = null;
      this.distributeForm.mid = "";
    },

    /** 零耳牌分配取消操作 */
    handleDistributeCancel() {
      this.handleDistributeClose();
      this.openDistribute = false;
    },

    /** 零耳牌分配确认操作 */
    handleDistributeConfirm() {
      // 验证选择状态
      const validationResult = this.validateDistributeForm();
      if (!validationResult.isValid) {
        this.$message.warning(validationResult.message);
        return;
      }

      // 显示确认对话框
      this.showDistributeConfirmDialog();
    },

    /** 验证分配表单 */
    validateDistributeForm() {
      if (this.zeroIds.length === 0) {
        return {
          isValid: false,
          message: "请先选择需要分配的零耳牌测定记录",
        };
      }

      if (!this.distributeForm.mid || this.distributeForm.mid.trim() === "") {
        return {
          isValid: false,
          message: "请输入目标耳缺号",
        };
      }

      return { isValid: true };
    },

    /** 显示分配确认对话框 */
    showDistributeConfirmDialog() {
      const targetEarTag = this.distributeForm.mid;
      const recordCount = this.zeroIds.length;

      const message = `将选中的 ${recordCount} 条零耳牌测定记录分配给耳缺号"${targetEarTag}"？\n\n此操作不可撤销，请确认无误后操作。`;

      this.$confirm(message, "确认分配操作", {
        confirmButtonText: "确认分配",
        cancelButtonText: "取消",
        type: "warning",
        customClass: "distribute-confirm-dialog",
      })
        .then(() => {
          this.executeDistribution();
        })
        .catch(() => {
          // 用户取消操作
        });
    },

    /** 执行零耳牌分配操作 */
    executeDistribution() {
      this.distributeLoading = true;

      // 批量处理选中的记录
      const promises = this.zeroIds.map((indexn) => {
        return this.assignZeroEarTag(indexn);
      });

      Promise.all(promises)
        .then(() => {
          this.$message.success(`成功分配 ${this.zeroIds.length} 条记录`);
          // this.openDistribute = false;
          this.handleNewMeasureQuery(); // 刷新数据
          this.loadMeasureListForDistribute();
        })
        .catch((error) => {
          console.error("分配失败:", error);
          this.$message.error("分配操作失败，请重试");
        })
        .finally(() => {
          this.distributeLoading = false;
        });
    },

    /** 分配单个零耳牌 */
    assignZeroEarTag(indexn) {
      return new Promise((resolve, reject) => {
        getMeasure(indexn)
          .then((response) => {
            if (response && !isEmpty(response)) {
              const zeroMeasureData = response.data;

              // 将零耳牌的测定数据分配给输入耳缺号的猪
              if (this.distributeForm) {
                // 保留零耳牌的测定数据，但更新猪只信息
                zeroMeasureData.mrfid = this.distributeForm.mrfid;
                zeroMeasureData.mid = this.distributeForm.mid;

                if (zeroMeasureData.indexn != null) {
                  updateMeasure(zeroMeasureData)
                    .then(() => {
                      resolve();
                    })
                    .catch(reject);
                } else {
                  reject(new Error("零耳牌测定数据索引为空"));
                }
              } else {
                reject(
                  new Error("目标猪只数据为空，请先查询耳缺号对应的测定数据")
                );
              }
            } else {
              reject(new Error("未找到零耳牌测定数据"));
            }
          })
          .catch(reject);
      });
    },
    /** 手动汇总 */
    handleDayGather() {
      // 检查是否有选中的猪只
      if (!this.mids || this.mids.length === 0) {
        this.$message.warning("请先选择需要汇总的猪只");
        return;
      }

      // 初始化汇总数据
      this.formDayGather.nenddate = this.queryParams.ndate;
      this.selectedMids = [...this.mids]; // 复制选中的耳缺号
      this.resetGatherProgress();
      this.openDayGather = true;
    },

    /** 重置汇总进度 */
    resetGatherProgress() {
      this.gatherProgress = {
        processing: false,
        currentIndex: -1,
        completed: [],
        failed: [],
      };
    },

    /** 汇总对话框关闭事件 */
    handleGatherDialogClose() {
      this.resetGatherProgress();
      this.selectedMids = [];
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.indexn != null) {
            updateMeasureday(this.form)
              .then(() => {
                this.msgSuccess(this.$t("common.modifiedSuccess"));
                this.open = false;
                this.getList();
              })
              .catch((error) => {
                console.error("更新测定日报告失败:", error);
              });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addMeasureday(this.form)
              .then(() => {
                this.msgSuccess(this.$t("common.addSuccess"));
                this.open = false;
                this.getList();
              })
              .catch((error) => {
                console.error("添加测定日报告失败:", error);
              });
          }
        }
      });
    },
    /** 批量汇总提交 */
    async submitFormDayGather() {
      try {
        // 检查是否有选中的猪只
        if (this.selectedMids.length === 0) {
          this.$message.warning("没有可汇总的猪只");
          return;
        }

        // 开始批量汇总
        await this.executeBatchGather();
      } catch (error) {
        console.error("批量汇总失败:", error);
        this.$message.error("批量汇总过程中发生错误");
      } finally {
        // 汇总完成后刷新列表
        this.getList();
      }
    },

    /** 执行批量汇总 */
    async executeBatchGather() {
      this.gatherProgress.processing = true;

      const baseData = {
        mfactory: this.$store.state.settings.nowPigFarm,
        nenddate: formatDate(addTime(new Date(this.formDayGather.nenddate))),
      };

      // 逐个处理每头猪的汇总
      for (let i = 0; i < this.selectedMids.length; i++) {
        this.gatherProgress.currentIndex = i;
        const mid = this.selectedMids[i];

        try {
          // 调用单头猪汇总接口
          await this.gatherSinglePig(mid, baseData);

          // 标记为成功
          this.gatherProgress.completed.push(i);
        } catch (error) {
          console.error(`汇总猪只 ${mid} 失败:`, error);

          // 标记为失败
          this.gatherProgress.failed.push(i);
        }

        // 添加延迟，避免请求过于频繁
        if (i < this.selectedMids.length - 1) {
          await this.delay(200);
        }
      }

      // 汇总完成
      this.gatherProgress.processing = false;
      this.showGatherResult();
    },

    /** 汇总单头猪 */
    async gatherSinglePig(mid, baseData) {
      const gatherData = {
        ...baseData,
        mid: mid,
      };

      return await gatherOneDayOnePigData(gatherData);
    },

    /** 延迟函数 */
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },

    /** 显示汇总结果 */
    showGatherResult() {
      const success = this.gatherProgress.completed.length;
      const failed = this.gatherProgress.failed.length;

      if (failed === 0) {
        this.$message.success(`汇总完成！成功处理 ${success} 头猪只`);
        this.openDayGather = false;
      } else {
        this.$message.warning(
          `汇总完成！成功 ${success} 头，失败 ${failed} 头`
        );

        // 显示失败的猪只信息
        if (failed > 0) {
          const failedMids = this.gatherProgress.failed.map(
            (index) => this.selectedMids[index]
          );
          this.$message.error(`失败的猪只：${failedMids.join(", ")}`);
        }
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        this.$t("boarMeasure.sureCancelDayReport") +
          `"` +
          indexns +
          `"` +
          this.$t("boarMeasure.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delMeasureday(indexns);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("boarMeasure.sureExportDayReport"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportMeasureday(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    // 格式化为整数（去掉小数位）
    formatToInteger(field) {
      if (this.form[field] && !isNaN(this.form[field])) {
        this.form[field] = Math.round(Number(this.form[field]));
      }
    },
  },
};
</script>
<style scoped>
/* 零耳牌分配界面样式 */
.distribute-container {
  padding: 0;
}

.operation-guide {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 6px;
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.operation-guide i {
  margin-right: 8px;
  color: #1ecd98;
}

.input-section {
  margin-bottom: 16px;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  margin-right: 12px;
  min-width: 100px;
  white-space: nowrap;
}

.status-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.pig-data-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.data-item .label {
  color: #909399;
  font-size: 13px;
}

.data-item .value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

.data-item .value.primary {
  color: #1ecd98;
  font-weight: 600;
}

.empty-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: #909399;
  font-size: 14px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.empty-tip i {
  margin-right: 8px;
  font-size: 16px;
}

/* Element UI 组件样式优化 */
.el-input-group__append .el-button {
  border-left: 0;
}

.el-tag {
  border-radius: 4px;
}

.el-table {
  border-radius: 6px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
}

/* .dialog-footer {
  padding: 16px 20px;
  background-color: #fafafa;
  border-top: 1px solid #ebeef5;
} */

/* 确认对话框样式 */
.distribute-confirm-dialog .el-message-box__content {
  line-height: 1.6;
}

/* 手动汇总弹框样式 */
.gather-overview {
  margin-bottom: 20px;
  padding: 16px;
  /* background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); */
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.overview-header i {
  margin-right: 8px;
  font-size: 18px;
}

.overview-content {
  display: flex;
  gap: 24px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  color: #606266;
  font-size: 14px;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
  font-weight: 600;
  font-size: 14px;
}

.selected-pigs-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #606266;
  font-weight: 500;
  font-size: 14px;
}

.section-title i {
  margin-right: 8px;
  color: #909399;
}

.pigs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.pig-tag {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 16px;
  font-size: 13px;
  color: #606266;
  transition: all 0.3s ease;
}

.pig-tag.processing {
  background: #e6f7ff;
  border-color: #409eff;
  color: #409eff;
  animation: pulse 1.5s infinite;
}

.pig-tag.completed {
  background: #f6ffed;
  border-color: #67c23a;
  color: #67c23a;
}

.pig-tag.failed {
  background: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.pig-id {
  margin-right: 4px;
  font-weight: 500;
}

.processing-icon {
  animation: rotate 1s linear infinite;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.progress-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #606266;
}

.progress-detail {
  font-size: 13px;
  color: #909399;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-grid {
    grid-template-columns: 1fr;
  }

  .input-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .input-label {
    margin-bottom: 8px;
    margin-right: 0;
  }

  .overview-content {
    flex-direction: column;
    gap: 12px;
  }

  .pigs-container {
    max-height: 150px;
  }

  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
.icon:hover {
  color: #48d1cc !important;
}
</style>
