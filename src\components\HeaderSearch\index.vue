<template>
  <div :class="{ show: show }" class="header-search">
    <svg-icon
      class-name="search-icon"
      icon-class="search"
      @click.stop="click"
    />
    <el-select
      ref="headerSearchSelect"
      v-model="search"
      :remote-method="querySearch"
      filterable
      default-first-option
      remote
      placeholder="Search"
      class="header-search-select"
      @change="change"
      @focus="focus"
      @blur="blur"
      allow-create
    >
      <el-option
        v-for="option in options"
        :key="option.item.path"
        :value="option.item"
        :label="option.item.title.join(' > ')"
      />
    </el-select>
    <el-dialog
      title="手动汇总"
      :visible.sync="openDayGather"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formDayGather"
        :model="formDayGather"
        label-width="130px"
        :rules="rulesDayGather"
      >
        <el-form-item label="手动汇总日期" prop="nenddate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="formDayGather.nenddate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择手动汇总日期"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitFormDayGather"
          :loading="submitLoading"
          :disabled="submitLoading"
        >
          {{ submitLoading ? "处理中..." : "确 定" }}
        </el-button>
        <el-button @click="cancelDayGather" :disabled="submitLoading"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// fuse is a lightweight fuzzy-search module
// make search results more in line with expectations
import Fuse from "fuse.js";
import path from "path";
import { gatherOneDayData } from "@/api/system/measureday";
import { gatherOneDayDataFenLan } from "@/api/columnSystem/dailyweight";

export default {
  name: "HeaderSearch",
  data() {
    return {
      search: "",
      options: [],
      searchPool: [],
      show: false,
      fuse: undefined,
      openDayGather: false,
      inputNowVal: "",
      submitLoading: false,
      formDayGather: {
        // nenddate: null,
      },
      rulesDayGather: {
        nenddate: [
          { required: true, message: "手动汇总日期不能为空", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    routes() {
      return this.$store.getters.permission_routes;
    },
  },
  watch: {
    routes() {
      this.searchPool = this.generateRoutes(this.routes);
    },
    searchPool(list) {
      this.initFuse(list);
    },
    show(value) {
      if (value) {
        document.body.addEventListener("click", this.close);
      } else {
        document.body.removeEventListener("click", this.close);
      }
    },
  },
  mounted() {
    this.searchPool = this.generateRoutes(this.routes);
  },
  methods: {
    cancelDayGather() {
      this.openDayGather = false;
      this.submitLoading = false;
      this.formDayGather = {
        nenddate: null,
      };
      this.resetForm("formDayGather");
    },
    submitFormDayGather() {
      this.$refs["formDayGather"].validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          this.formDayGather.mfactory = this.$store.state.settings.nowPigFarm;

          // /system/measure/gatherOneDayData
          if (this.inputNowVal == "007") {
            gatherOneDayData(this.formDayGather)
              .then((response) => {
                // 显示接口返回的msg信息
                if (response && response.code === 200) {
                  this.msgSuccess(response.msg);
                } else {
                  this.msgError("汇总失败，请重试");
                }
                this.openDayGather = false;
                this.getList();
              })
              // .catch((error) => {
              //   console.error("汇总失败:", error);
              //   this.msgError("汇总失败，请重试");
              // })
              .finally(() => {
                this.submitLoading = false;
              });
          }
          if (this.inputNowVal == "008") {
            this.formDayGather.ndate = this.formDayGather.nenddate;
            gatherOneDayDataFenLan(this.formDayGather)
              .then((response) => {
                // 显示接口返回的msg信息
                if (response && response.code === 200) {
                  this.msgSuccess(response.msg);
                } else {
                  this.msgError("汇总失败，请重试");
                }
                this.openDayGather = false;
                this.getList();
              })
              // .catch((error) => {
              //   console.error("汇总失败:", error);
              //   this.msgError("汇总失败，请重试");
              // })
              .finally(() => {
                this.submitLoading = false;
              });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    click() {
      this.show = !this.show;
      if (this.show) {
        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus();
      }
    },
    close() {
      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur();
      this.options = [];
      this.show = false;
    },
    focus(val) {
      // console.log("focus", val, this.search);
    },
    blur(val) {
      // console.log("blur", val, this.search);
    },
    change(val) {
      if (val == "007") {
        this.openDayGather = true;
        this.inputNowVal = "007";
      }
      if (val == "008") {
        this.openDayGather = true;
        this.inputNowVal = "008";
      }
      if (val && val.path && this.ishttp(val.path)) {
        // http(s):// 路径新窗口打开
        window.open(val.path, "_blank");
      } else {
        this.$router.push(val.path);
      }
      this.search = "";
      this.options = [];
      this.$nextTick(() => {
        this.show = false;
      });
    },
    initFuse(list) {
      this.fuse = new Fuse(list, {
        shouldSort: true,
        threshold: 0.4,
        location: 0,
        distance: 100,
        maxPatternLength: 32,
        minMatchCharLength: 1,
        keys: [
          {
            name: "title",
            weight: 0.7,
          },
          {
            name: "path",
            weight: 0.3,
          },
        ],
      });
    },
    // Filter out the routes that can be displayed in the sidebar
    // And generate the internationalized title
    generateRoutes(routes, basePath = "/", prefixTitle = []) {
      let res = [];

      for (const router of routes) {
        // skip hidden router
        if (router.hidden) {
          continue;
        }

        const data = {
          path: !this.ishttp(router.path)
            ? path.resolve(basePath, router.path)
            : router.path,
          title: [...prefixTitle],
        };

        if (router.meta && router.meta.title) {
          data.title = [...data.title, router.meta.title];

          if (router.redirect !== "noRedirect") {
            // only push the routes with title
            // special case: need to exclude parent router without redirect
            res.push(data);
          }
        }

        // recursive child routes
        if (router.children) {
          const tempRoutes = this.generateRoutes(
            router.children,
            data.path,
            data.title
          );
          if (tempRoutes.length >= 1) {
            res = [...res, ...tempRoutes];
          }
        }
      }
      return res;
    },
    querySearch(query) {
      if (query !== "") {
        this.options = this.fuse.search(query);
      } else {
        this.options = [];
      }
    },
    ishttp(url) {
      return url.indexOf("http://") !== -1 || url.indexOf("https://") !== -1;
    },
  },
};
</script>

<style lang="scss" scoped>
.header-search {
  font-size: 0 !important;

  .search-icon {
    cursor: pointer;
    font-size: 18px;
    vertical-align: middle;
  }

  .header-search-select {
    font-size: 18px;
    transition: width 0.2s;
    width: 0;
    overflow: hidden;
    background: transparent;
    border-radius: 0;
    display: inline-block;
    vertical-align: middle;
    min-width: 0;
    max-width: 300px;

    /deep/ .el-input__inner {
      border-radius: 0;
      border: 0;
      padding-left: 0;
      padding-right: 0;
      box-shadow: none !important;
      border-bottom: 1px solid #d9d9d9;
      vertical-align: middle;
      min-width: 0;
    }
  }

  &.show {
    .header-search-select {
      width: 210px;
      min-width: 150px;
      margin-left: 10px;
    }
  }
}

/* 响应式设计 - 处理不同缩放比例和屏幕尺寸 */
@media screen and (max-width: 1200px) {
  .header-search {
    &.show {
      .header-search-select {
        width: 180px;
        min-width: 120px;
      }
    }
  }
}

@media screen and (max-width: 992px) {
  .header-search {
    &.show {
      .header-search-select {
        width: 150px;
        min-width: 100px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .header-search {
    &.show {
      .header-search-select {
        width: 120px;
        min-width: 80px;
        font-size: 14px;
      }
    }
  }
}
</style>
