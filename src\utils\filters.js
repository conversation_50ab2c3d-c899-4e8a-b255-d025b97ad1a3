/**
 * 数值处理全局过滤器
 */

/**
 * 数字格式化 - 保留指定小数位数
 * @param {number} value - 要格式化的数值
 * @param {number} decimals - 小数位数，默认2位
 * @param {boolean} autoRound - 是否自动四舍五入，默认true
 * @returns {string} 格式化后的数值字符串
 */
export function formatNumber(value, decimals = 2, autoRound = true) {
  if (value === undefined || value === '') {
    return ''
  }
  if (value === null) {
    return '/'
  }

  let num = parseFloat(value)
  if (isNaN(num)) {
    return ''
  }

  if (autoRound) {
    num = Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
  }

  return num.toFixed(decimals)
}

/**
 * 千分位分隔符格式化
 * @param {number} value - 要格式化的数值
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 带千分位分隔符的数值字符串
 */
export function formatThousands(value, decimals = 2) {
  if (value === undefined || value === '') {
    return ''
  }

  if (value === null) {
    return '/'
  }

  let num = parseFloat(value)
  if (isNaN(num)) {
    return ''
  }

  const parts = num.toFixed(decimals).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  return parts.join('.')
}

/**
 * 百分比格式化
 * @param {number} value - 要格式化的数值（0-1之间的小数）
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 百分比格式的字符串
 */
export function formatPercent(value, decimals = 2) {
  if (value === null || value === undefined || value === '') {
    return '0%'
  }

  let num = parseFloat(value)
  if (isNaN(num)) {
    return '0%'
  }

  return (num * 100).toFixed(decimals) + '%'
}

/**
 * 文件大小格式化
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 格式化后的文件大小字符串
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 科学计数法格式化
 * @param {number} value - 要格式化的数值
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 科学计数法格式的字符串
 */
export function formatScientific(value, decimals = 2) {
  if (value === null || value === undefined || value === '') {
    return '0.00'
  }

  let num = parseFloat(value)
  if (isNaN(num)) {
    return '0.00'
  }

  if (Math.abs(num) < 0.0001 || Math.abs(num) >= 10000) {
    return num.toExponential(decimals)
  }

  return num.toFixed(decimals)
}

/**
 * 货币格式化
 * @param {number} value - 要格式化的数值
 * @param {string} currency - 货币符号，默认'¥'
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 货币格式的字符串
 */
export function formatCurrency(value, currency = '¥', decimals = 2) {
  if (value === null || value === undefined || value === '') {
    return currency + '0.00'
  }

  let num = parseFloat(value)
  if (isNaN(num)) {
    return currency + '0.00'
  }

  const formatted = formatThousands(num, decimals)
  return currency + formatted
}

/**
 * 数值范围限制
 * @param {number} value - 要限制的数值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 限制后的数值
 */
export function clampNumber(value, min, max) {
  if (value === null || value === undefined || value === '') {
    return min
  }

  let num = parseFloat(value)
  if (isNaN(num)) {
    return min
  }

  return Math.min(Math.max(num, min), max)
}

/**
 * 数值验证 - 是否为有效数字
 * @param {*} value - 要验证的值
 * @returns {boolean} 是否为有效数字
 */
export function isValidNumber(value) {
  if (value === null || value === undefined || value === '') {
    return false
  }

  const num = parseFloat(value)
  return !isNaN(num) && isFinite(num)
}

/**
 * 数值比较 - 安全比较两个数值
 * @param {*} a - 第一个值
 * @param {*} b - 第二个值
 * @returns {number} 比较结果：-1(a<b), 0(a=b), 1(a>b)
 */
export function compareNumbers(a, b) {
  const numA = parseFloat(a)
  const numB = parseFloat(b)

  if (isNaN(numA) && isNaN(numB)) return 0
  if (isNaN(numA)) return -1
  if (isNaN(numB)) return 1

  if (numA < numB) return -1
  if (numA > numB) return 1
  return 0
}

/**
 * 秒数转换为时分秒格式
 * @param {number} totalSeconds - 总秒数
 * @param {string} format - 输出格式，可选值：'HMS'(默认), 'HM', 'H', 'M', 'S'
 * @param {boolean} showZero - 是否显示零值，默认true
 * @returns {string} 格式化后的时间字符串
 */
export function secondsToHMS(totalSeconds, format = 'HMS', showZero = true) {
  if (totalSeconds === null || totalSeconds === undefined || totalSeconds === '') {
    return '00:00:00'
  }

  let seconds = parseInt(totalSeconds)
  if (isNaN(seconds) || seconds < 0) {
    return '00:00:00'
  }

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  // 补零操作
  const pad = (num) => num.toString().padStart(2, '0')

  // 根据格式返回不同结果
  switch (format.toUpperCase()) {
    case 'HMS':
      return `${pad(hours)}:${pad(minutes)}:${pad(remainingSeconds)}`
    case 'HM':
      return showZero || hours > 0 ? `${pad(hours)}:${pad(minutes)}` : `${pad(minutes)}`
    case 'H':
      return showZero || hours > 0 ? `${pad(hours)}` : ''
    case 'M':
      const totalMinutes = Math.floor(seconds / 60)
      return showZero || totalMinutes > 0 ? `${pad(totalMinutes)}` : ''
    case 'S':
      return `${pad(seconds)}`
    default:
      return `${pad(hours)}:${pad(minutes)}:${pad(remainingSeconds)}`
  }
}

/**
 * 秒数转换为可读时间格式
 * @param {number} totalSeconds - 总秒数
 * @param {boolean} short - 是否使用简短格式，默认false
 * @returns {string} 可读时间格式字符串
 */
export function secondsToReadable(totalSeconds, short = false) {
  if (totalSeconds === null || totalSeconds === undefined || totalSeconds === '') {
    return '0秒'
  }

  let seconds = parseInt(totalSeconds)
  if (isNaN(seconds) || seconds < 0) {
    return '0秒'
  }

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (short) {
    if (hours > 0) {
      return `${hours}时${minutes}分`
    } else if (minutes > 0) {
      return `${minutes}分${remainingSeconds}秒`
    } else {
      return `${remainingSeconds}秒`
    }
  } else {
    if (hours > 0) {
      return `${hours}小时${minutes}分钟${remainingSeconds}秒`
    } else if (minutes > 0) {
      return `${minutes}分钟${remainingSeconds}秒`
    } else {
      return `${remainingSeconds}秒`
    }
  }
}
