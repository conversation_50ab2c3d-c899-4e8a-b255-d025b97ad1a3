<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('columnSystem.ntype')" prop="ntype">
        <el-select
          v-model="queryParams.ntype"
          :placeholder="$t('common.pleaseChoose')"
        >
          <el-option
            v-for="dict in pigDataStatusList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mid')" prop="mid">
        <el-input
          v-model="queryParams.mid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item  :label="$t('columnSystem.mname')"  prop="mname">
        <el-input
          v-model="queryParams.mname"
          :placeholder="$t('common.pleaseChoose')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
        <el-input
          v-model="queryParams.naddress"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item :label="$t('columnSystem.weightDate')" prop="ddate">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.ddate"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="$t('common.pleaseChoose')">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="最近体重" prop="nweight">
        <el-input
          v-model="queryParams.nweight"
          placeholder="请输入最近体重"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <el-form-item :label="$t('columnSystem.ddateinkjet')" prop="ddateinkjet">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.ddateinkjet"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="$t('common.pleaseChoose')"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['columnSystem:pigdatadaily:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['columnSystem:pigdatadaily:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['columnSystem:pigdatadaily:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['columnSystem:pigdatadaily:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-set-up"
          size="mini"
          @click="handleOut"
          v-hasPermi="['columnSystem:pigdatadaily:lanout']"
          >{{ $t("columnSystem.outLan") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="pigdatadailyList"
      @selection-change="handleSelectionChange"
      :cell-style="{ padding: '0' }"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('columnSystem.mid')"
        align="center"
        prop="mid"
        width="180"
      />
      <el-table-column
        :label="$t('columnSystem.mrfid')"
        align="center"
        prop="mrfid"
        width="180"
      />
      <el-table-column
        :label="$t('columnSystem.mdorm')"
        align="center"
        prop="mdorm"
      />
      <el-table-column
        :label="$t('columnSystem.nindex')"
        align="center"
        prop="nindex"
      />
      <!-- <el-table-column  :label="$t('columnSystem.mname')"  align="center" prop="mname" />-->
      <el-table-column
        :label="$t('columnSystem.naddress')"
        align="center"
        prop="naddress"
      />
      <el-table-column
        :label="$t('columnSystem.naddressup')"
        align="center"
        prop="naddressup"
      />
      naddressup
      <el-table-column
        :label="$t('columnSystem.enterDate')"
        align="center"
        prop="ddate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ddate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('columnSystem.enterWeight')"
        align="center"
        prop="nweight"
      />
      <el-table-column
          :label="$t('columnSystem.rlage')"
          align="center"
          prop="nrlage"
      />
      <el-table-column
        :label="$t('columnSystem.ntype')"
        align="center"
        prop="ntype"
        :formatter="statusFormat"
      />
      <el-table-column
        :label="$t('columnSystem.remark')"
        align="center"
        prop="remark"
      />
      <!-- <el-table-column
        :label="$t('columnSystem.ddateinkjet')"
        align="center"
        prop="ddateinkjet"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ddateinkjet, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['columnSystem:pigdatadaily:edit']"
            >{{ $t("common.update") }}</el-button
          >

          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            @click="handleTransaction(scope.row)"
            v-hasPermi="['columnSystem:pigdatadaily:transaction']"
            >{{ $t("columnSystem.transaction") }}</el-button
          > -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['columnSystem:pigdatadaily:remove']"
            >{{ $t("common.delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【分栏猪数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="68px">
        <!-- <el-form-item label="关联id" prop="boarid">
          <el-input v-model="form.boarid" placeholder="请输入最后一次喷墨的日期" />
        </el-form-item> -->
        <el-form-item :label="$t('columnSystem.mid')" prop="mid">
          <el-input
            v-model="form.mid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
          <el-input
            v-model="form.mrfid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
          <el-input
            v-model="form.mdorm"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
          <el-input
            v-model="form.nindex"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.mname')" prop="mname">
          <el-input
            v-model="form.mname"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
          <el-input
            v-model="form.naddress"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.naddressup')" prop="naddressup">
          <el-input
            v-model="form.naddressup"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.weightDate')" prop="ddate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ddate"
            type="date"
            value-format="yyyy-MM-dd"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.rlage')" prop="rlage">
          <el-input
              v-model="form.nrlage"
              :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.nearWeight')" prop="nweight">
          <el-input
            v-model="form.nweight"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.ntype')" prop="ntype">
          <el-select
            v-model="form.ntype"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in pigDataStatusList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('columnSystem.ddateinkjet')"
          prop="ddateinkjet"
        >
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ddateinkjet"
            type="date"
            value-format="yyyy-MM-dd"
            :placeholder="$t('common.pleaseChoose')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.remark')" prop="remark">
          <el-input
            v-model="form.remark"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改【分栏猪数据】对话框 -->
    <el-dialog
      :title="titleTransaction"
      :visible.sync="openTransaction"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formTransaction"
        :model="formTransaction"
        :rules="rules"
        label-width="68px"
      >
        <el-form-item :label="$t('columnSystem.mrfid')" prop="mrfid">
          <el-input
            v-model="formTransaction.mrfid"
            :placeholder="$t('common.pleaseInput')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('columnSystem.mreasoninkjet')"
          prop="mreasoninkjet"
        >
          <el-select
            v-model="formTransaction.mreasoninkjet"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in transactionReasonsList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.ninkjet')" prop="ninkjet">
          <el-select
            v-model="formTransaction.ninkjet"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in inkjetStatusList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('columnSystem.ndepart')" prop="ndepart">
          <el-select
            v-model="formTransaction.ndepart"
            :placeholder="$t('common.pleaseChoose')"
          >
            <el-option
              v-for="dict in departStatusList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="Number(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTransaction">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancelTransaction">{{
          $t("common.cancel")
        }}</el-button>
      </div>
    </el-dialog>

    <!-- 全部出栏对话框 -->
    <el-dialog
      :title="titleLan"
      :visible.sync="openLan"
      width="500px"
      append-to-body
    >
      <div class="head-container">
        <el-tree
          show-checkbox
          :data="deptOptions"
          :props="defaultProps"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          ref="tree"
          default-expand-all
          @node-click="handleNodeClick"
          node-key="nIndex"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleLanOut">{{
          $t("columnSystem.outLan")
        }}</el-button>
        <el-button @click="cancelLan">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPigdatadaily,
  getPigdatadaily,
  delPigdatadaily,
  addPigdatadaily,
  updatePigdatadaily,
  exportPigdatadaily,
  outLan,
} from "@/api/columnSystem/pigdatadaily";
import { formatDate } from "@/utils";
import { addProcess } from "@/api/columnSystem/transactionprocessing";
import { treeselect } from "@/api/columnSystem/overview";

export default {
  name: "Pigdatadaily",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【分栏猪数据】表格数据
      pigdatadailyList: [],
      titleTransaction: "",
      formTransaction: {},
      openTransaction: false,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      titleLan: this.$t("columnSystem.outLan"),
      openLan: false,
      // 猪场树选项
      deptOptions: undefined,
      lan: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        boarid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ddate: null,
        nweight: null,
        ntype: "1",
        ddateinkjet: null,
        nrlage: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      pigDataStatusList: [],
      transactionReasonsList: [],
      inkjetStatusList: [],
      departStatusList: [],
    };
  },
  created() {
    this.getList();
    this.getDicts("fl_data_type").then((response) => {
      this.pigDataStatusList = response.data;
    });
    this.getDicts("fl_inkjet_type").then((response) => {
      this.inkjetStatusList = response.data;
    });
    //是否分离
    this.getDicts("fl_depart_type").then((response) => {
      this.departStatusList = response.data;
    });
    //处理原因
    this.getDicts("fl_transaction_reason").then((response) => {
      this.transactionReasonsList = response.data;
    });
    this.getTreeselect();
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  methods: {
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.pigDataStatusList, row.ntype);
    },
    /** 查询【分栏猪数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listPigdatadaily(this.queryParams).then((response) => {
        this.pigdatadailyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelTransaction() {
      this.openTransaction = false;
      this.resetTransaction();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        boarid: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ddate: null,
        nweight: null,
        ntype: null,
        ddateinkjet: null,
        nrlage: null,
      };
      this.resetForm("form");
    },
    // 表单重置
    resetTransaction() {
      this.formTransaction = {
        indexn: null,
        uuid: null,

        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nregdate: null,
        nflag: null,
        ninkjet: null,
        ndepart: null,
        mreasoninkjet: null,
        mreasondepart: null,
        nexecdate: null,
        ntype: null,
        mfactory: null,
        naddressup: null,
        ncol1: null,
        ncol2: null,
      };
      this.resetForm("formTransaction");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("columnSystem.addPigdataDaily");
    },

    /** 事务处理操作 */
    handleTransaction(row) {
      this.reset();
      const id = row.id || this.ids;
      getPigdatadaily(id).then((response) => {
        this.formTransaction = response.data;
        this.openTransaction = true;
        this.titleTransaction = this.$t("columnSystem.transaction");
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPigdatadaily(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("columnSystem.updatePigdataDaily");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updatePigdatadaily(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addPigdatadaily(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    submitTransaction() {
      this.$refs["formTransaction"].validate((valid) => {
        if (valid) {
          this.formTransaction.ntype = 0;
          this.formTransaction.nregdate = formatDate(new Date());
          addProcess(this.formTransaction).then((response) => {
            this.msgSuccess(this.$t("columnSystem.transactionAddSuccess"));
            this.openTransaction = false;
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("columnSystem.sureCancelPigdataDaily") +
          `"` +
          ids +
          `"` +
          this.$t("common.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delPigdatadaily(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("columnSystem.sureExportPigdataDaily"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportPigdatadaily(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },

    /** 查询猪舍结构树 */
    getTreeselect() {
      // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      treeselect({ mfactory: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          response.data.forEach((item) => {
            item.children &&
              item.children.forEach((i) => {
                this.lan.push(i.nIndex);
                if (i.children) {
                  delete i.children;
                }
              });
          });
          this.deptOptions = response.data;
        }
      );
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      // this.queryParams.pageNum = 1;
      // this.queryParams.indexn = data.id;
      // this.getList();
    },

    handleOut() {
      // openLan
      //   this.reset();

      this.openLan = true;
      this.$refs.tree.setCheckedKeys([]);
    },
    cancelLan() {
      // this.$refs.tree.setCheckedKeys([]);
      this.openLan = false;
    },
    handleLanOut() {
      let nindexs = this.lan.filter((item) =>
        new Set(this.$refs.tree.getCheckedKeys()).has(item)
      );
      this.$confirm(
        this.$t("columnSystem.sureOutLan"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      ).then(() => {
        outLan({
          nindexs,
          mfactory: this.$store.state.settings.nowPigFarm,
        }).then((response) => {
          // this.openLan = false;
          this.msgSuccess(this.$t("columnSystem.outLanSuccess"));
          this.$refs.tree.setCheckedKeys([]);
        });
      });
    },
  },
};
</script>
