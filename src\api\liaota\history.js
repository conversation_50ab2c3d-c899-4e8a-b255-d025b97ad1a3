import request from '@/utils/request'
// 查询【请填写功能名称】列表
// /system/liaotahistory
export function listLiaotaHistory(query) {
    return request({
      url: '/system/liaotahistory/list',
      method: 'get',
      params: query
    })
  }
  
  // 查询【请填写功能名称】详细
  export function getLiaotaHistory(id) {
    return request({
      url: '/system/liaotahistory/' + id,
      method: 'get'
    })
  }
  
  // 新增【请填写功能名称】
  export function addLiaotaHistory(data) {
    return request({
      url: '/system/liaotahistory',
      method: 'post',
      data: data
    })
  }
  
  // 修改【请填写功能名称】
  export function updateLiaotaHistory(data) {
    return request({
      url: '/system/liaotahistory',
      method: 'put',
      data: data
    })
  }
  
  // 删除【请填写功能名称】
  export function delLiaotaHistory(id) {
    return request({
      url: '/system/liaotahistory/' + id,
      method: 'delete'
    })
  }
  
  // 导出【请填写功能名称】
  export function exportLiaotaHistory(query) {
    return request({
      url: '/system/liaotahistory/export',
      method: 'get',
      params: query
    })
  }