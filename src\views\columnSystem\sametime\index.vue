<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item  :label="$t('columnSystem.nindex')"  prop="nindex">
        <el-input
          v-model="queryParams.nindex"
           :placeholder="$t('common.pleaseInput')" 
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item  :label="$t('columnSystem.mname')"  prop="mname">
        <el-input
          v-model="queryParams.mname"
           :placeholder="$t('common.pleaseInput')" 
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
        <el-input
          v-model="queryParams.naddress"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item :label="$t('columnSystem.nsametime')" prop="nsametime">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.nsametime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择开门时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item
        :label="$t('columnSystem.nsametimeFrom')"
        prop="nsametimeFrom"
      >
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.nsametimeFrom"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="$t('common.pleaseChoose')"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('columnSystem.nsametimeTo')" prop="nsametimeTo">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.nsametimeTo"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="$t('common.pleaseChoose')"
        >
        </el-date-picker>
      </el-form-item>

      <!-- <el-form-item label="1 同时开启门；2 同时开启门已经关闭" prop="ntype">
        <el-select
          v-model="queryParams.ntype"
          placeholder="请选择1 同时开启门；2 同时开启门已经关闭"
          clearable
          size="small"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      同时开启时间秒
      <el-form-item :label="$t('columnSystem.ntimes')" prop="ntimes">
        <el-input
          v-model="queryParams.ntimes"
          placeholder="请输入延续时间"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['columnSystem:sametime:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['columnSystem:sametime:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['columnSystem:sametime:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['columnSystem:sametime:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        :label="$t('columnSystem.recordList')"
        name="first"
        :lazy="true"
        style="padding-bottom: 10px"
      >
        <el-table
          v-loading="loading"
          :data="sametimeList"
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('common.serialNumber')" align="center" prop="indexn" />-->
          <el-table-column
            :label="$t('columnSystem.mdorm')"
            align="center"
            prop="mdorm"
          />
          <el-table-column
            :label="$t('columnSystem.nindex')"
            align="center"
            prop="nindex"
          />
          <el-table-column
            :label="$t('columnSystem.mname')"
            align="center"
            prop="mname"
          />
          <!-- <el-table-column :label="$t('columnSystem.naddress')" align="center" prop="naddress" /> -->
          <el-table-column
            :label="$t('columnSystem.nsametime')"
            align="center"
            prop="nsametime"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.nsametime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="1 同时开启门；2 同时开启门已经关闭"
            align="center"
            prop="ntype"
          /> -->
          <el-table-column
            :label="$t('columnSystem.ntimes')"
            align="center"
            prop="ntimes"
          />
          <el-table-column
            :label="$t('common.operate')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['columnSystem:sametime:edit']"
                >{{ $t("common.update") }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['columnSystem:sametime:remove']"
                >{{ $t("common.delete") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改【同时开启】对话框 -->
        <el-dialog
          :title="title"
          :visible.sync="open"
          width="500px"
          append-to-body
        >
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
              <el-input
                v-model="form.mdorm"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
              <el-input
                v-model="form.nindex"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.mname')" prop="mname">
              <el-input
                v-model="form.mname"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
              <el-input
                v-model="form.naddress"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item
              :label="$t('columnSystem.nsametime')"
              prop="nsametime"
            >
              <el-date-picker
                clearable
                size="small"
                style="width: 200px"
                v-model="form.nsametime"
                type="date"
                value-format="yyyy-MM-dd"
                :placeholder="$t('common.pleaseChoose')"
              >
              </el-date-picker>
            </el-form-item>
            <!-- <el-form-item
              label="1 同时开启门；2 同时开启门已经关闭"
              prop="ntype"
            >
              <el-select
                v-model="form.ntype"
                placeholder="请选择1 同时开启门；2 同时开启门已经关闭"
              >
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item> -->
            <el-form-item :label="$t('columnSystem.ntimes')" prop="ntimes">
              <el-input
                v-model="form.ntimes"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">{{
              $t("common.determine")
            }}</el-button>
            <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
          </div>
        </el-dialog>
      </el-tab-pane>
      <el-tab-pane
        :label="$t('columnSystem.accruedTime')"
        name="second"
        :lazy="true"
      >
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  listSametime,
  getSametime,
  delSametime,
  addSametime,
  updateSametime,
  exportSametime,
} from "@/api/columnSystem/sametime";

export default {
  name: "Sametime",
  components: {},
  data() {
    return {
      activeName: "first",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【同时开启】表格数据
      sametimeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nsametime: null,
        ntype: null,
        ntimes: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name == "first") this.getList();
      if (tab.name == "second") {
        // this.$nextTick(() => {
        //   this.getPigDataByMid();
        //   // this.category22.resize();
        // });
      }
    },
    /** 查询【同时开启】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listSametime(this.queryParams).then((response) => {
        this.sametimeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        nsametime: null,
        ntype: null,
        ntimes: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("columnSystem.addSameTime");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.indexn || this.ids;
      getSametime(indexn).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("columnSystem.updateSameTime");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.indexn != null) {
            updateSametime(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            addSametime(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        this.$t("columnSystem.sureCancelSameTime") +
          `"` +
          indexns +
          `"` +
          this.$t("common.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delSametime(indexns);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("columnSystem.sureExportSameTime"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportSametime(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>