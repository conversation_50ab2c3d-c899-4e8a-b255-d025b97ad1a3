# 数值处理全局过滤器使用示例

## 在模板中使用

### 1. 基础数字格式化
```vue
<template>
  <div>
    <!-- 保留2位小数 -->
    <p>原始值: {{ 123.4567 }}</p>
    <p>格式化后: {{ 123.4567 | formatNumber }}</p>
    
    <!-- 保留4位小数 -->
    <p>4位小数: {{ 123.4567 | formatNumber(4) }}</p>
    
    <!-- 不自动四舍五入 -->
    <p>不四舍五入: {{ 123.4567 | formatNumber(2, false) }}</p>
  </div>
</template>
```

### 2. 千分位分隔符
```vue
<template>
  <div>
    <p>原始值: {{ 1234567.89 }}</p>
    <p>千分位: {{ 1234567.89 | formatThousands }}</p>
    <p>1位小数: {{ 1234567.89 | formatThousands(1) }}</p>
  </div>
</template>
```

### 3. 百分比格式化
```vue
<template>
  <div>
    <p>原始值: {{ 0.1234 }}</p>
    <p>百分比: {{ 0.1234 | formatPercent }}</p>
    <p>1位小数: {{ 0.1234 | formatPercent(1) }}</p>
  </div>
</template>
```

### 4. 文件大小格式化
```vue
<template>
  <div>
    <p>字节数: {{ 1024 }}</p>
    <p>文件大小: {{ 1024 | formatFileSize }}</p>
    <p>1位小数: {{ 1024 | formatFileSize(1) }}</p>
  </div>
</template>
```

### 5. 科学计数法
```vue
<template>
  <div>
    <p>大数: {{ 1234567890 }}</p>
    <p>科学计数法: {{ 1234567890 | formatScientific }}</p>
    <p>小数: {{ 0.00000123 }}</p>
    <p>科学计数法: {{ 0.00000123 | formatScientific }}</p>
  </div>
</template>
```

### 6. 货币格式化
```vue
<template>
  <div>
    <p>金额: {{ 1234567.89 }}</p>
    <p>人民币: {{ 1234567.89 | formatCurrency }}</p>
    <p>美元: {{ 1234567.89 | formatCurrency('$') }}</p>
    <p>欧元: {{ 1234567.89 | formatCurrency('€') }}</p>
  </div>
</template>
```

### 7. 数值范围限制
```vue
<template>
  <div>
    <p>原始值: {{ 150 }}</p>
    <p>限制在0-100: {{ 150 | clampNumber(0, 100) }}</p>
  </div>
</template>
```

### 8. 数值验证
```vue
<template>
  <div>
    <p>有效数字: {{ '123.45' | isValidNumber }}</p>
    <p>无效数字: {{ 'abc' | isValidNumber }}</p>
  </div>
</template>
```

### 9. 秒数转时分秒格式
```vue
<template>
  <div>
    <p>总秒数: {{ 3665 }}</p>
    <p>时分秒: {{ 3665 | secondsToHMS }}</p>
    <p>时分: {{ 3665 | secondsToHMS('HM') }}</p>
    <p>仅小时: {{ 3665 | secondsToHMS('H') }}</p>
    <p>仅分钟: {{ 3665 | secondsToHMS('M') }}</p>
    <p>仅秒数: {{ 3665 | secondsToHMS('S') }}</p>
  </div>
</template>
```

### 10. 秒数转可读时间格式
```vue
<template>
  <div>
    <p>总秒数: {{ 3665 }}</p>
    <p>完整格式: {{ 3665 | secondsToReadable }}</p>
    <p>简短格式: {{ 3665 | secondsToReadable(true) }}</p>
  </div>
</template>
```

## 在JavaScript中使用

```javascript
// 直接调用函数
import { formatNumber, formatThousands } from '@/utils/filters'

export default {
  methods: {
    processData() {
      const num = 123.4567
      const formatted = formatNumber(num, 2)
      const thousands = formatThousands(num, 2)
      
      console.log(formatted) // "123.46"
      console.log(thousands) // "123.46"
    }
  }
}
```

## 过滤器参数说明

| 过滤器名称 | 参数1 | 参数2 | 参数3 | 说明 |
|-----------|--------|--------|--------|------|
| formatNumber | value | decimals(2) | autoRound(true) | 数字格式化 |
| formatThousands | value | decimals(2) | - | 千分位分隔符 |
| formatPercent | value | decimals(2) | - | 百分比格式化 |
| formatFileSize | bytes | decimals(2) | - | 文件大小格式化 |
| formatScientific | value | decimals(2) | - | 科学计数法 |
| formatCurrency | value | currency('¥') | decimals(2) | 货币格式化 |
| clampNumber | value | min | max | 数值范围限制 |
| isValidNumber | value | - | - | 数值验证 |
| compareNumbers | a | b | - | 数值比较 |
| secondsToHMS | totalSeconds | format('HMS') | showZero(true) | 秒数转时分秒格式 |
| secondsToReadable | totalSeconds | short(false) | - | 秒数转可读时间格式 |

## 注意事项

1. 所有过滤器都会自动处理 `null`、`undefined` 和空字符串
2. 数值验证失败时会返回默认值或 `false`
3. 过滤器可以在模板中链式使用：`{{ value | formatNumber(2) | formatThousands(2) }}`
4. 在组件中也可以直接导入函数使用，不限于模板


