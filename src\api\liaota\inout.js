import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listInout(query) {
  return request({
    url: '/system/inout/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getInout(id) {
  return request({
    url: '/system/inout/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addInout(data) {
  return request({
    url: '/system/inout',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateInout(data) {
  return request({
    url: '/system/inout',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delInout(id) {
  return request({
    url: '/system/inout/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportInout(query) {
  return request({
    url: '/system/inout/export',
    method: 'get',
    params: query
  })
}