<template>
  <div class="app-container">
    <el-row>
      <el-col
        :xs="24"
        :sm="24"
        :md="12"
        :lg="6"
        class="card-box"
        v-for="item in meterList"
        v-bind:key="item.value"
      >
        <el-card>
          <el-row style="display: flex">
            <el-col :span="10">
              <div class="demonstration">{{ item.metername }}</div>
              <el-image
                style="
                  width: 80%;
                  display: flex;
                  justify-content: center; /* 水平居中 */
                  align-items: center; /* 垂直居中 */
                "
                :src="
                  item.slavenetwork === 'ok'
                    ? require('@/assets/icons/environment_svg/electric.png')
                    : require('@/assets/icons/environment_svg/elctric_off.png')
                "
              ></el-image>
            </el-col>
            <el-col :span="1">
              <el-divider
                style="height: 100%"
                direction="vertical"
              ></el-divider>
            </el-col>

            <el-col :span="12">
              <div :class="item.slavenetwork === 'ok' ? 'spanS' : 'spanS_no'">
                <el-tooltip :content="fullNumber(item.energy)" placement="top">
                  <span v-if="isOverLength(item.energy)">{{
                    truncatedNumber(item.energy)
                  }}</span>
                  <span v-else>{{ item.energy ? item.energy : "---" }}</span>
                </el-tooltip>
              </div>

              <!-- <div class="spanS">{{ item.energy ? item.energy : "---" }}</div> -->
              <div class="rowStyle">KW·H</div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listMeter } from "@/api/electric/meter";

export default {
  name: "liaotahomeData",
  data() {
    return {
      maxLength: 10, // 超过这个长度则显示省略号
      dataInterval: 30 * 1000,
      updateIntervalLiaoTa: null,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      meterList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        meterid: null,
        metername: null,
        switchid: null,
        homeid: null,
        uptime: null,
        slavenetwork: null,
        energy: null,
        alarmmsg: null,
      },
    };
  },
  created() {
    this.getList();
    // this.updateIntervalLiaoTa = setInterval(() => {
    //   this.getList();
    // }, this.dataInterval);
  },
  mounted() {
    this.updateIntervalLiaoTa = setInterval(() => {
      this.getList();
    }, this.dataInterval);
  },
  beforeDestroy() {
    clearInterval(this.updateIntervalLiaoTa);
  },
  methods: {
    truncatedNumber(value) {
      return `${value.toString().substring(0, this.maxLength)}...`;
    },
    fullNumber(value) {
      return value;
    },
    isOverLength(value) {
      return value.toString().length > this.maxLength;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listMeter(this.queryParams).then((response) => {
        this.meterList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
  },
};
</script>

<style scoped>
.tagLine {
  border-radius: 100px;
  width: 100%;
  text-align: center;
  height: 40px;
  line-height: 40px;
  background-color: #79bbff;
  border: none;
  font-size: 14px;
}
.rowStyle {
  /* line-height: 50px; */
  font-size: 20px;
  font-family: Helvetica;
  text-align: right;
  color: #82848a;
}
.spanS {
  line-height: 120px;
  font-size: 30px;
  font-family: Helvetica;
  margin: 0 0 0 5px;
  text-align: center;
  color: #26d8e0e0;
}
.spanS_no {
  line-height: 120px;
  font-size: 30px;
  font-family: Helvetica;
  margin: 0 0 0 5px;
  text-align: center;
  color: #82848a;
}
.lineS {
  margin: 5px 0px;
  height: 0.3px;
}
.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 5em;
  margin: 0 8px;
  vertical-align: middle;
  position: relative;
}
/* .demonstration {
  display: block;
  color: #8492a6;
  font-size: 12px;
  margin-bottom: 5px;
  text-align: center;
} */
.cardhead {
  font-family: Helvetica;
  color: #8492a6;
}
.bkpng {
  background-image: url("../../../assets/icons/environment_svg/lianzi.png");
}

.demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin-bottom: 20px;
  text-align: center;
  height: 20%;
}

.el-divider--vertical {
  height: 100%;
}

.rowStyleSpan {
  font-size: 15px;
  padding-top: 3px;
  text-align: center;
}

.rowStyleSpan2 {
  font-size: 16px;
  padding-top: 3px;
  text-align: center;
  color: #077225;
}

.lineS {
  margin: 5px 0px;
  height: 0.3px;
}
</style>