import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listMeter(query) {
  return request({
    url: '/system/meter/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getMeter(id) {
  return request({
    url: '/system/meter/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addMeter(data) {
  return request({
    url: '/system/meter',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateMeter(data) {
  return request({
    url: '/system/meter',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delMeter(id) {
  return request({
    url: '/system/meter/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportMeter(query) {
  return request({
    url: '/system/meter/export',
    method: 'get',
    params: query
  })
}