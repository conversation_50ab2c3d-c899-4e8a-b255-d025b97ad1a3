<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('boarMeasure.mdorm')" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('boarMeasure.mname')" prop="mname">
        <el-input
          v-model="queryParams.mname"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="3" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            :placeholder="$t('boarMeasure.pleaseInputPigHouseName')"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span
                ><svg-icon
                  v-if="data.icon"
                  :icon-class="data.icon"
                  class-name="card-panel-icon"
                />
                {{ node.label }}
              </span>
            </span>
          </el-tree>
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="21" :xs="24">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['boarMeasure:control:add']"
              >{{ $t("common.add") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['boarMeasure:control:edit']"
              >{{ $t("common.update") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['boarMeasure:control:remove']"
              >{{ $t("common.delete") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['boarMeasure:control:export']"
              >{{ $t("common.export") }}</el-button
            > -->
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-setting"
              size="mini"
              :disabled="ruleType"
              @click="handleSet"
              v-hasPermi="['boarMeasure:control:set']"
              >{{ $t("boarMeasure.parameterDistribution") }}</el-button
            >
          </el-col>

          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
          <el-col :span="1.5">
            <el-tooltip
              :content="$t('boarMeasure.onlyShowControl')"
              placement="top"
            >
              <el-switch
                style="
                  position: absolute;
                  top: 50%;
                  transform: translateY(-50%);
                "
                v-model="viewSwitch"
                active-color="#409eff"
                inactive-color="#dcdfe6"
                active-value="3"
                inactive-value="0"
                @change="changeSwitch"
              >
              </el-switch>
            </el-tooltip>
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="controlList"
          @selection-change="handleSelectionChange"
          :cell-style="{ padding: '0' }"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column
            :label="$t('boarMeasure.topologyNodeType')"
            align="center"
            prop="ntype"
            width="80"
            :formatter="typeFormatter"
          >
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.mdorm')"
            align="center"
            prop="mdorm"
            width="60"
          />
          <el-table-column
            :label="$t('boarMeasure.nindex')"
            align="center"
            prop="nindex"
            width="70"
          />
          <el-table-column
            :label="$t('boarMeasure.mname')"
            align="center"
            prop="mname"
            width="60"
          />
          <el-table-column
            :label="$t('boarMeasure.naddress')"
            align="center"
            prop="naddress"
            width="50"
          />
          <!-- <el-table-column
            label="上传地址"
            align="center"
            prop="naddressup"
            width="75"
          />
          <el-table-column
            label="设备序列号"
            align="center"
            prop="nserial"
            width="70"
          /> -->
          <el-table-column
            :label="$t('boarMeasure.gatewayID')"
            align="center"
            prop="switchid"
            width="60"
          />
          <el-table-column
            :label="$t('boarMeasure.nstatus')"
            align="center"
            prop="nstatus"
            :formatter="statusFormat"
            width="50"
          />
          <el-table-column
            :label="$t('boarMeasure.nversion')"
            align="center"
            prop="nversion"
            width="50"
          />
          <!-- <el-table-column
            label="激活状态"
            align="center"
            prop="nactivate"
            :formatter="nativateFormat"
          /> -->
          <el-table-column
            :label="$t('boarMeasure.ncorrect')"
            align="center"
            prop="ncorrect"
            width="70"
          />
          <!-- <el-table-column
            label="料槽校准值最小"
            align="center"
            prop="ncorrectmin"
          />
          <el-table-column
            label="料槽校准值最大"
            align="center"
            prop="ncorrectmax"
          /> -->
          <el-table-column
            :label="$t('boarMeasure.DPCkg')"
            align="center"
            prop="ndpc"
            :formatter="valueFormat"
            width="75"
          />
          <!-- <el-table-column
            label="DPC最小(kg)"
            align="center"
            prop="ndpcmin"
            :formatter="valueFormat"
          />
          <el-table-column
            label="DPC最大(kg)"
            align="center"
            prop="ndpcmax"
            :formatter="valueFormat"
          /> -->
          <el-table-column
            :label="$t('boarMeasure.nsurpluskg')"
            align="center"
            prop="nsurplus"
            :formatter="valueFormat"
            width="85"
          />
          <el-table-column
            :label="$t('boarMeasure.npulse')"
            align="center"
            prop="npulse"
            width="70"
          />
          <el-table-column
            :label="$t('boarMeasure.nindivkg')"
            width="80"
            align="center"
            prop="nindiv"
          />
          <!-- <el-table-column
            label="个体秤校准值最小"
            align="center"
            prop="nindivmin"
          />
          <el-table-column
            label="个体秤校准值最大"
            align="center"
            prop="nindivmax"
          />
          <el-table-column
            label="个体秤空重(kg)"
            align="center"
            prop="nindivnull"
            :formatter="valueFormat"
          />
          <el-table-column
            label="料槽空重(kg)"
            align="center"
            prop="ncorrectnull"
            :formatter="valueFormat"
          /> -->
          <!-- <el-table-column
            :label="$t('boarMeasure.dstartdate')"
            align="center"
            prop="dstartdate"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.dstartdate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.denddate')"
            align="center"
            prop="denddate"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.denddate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('boarMeasure.nweightStart')"
            align="center"
            prop="nweight1"
            :formatter="valueFormat"
          />
          <el-table-column
            :label="$t('boarMeasure.nweightEnd')"
            align="center"
            prop="nweight2"
            :formatter="valueFormat"
          /> -->
          <el-table-column
            :label="$t('boarMeasure.remarks')"
            align="center"
            prop="mmemo"
          />
          <el-table-column
            :label="$t('common.operate')"
            align="center"
            class-name="small-padding fixed-width"
            width="100"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['boarMeasure:control:edit']"
              />

              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['boarMeasure:control:remove']"
              />
              <el-button
                size="mini"
                type="text"
                icon="el-icon-setting"
                :disabled="scope.row.ntype !== 3"
                @click="handleSet(scope.row)"
                v-hasPermi="['boarMeasure:control:set']"
              />
              <el-button
                size="mini"
                type="text"
                icon="el-icon-time"
                @click="handleCheckTime(scope.row)"
                v-hasPermi="['boarMeasure:control:check']"
              />
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改【控制器配置】对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="700px"
      height="600px"
      append-to-body
    >
      <el-form ref="form" :model="form" label-width="130px">
        <el-row>
          <el-col :xs="12" :sm="12" :md="12" :lg="12">
            <el-form-item
              :label="$t('boarMeasure.topologyNodeType')"
              prop="ntype"
              :rules="rules.ntype"
            >
              <el-select
                v-model="form.ntype"
                :placeholder="$t('common.pleaseChoose')"
                style="width: 200px"
                @change="changeNtype"
              >
                <el-option
                  v-for="item in ntypeOptions"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.mdorm')"
              prop="mdorm"
              :rules="rules.mdorm"
            >
              <el-input
                v-model="form.mdorm"
                :placeholder="$t('common.pleaseInput')"
                type="number"
              />
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.nindex')"
              prop="nindex"
              :rules="form.ntype === 1 ? [{ required: false }] : rules.nindex"
            >
              <el-input
                v-model="form.nindex"
                :placeholder="$t('common.pleaseInput')"
                :disabled="form.ntype === 1"
                type="number"
              />
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.mname')"
              prop="mname"
              type="number"
              :rules="
                form.ntype === 1 || form.ntype === 2
                  ? [{ required: false }]
                  : rules.mname
              "
            >
              <el-input
                v-model="form.mname"
                :placeholder="$t('common.pleaseInput')"
                :disabled="form.ntype === 1 || form.ntype === 2"
              />
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.naddress')"
              prop="naddress"
              v-show="form.ntype === 3"
              :rules="
                form.ntype === 1 || form.ntype === 2
                  ? [{ required: false }]
                  : rules.naddress
              "
            >
              <el-input
                v-model="form.naddress"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.gatewayID')"
              prop="switchid"
              v-show="form.ntype === 3"
              :rules="
                form.ntype === 1 || form.ntype === 2
                  ? [{ required: false }]
                  : rules.switchid
              "
            >
              <el-input
                v-model="form.switchid"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.naddressup')"
              prop="naddressup"
              v-show="form.ntype === 3"
            >
              <el-input
                v-model="form.naddressup"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.nserial')"
              prop="nserial"
              v-show="form.ntype === 3"
            >
              <el-input
                v-model="form.nserial"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>

            <el-form-item
              :label="$t('boarMeasure.nstatus')"
              prop="nstatus"
              v-show="form.ntype === 3"
            >
              <el-radio-group v-model="form.nstatus">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.nversion')"
              prop="nversion"
              v-show="form.ntype === 3"
            >
              <el-input
                v-model="form.nversion"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <!-- <el-form-item label="激活状态" prop="nactivate">
          <el-radio-group v-model="form.nactivate">
            <el-radio
              v-for="dict in nativateOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
              >{{ dict.dictLabel }}</el-radio
            >
          </el-radio-group>
          <el-radio-group v-model="form.nactivate">
            <el-radio label="1">请选择激活状态</el-radio>
          </el-radio-group>
          <el-input v-model="form.nactivate" placeholder="请输入激活状态" />
        </el-form-item> -->
            <el-form-item
              :label="$t('boarMeasure.ncorrect')"
              prop="ncorrect"
              v-show="form.ntype === 3"
            >
              <el-input
                v-model="form.ncorrect"
                :placeholder="$t('common.pleaseInput')"
                disabled
              />
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.ncorrectmin')"
              prop="ncorrectmin"
              v-show="form.ntype === 3"
            >
              <el-input
                v-model="form.ncorrectmin"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" v-show="form.ntype === 3">
            <el-form-item
              :label="$t('boarMeasure.ncorrectmax')"
              prop="ncorrectmax"
            >
              <el-input
                v-model="form.ncorrectmax"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('boarMeasure.DPC')" prop="ndpc">
              <el-input
                v-model="form.ndpc"
                :placeholder="$t('common.pleaseInput')"
                disabled
              >
                <i slot="suffix" style="font-style: normal; margin-right: 10px"
                  >kg</i
                >
              </el-input>
            </el-form-item>
            <el-form-item :label="$t('boarMeasure.DPCmin')" prop="ndpcmin">
              <el-input
                v-model="form.ndpcmin"
                :placeholder="$t('common.pleaseInput')"
              >
                <i slot="suffix" style="font-style: normal; margin-right: 10px"
                  >kg</i
                >
              </el-input>
            </el-form-item>
            <el-form-item :label="$t('boarMeasure.DPCmax')" prop="ndpcmax">
              <el-input
                v-model="form.ndpcmax"
                :placeholder="$t('common.pleaseInput')"
              >
                <i slot="suffix" style="font-style: normal; margin-right: 10px"
                  >kg</i
                >
              </el-input>
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.nsurplus')"
              prop="nsurplus"
              disabled
            >
              <el-input
                v-model="form.nsurplus"
                :placeholder="$t('common.pleaseInput')"
                disabled
              >
                <i slot="suffix" style="font-style: normal; margin-right: 10px"
                  >kg</i
                >
              </el-input>
            </el-form-item>
            <el-form-item :label="$t('boarMeasure.npulse')" prop="npulse">
              <el-input
                v-model="form.npulse"
                :placeholder="$t('common.pleaseInput')"
                disabled
              />
            </el-form-item>
            <el-form-item :label="$t('boarMeasure.nindiv')" prop="nindiv">
              <el-input
                v-model="form.nindiv"
                :placeholder="$t('common.pleaseInput')"
                disabled
              />
            </el-form-item>
            <el-form-item :label="$t('boarMeasure.nindivmin')" prop="nindivmin">
              <el-input
                v-model="form.nindivmin"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('boarMeasure.nindivmax')" prop="nindivmax">
              <el-input
                v-model="form.nindivmax"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.nindivnull')"
              prop="nindivnull"
            >
              <el-input
                v-model="form.nindivnull"
                :placeholder="$t('common.pleaseInput')"
                disabled
              >
                <i slot="suffix" style="font-style: normal; margin-right: 10px"
                  >kg</i
                >
              </el-input>
            </el-form-item>
            <el-form-item
              :label="$t('boarMeasure.ncorrectnull')"
              prop="ncorrectnull"
            >
              <el-input
                v-model="form.ncorrectnull"
                :placeholder="$t('common.pleaseInput')"
                disabled
              >
                <i slot="suffix" style="font-style: normal; margin-right: 10px"
                  >kg</i
                >
              </el-input>
            </el-form-item>
            <!-- <el-form-item :label="$t('boarMeasure.dstartdate')" prop="dstartdate">
          <el-date-picker
            clearable
            size="small"
            style="width: 360px"
            v-model="form.dstartdate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择测定开始日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.denddate')" prop="denddate">
          <el-date-picker
            clearable
            size="small"
            style="width: 360px"
            v-model="form.denddate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择测定结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="开始测定体重" prop="nweight1">
          <el-input v-model="form.nweight1" placeholder="请输入开始测定体重">
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >kg</i
            >
          </el-input>
        </el-form-item>
        <el-form-item label="结束测定体重" prop="nweight2">
          <el-input v-model="form.nweight2" placeholder="请输入结束测定体重">
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >kg</i
            >
          </el-input>
        </el-form-item> -->
            <el-form-item :label="$t('boarMeasure.remarks')" prop="mmemo">
              <el-input
                v-model="form.mmemo"
                :placeholder="$t('common.pleaseInput')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm"
          :loading="submitLoading"
          >{{ $t("common.determine") }}</el-button
        >
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="titleSet"
      :visible.sync="openSet"
      width="600px"
      append-to-body
      :destroy-on-close="true"
      :show-close="false"
      :before-close="cancelSet"
      :close="cancelSet"
      :closed="cancelSet"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <!-- //      v-loading="loadingSet" -->
      <el-form
        ref="formSet"
        :model="formSet"
        label-width="130px"
        class="demo-dynamic"
      >
        <el-form-item :label="$t('boarMeasure.mname')" prop="mname">
          <el-input
            v-model="formSet.mname"
            :placeholder="$t('common.pleaseInput')"
            disabled
          />
        </el-form-item>
        <el-form-item :label="$t('boarMeasure.naddress')" prop="naddress">
          <el-input
            v-model="formSet.naddress"
            :placeholder="$t('common.pleaseInput')"
            disabled
          />
        </el-form-item>

        <!-- <el-form-item
          :label="$t('boarMeasure.ncorrect')"
          prop="ncorrect"
          :rules="rulesSet.ncorrect"
        >
          <el-input
            style="width: 90%"
            v-model="formSet.ncorrect"
            :placeholder="$t('common.pleaseInput')"
            ><i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-255)kg</i
            ></el-input
          >
          <el-button
            style="margin-left: 6px"
            type="primary"
            icon="el-icon-check"
            circle
            @click.prevent="handleDataSave(formSet.ncorrect, 'ncorrect')"
          ></el-button>
        </el-form-item> -->

        <el-form-item
          :label="$t('boarMeasure.DPC')"
          prop="ndpc"
          :rules="rulesSet.ndpc"
        >
          <el-input
            style="width: 90%"
            v-model="formSet.ndpc"
            :placeholder="$t('common.pleaseInput')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-1)kg
            </i>
          </el-input>
          <el-button
            style="margin-left: 6px"
            type="primary"
            icon="el-icon-check"
            circle
            @click.prevent="handleDataSave(formSet.ndpc * 1000, 7)"
          ></el-button>
        </el-form-item>

        <el-form-item
          :label="$t('boarMeasure.nsurplus')"
          prop="nsurplus"
          :rules="rulesSet.nsurplus"
        >
          <el-input
            style="width: 90%"
            v-model="formSet.nsurplus"
            :placeholder="$t('common.pleaseInput')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-1)kg
            </i>
          </el-input>
          <el-button
            style="margin-left: 6px"
            type="primary"
            icon="el-icon-check"
            circle
            @click.prevent="handleDataSave(formSet.nsurplus * 1000, 8)"
          ></el-button>
        </el-form-item>
        <div style="display: flex">
          <el-form-item label="料槽调零" prop="nsurplus">
            <el-button
              style="margin-left: 6px"
              type="primary"
              icon="el-icon-check"
              circle
              @click.prevent="handleDataSave(0, 1)"
            ></el-button>
          </el-form-item>
          <el-form-item label="料槽校准" prop="nsurplus">
            <el-button
              style="margin-left: 6px"
              type="primary"
              icon="el-icon-check"
              circle
              @click.prevent="handleDataSave(0, 2)"
            ></el-button>
          </el-form-item>
          <el-form-item label="称重故障复位" prop="nsurplus">
            <el-button
              style="margin-left: 6px"
              type="primary"
              icon="el-icon-check"
              circle
              @click.prevent="handleDataSave(0, 11)"
            ></el-button>
          </el-form-item>
        </div>
        <div style="display: flex">
          <el-form-item label="秤体调零" prop="nsurplus">
            <el-button
              style="margin-left: 6px"
              type="primary"
              icon="el-icon-check"
              circle
              @click.prevent="handleDataSave(0, 3)"
            ></el-button>
          </el-form-item>
          <el-form-item label="秤体校准" prop="nsurplus">
            <el-button
              style="margin-left: 6px"
              type="primary"
              icon="el-icon-check"
              circle
              @click.prevent="handleDataSave(0, 4)"
            ></el-button>
          </el-form-item>
          <el-form-item label="电机故障复位" prop="nsurplus">
            <el-button
              style="margin-left: 6px"
              type="primary"
              icon="el-icon-check"
              circle
              @click.prevent="handleDataSave(0, 9)"
            ></el-button>
          </el-form-item>
        </div>
        <div style="display: flex">
          <el-form-item label="DPC异常复位" prop="nsurplus">
            <el-button
              style="margin-left: 6px"
              type="primary"
              icon="el-icon-check"
              circle
              @click.prevent="handleDataSave(0, 10)"
            ></el-button>
          </el-form-item>
        </div>
        <el-col :span="1.5" class="warning">
          <span style="color: #f44d03; margin-right: 5px">{{
            $t("boarMeasure.saveDetails")
          }}</span>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          id="query-button-id"
          type="primary"
          :loading="loadingSet"
          :disabled="loadingSet"
          @click.prevent="submitFormSet"
          >{{ $t("common.refresh") }}</el-button
        >

        <el-button @click="cancelSet">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listControl,
  getControl,
  delControl,
  addControl,
  updateControl,
  exportControl,
  treeselect,
  boarControlMqtt,
  checkTime,
} from "@/api/system/control";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { generateUUIDInRange } from "@/utils";
import { isUndefined, isEmpty, isObject } from "lodash";

let source;

export default {
  name: "Control",
  components: {},
  data() {
    return {
      viewSwitch: "3",
      ntypeOptions: [
        {
          value: 1,
          name: this.$t("boarMeasure.mdorm"),
        },
        {
          value: 2,
          name: this.$t("boarMeasure.nindex"),
        },
        {
          value: 3,
          name: this.$t("boarMeasure.mname"),
        },
      ],
      // 状态数据字典
      statusOptions: [
        {
          dictValue: 0,
          dictLabel: this.$t("boarMeasure.stop"),
        },
        {
          dictValue: 1,
          dictLabel: this.$t("boarMeasure.normal"),
        },
      ],
      // 激活状态数据字典
      nativateOptions: [
        {
          dictValue: 0,
          dictLabel: this.$t("boarMeasure.inactive"),
        },
        {
          dictValue: 1,
          dictLabel: this.$t("boarMeasure.active"),
        },
      ],
      // stausMap: {
      //   1: "猪舍号",
      //   2: "栏号",
      //   3: "测定站",
      // },
      deptName: undefined,
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 猪舍树选项
      deptOptions: undefined,
      // 遮罩层
      loading: true,
      loadingSet: true,
      // loadingSave: false,
      // 选中数组
      ids: [],
      devAddrs: [],
      devAddr: "",
      indexn: "",
      // 类型
      ruleType: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 【控制器配置】表格数据
      controlList: [],
      titleSet: "",
      openSet: false,
      // 弹出层标题
      title: "",
      submitLoading: false,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        ntype: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        nserial: null,
        nstatus: null,
        nversion: null,
        nactivate: null,
        ncorrect: null,
        ncorrectmin: null,
        ncorrectmax: null,
        ndpc: null,
        ndpcmin: null,
        ndpcmax: null,
        nsurplus: null,
        npulse: null,
        nindiv: null,
        nindivmin: null,
        nindivmax: null,
        nindivnull: null,
        ncorrectnull: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        mmemo: null,
      },
      // 表单参数
      form: {},
      // 参数下发表单
      formSet: {},
      // 表单校验
      rulesSet: {
        ncorrect: [
          {
            pattern: /^(([0-9]|([1-9]\d)|(1\d\d)|(2([0-4]\d|5[0-5]))))$/g,
            message: this.$t("boarMeasure.pleaseEnterCorrectRange"),
            // required: true,
            // message: "拓扑节点类型不能为空",
            trigger: ["blur", "change"],
          },
        ],
        ndpc: [
          {
            pattern: /^(0(\.\d{1,3})?|1)$/g,
            message: this.$t("boarMeasure.pleaseEnterCorrectRange"),
            trigger: ["blur", "change"],
          },
        ],
        nsurplus: [
          {
            pattern: /^(0(\.\d{1,3})?|1)$/g,
            message: this.$t("boarMeasure.pleaseEnterCorrectRange"),
            trigger: ["blur", "change"],
          },
        ],
        npulse: [
          // /^\d{1,10}$/
          {
            pattern: /^([1-9]|10)$/g,
            message: this.$t("boarMeasure.pleaseEnterCorrectRange"),
            trigger: ["blur", "change"],
          },
        ],
        nindiv: [
          {
            pattern: /^(([0-9]|([1-9]\d)|(1\d\d)|(2([0-4]\d|5[0-5]))))$/g,
            message: this.$t("boarMeasure.pleaseEnterCorrectRange"),
            trigger: ["blur", "change"],
          },
        ],
        nindivnull: [
          {
            pattern: /^(\d{1,2}(\.\d{1,3})?|100)$/,
            message: this.$t("boarMeasure.pleaseEnterCorrectRange"),
            trigger: ["blur", "change"],
          },
        ],
        ncorrectnull: [
          {
            pattern: /^(\d{1,2}(\.\d{1,3})?|100)$/,
            message: this.$t("boarMeasure.pleaseEnterCorrectRange"),
            trigger: ["blur", "change"],
          },
        ],
      },
      rules: {
        ntype: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        mdorm: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        nindex: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        mname: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        naddress: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        switchid: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
    // viewSwitch: {
    //   handler() {
    //     this.getList();
    //   },
    //   deep: true,
    // },
  },

  created() {
    this.getList();
    this.getTreeselect();
    // this.getDicts("sys_normal_disable").then((response) => {
    //   this.statusOptions = response.data;
    // });
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  beforeDestroy() {
    // if (this.timer) {
    //   clearTimeout(this.timer); // 在Vue实例销毁前，清除我们的定时器
    // }
    if (
      // !(isUndefined(source) || isEmpty(source))
      !(typeof source == "undefined" || source == undefined || source == null)
    ) {
      source.close();
      source = null;
    }
    // window.removeEventListener("resize", this.handleWindowResize);
  },
  methods: {
    handleCheckTime(row) {
      checkTime({
        indexn: row.indexn,
        switchid: row.switchid,
        naddress: row.naddress,
        mfactory: this.$store.state.settings.nowPigFarm,
      }).then((response) => {
        this.msgSuccess(this.$t("boarMeasure.timingSuccess"));
        // this.open = false;
        // this.getList();
        // this.getTreeselect();
      });
    },
    changeNtype(val) {
      if (val === 1) {
        this.form.nindex = null;
        this.form.mname = null;
        // this.form.mdorm = Number(nowForm.mdorm) + 1;
      } else if (val === 2) {
        this.form.mname = null;
        // this.form.nindex = Number(nowForm.nindex) + 1;
      }
      // else if (val === 3) {
      //   this.form.mname = Number(nowForm.mname) + 1;
      // }
    },
    valueFormat(row, column, cellValue, index) {
      return cellValue / 1000;
    },
    /** 查询【控制器配置】列表 */
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.nstatus);
    },
    nativateFormat(row, column) {
      return this.selectDictLabel(this.nativateOptions, row.nactivate);
    },

    typeFormatter(row) {
      return (
        this.ntypeOptions.filter((item) => item.value === row.ntype) &&
        this.ntypeOptions.filter((item) => item.value === row.ntype)[0].name
      );
    },
    // 节点单击事件
    handleNodeClick(data) {
      // this.queryParams.pageNum = 1;
      // this.queryParams.indexn = data.id;
      // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      // this.getList();
      this.showSearch = true;
      this.queryParams.mname = null;
      this.queryParams.nindex = null;
      this.queryParams.mdorm = null;
      listControl({
        pageNum: 1,
        indexn: data.id,
        mfactory: this.$store.state.settings.nowPigFarm,
      }).then((response) => {
        // ntype类型  mdorm猪舍号 nindex栏号 mname测定站
        this.queryParams.pageNum = 1;
        this.queryParams.indexn = data.id;
        this.queryParams.ntype = response.rows[0].ntype;
        if (response.rows && response.rows[0].ntype == 3) {
          this.queryParams.mname = response.rows[0].mname;
          this.queryParams.nindex = response.rows[0].nindex;
          this.queryParams.mdorm = response.rows[0].mdorm;
        } else if (response.rows && response.rows[0].ntype == 2) {
          this.queryParams.nindex = response.rows[0].nindex;
          this.queryParams.mdorm = response.rows[0].mdorm;
        } else if (response.rows && response.rows[0].ntype == 1) {
          this.queryParams.mdorm = response.rows[0].mdorm;
        }
        this.getList();

        // this.controlList = response.rows;
        // this.total = response.total;

        // this.queryParams.pageNum = 1;
        // this.loading = false;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    /** 查询猪舍结构树 */
    getTreeselect() {
      treeselect({ mfactory: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          let icons = ["she", "lan", "zhan"];
          let data = this.walk(
            response.data,
            (data, deep) => ({
              ...data,
              icon: icons[deep],
            }),
            0
          );
          this.deptOptions = data;
        }
      );
    },

    walk(list, callback, deep = 0) {
      return list.map((it) => {
        const result = callback({ ...it }, deep);
        if (it.children) {
          result.children = this.walk(it.children, callback, deep + 1);
        }
        return result;
      });
    },
    //控制器配置
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      this.queryParams.ntype = this.viewSwitch == 3 ? 3 : null;

      listControl(this.queryParams).then((response) => {
        this.controlList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮
    cancelSet() {
      // clearTimeout(this.timer);
      if (
        // !(isUndefined(source) || isEmpty(source))
        // !(typeof myObj == "undefined")
        !(typeof source == "undefined" || source == undefined || source == null)
      ) {
        source.close();
        source = null;
      }
      this.loadingSet = false;
      this.openSet = false;
      this.resetSet();
    },

    //参数下发单个保存
    handleDataSave(value, code) {
      this.handleEventSource(value, code);
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        ntype: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        nserial: null,
        nstatus: 0,
        nversion: null,
        nactivate: null,
        ncorrect: null,
        ncorrectmin: null,
        ncorrectmax: null,
        ndpc: null,
        ndpcmin: null,
        ndpcmax: null,
        nsurplus: null,
        npulse: null,
        nindiv: null,
        nindivmin: null,
        nindivmax: null,
        nindivnull: null,
        ncorrectnull: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        mmemo: null,
      };
      this.resetForm("form");
    },

    // 表单重置
    resetSet() {
      this.formSet = {
        indexn: null,
        ntype: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        nserial: null,
        nstatus: 0,
        nversion: null,
        nactivate: null,
        ncorrect: null,
        ncorrectmin: null,
        ncorrectmax: null,
        ndpc: null,
        ndpcmin: null,
        ndpcmax: null,
        nsurplus: null,
        npulse: null,
        nindiv: null,
        nindivmin: null,
        nindivmax: null,
        nindivnull: null,
        ncorrectnull: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        mmemo: null,
      };
      this.resetForm("formSet");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.deptName = undefined;
      this.queryParams.indexn = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.devAddrs = selection.map((item) => item.naddress);
      this.single = selection.length !== 1;
      this.ruleType = !(selection.length === 1 && selection[0].ntype === 3);

      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("boarMeasure.addControl");
    },
    //参数下发
    async handleSet(row) {
      if (
        !(typeof source == "undefined" || source == undefined || source == null)
        // !(typeof myObj == "undefined")
      ) {
        source.close();
        source = null;
      }

      this.resetSet();
      this.loadingSet = false;
      this.indexn = row.indexn || this.ids;
      this.devAddr = row.naddress || this.devAddrs;

      // this.formSet.naddress = 1;
      // ndpc nsurplus ncorrectnull nindivnull
      getControl(this.indexn).then((response) => {
        this.formSet = response.data;
        this.formSet.ndpc = this.formSet.ndpc / 1000;
        this.formSet.nsurplus = this.formSet.nsurplus / 1000;
      });
      // await this.handleEventSource(0, 12);
      this.openSet = true;
      this.titleSet = this.$t("boarMeasure.parameterDistribution");
    },
    handleEventSource(value, code) {
      //创建SSE对象 查询
      let timer = null;
      if (window.EventSource) {
        //定义EventSource对象的变量，onopen是连接的时候触发、onmessage是接收消息的时候触发、onerror是连接失败时候触发，EventSource是连接失败后会重新发起连接的。
        // 创建 EventSource 对象连接服务器
        //const source = new EventSource('http://localhost:2000');
        // source = new EventSource("http://localhost:2000/");
        ///system/sse/boarparampublishV2
        if (code == "12") {
          source = new EventSource(
            process.env.VUE_APP_BASE_API +
              `/system/sse/boarparampublishV2?uuid=${generateUUIDInRange(
                0,
                65535
              )}&devAddr=${this.devAddr}&indexn=${this.indexn}&pigfarmID=${
                this.$store.state.settings.nowPigFarm
              }&code=${code}&value=${value}`
          );
        } else {
          source = new EventSource(
            process.env.VUE_APP_BASE_API +
              `/system/sse/boarparampublishV2?devAddr=${this.devAddr}&indexn=${this.indexn}&pigfarmID=${this.$store.state.settings.nowPigFarm}&code=${code}&value=${value}`
          );
        }

        // 连接成功后会触发 open 事件
        source.addEventListener("open", (e) => {}, false);

        // 服务器发送信息到客户端时，如果没有 event 字段，默认会触发 message 事件
        source.addEventListener(
          "message",
          (e) => {
            if (e.data === "查询中" || e == null) {
              this.loadingSet = true;
              const that = this;
              timer = setTimeout(function () {
                that.msgError(that.$t("boarMeasure.timeout"));
                that.loadingSet = false;
              }, 30000);
            } else if (e.data === "参数下发成功") {
              // if (code == "12") {
              //   this.loadingSet = false;
              // } else {
              //   this.msgSuccess(this.$t("boarMeasure.successfulLaunch"));
              // }
            } else if (
              JSON.parse(e.data) &&
              isObject(JSON.parse(e.data)) &&
              JSON.stringify(JSON.parse(e.data)) !== "{}"
            ) {
              this.formSet.ndpc = JSON.parse(e.data).ndpc / 1000;
              this.formSet.nsurplus = JSON.parse(e.data).nsurplus / 1000;
              clearTimeout(timer);
              this.loadingSet = false;
              if (
                !(
                  typeof source == "undefined" ||
                  source == undefined ||
                  source == null
                )
              ) {
                source.close();
                source = null;
              }
            }
          },
          false
        );

        // 自定义 EventHandler，在收到 event 字段为 slide 的消息时触发
        source.addEventListener("slide", (e) => {}, false);

        // 连接异常时会触发 error 事件并自动重连
        source.addEventListener(
          "error",
          (e) => {
            if (e.target.readyState === EventSource.CLOSED) {
              console.log("Disconnected");
              source.close();
              source = null;
              this.msgError(this.$t("boarMeasure.timeout"));
              this.loadingSet = false;
            } else if (e.target.readyState === EventSource.CONNECTING) {
              source.close();
              source = null;
              // this.msgError(this.$t("boarMeasure.timeout"));
              this.loadingSet = false;

              console.log("Connecting...1");
            }
          },
          false
        );
        // }
      } else {
        console.error("Your browser doesn't support SSE");
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.indexn || this.ids;
      getControl(indexn).then((response) => {
        this.form = response.data;
        //ndpcmin ndpcmax nindivmin nindivmax
        this.form.ndpc = this.form.ndpc && this.form.ndpc / 1000;
        this.form.nsurplus = this.form.nsurplus && this.form.nsurplus / 1000;
        this.form.ncorrectnull =
          this.form.ncorrectnull && this.form.ncorrectnull / 1000;
        this.form.nindivnull =
          this.form.nindivnull && this.form.nindivnull / 1000;
        this.form.ndpcmin = this.form.ndpcmin && this.form.ndpcmin / 1000;
        this.form.ndpcmax = this.form.ndpcmax && this.form.ndpcmax / 1000;
        // this.form.nweight1 = this.form.nweight1 && this.form.nweight1 / 1000;
        // this.form.nweight2 = this.form.nweight2 && this.form.nweight2 / 1000;
        this.open = true;
        this.title = this.$t("boarMeasure.updateControl");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.submitLoading = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // this.form.mfactory = this.$store.state.settings.nowPigFarm;
          // this.form.ndpc = this.form.ndpc && this.form.ndpc * 1000;
          // this.form.nsurplus = this.form.nsurplus && this.form.nsurplus * 1000;
          // this.form.ncorrectnull =
          //   this.form.ncorrectnull && this.form.ncorrectnull * 1000;
          // this.form.nindivnull =
          //   this.form.nindivnull && this.form.nindivnull * 1000;
          // this.form.ndpcmin = this.form.ndpcmin && this.form.ndpcmin * 1000;
          // this.form.ndpcmax = this.form.ndpcmax && this.form.ndpcmax * 1000;
          // this.form.nweight1 = this.form.nweight1 && this.form.nweight1 * 1000;
          // this.form.nweight2 = this.form.nweight2 && this.form.nweight2 * 1000;
          let nowForm = {
            ...this.form,
            mfactory: this.$store.state.settings.nowPigFarm,
            ndpc: this.form.ndpc && this.form.ndpc * 1000,
            nsurplus: this.form.nsurplus && this.form.nsurplus * 1000,
            ncorrectnull:
              this.form.ncorrectnull && this.form.ncorrectnull * 1000,
            nindivnull: this.form.nindivnull && this.form.nindivnull * 1000,
            ndpcmin: this.form.ndpcmin && this.form.ndpcmin * 1000,
            ndpcmax: this.form.ndpcmax && this.form.ndpcmax * 1000,
            // nweight1: this.form.nweight1 && this.form.nweight1 * 1000,
            // nweight2: this.form.nweight2 && this.form.nweight2 * 1000,
          };

          if (this.form.indexn != null) {
            updateControl(nowForm).then((response) => {
              if (response) {
                this.msgSuccess(this.$t("common.modifiedSuccess"));
                this.submitLoading = false;
                this.open = false;
                this.getList();
                this.getTreeselect();
              } else {
                this.submitLoading = false;
              }
            });
            this.submitLoading = false;
          } else {
            addControl(nowForm).then((response) => {
              if (response) {
                this.msgSuccess(this.$t("common.addSuccess"));
                this.submitLoading = false;
                // this.open = false;
                if (nowForm.ntype === 1) {
                  this.form.mdorm = Number(nowForm.mdorm) + 1;
                } else if (nowForm.ntype === 2) {
                  this.form.nindex = Number(nowForm.nindex) + 1;
                } else if (nowForm.ntype === 3) {
                  this.form.mname = Number(nowForm.mname) + 1;
                }
                this.getList();
                this.getTreeselect();
              } else {
                this.submitLoading = false;
              }
            });
            this.submitLoading = false;
          }
        }
      });
    },
    //     防抖：用户操作频繁，但是只执行一次
    // 节流：用户操作频繁，但是把频繁的操作变为少量操作【可以给浏览器有充裕的时间解析代码】
    submitFormSet() {
      // 先执行一次 delayedFunction
      this.delayedFunction();
      // 使用 debounce 函数包裹 delayedFunction，并设置立即执行
      this.debouncedDelayedFunction = this.debounce(
        this.delayedFunction,
        5000,
        true
      );

      // 获取按钮元素
      const buttonElement = document.getElementById("query-button-id");

      // 绑定点击事件，确保调用的是 debouncedDelayedFunction
      buttonElement.addEventListener("click", this.debouncedDelayedFunction);
    },

    debounce(func, wait, immediate) {
      var timeout;
      return function () {
        var context = this,
          args = arguments;
        var later = function () {
          timeout = null;
          if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
      };
    },

    // 定义要延迟执行的函数
    delayedFunction() {
      this.loadingSet = true;
      if (
        !(typeof source == "undefined" || source == undefined || source == null)
      ) {
        source.close();
        source = null;
      }
      this.handleEventSource(0, 12);
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        this.$t("boarMeasure.sureCancelControl") +
          `"` +
          indexns +
          `"` +
          this.$t("boarMeasure.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delControl(indexns);
        })
        .then(() => {
          this.getList();
          this.getTreeselect();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("boarMeasure.sureExportControl"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportControl(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    changeSwitch(val) {
      this.getList();
    },
  },
};
</script>
<style scoped>
.warning {
  padding: 4px 16px;
  background-color: #fff6f7;
  border-radius: 4px;
  margin-bottom: 10px;
  /* border-left: 5px solid #fe6c6f; */
  /* margin: 20px 0; */
}
</style>
