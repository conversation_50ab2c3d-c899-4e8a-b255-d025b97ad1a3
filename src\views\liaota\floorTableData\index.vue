<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
        label-width="68px"
    >
      <el-form-item label="楼层" prop="floor">
        <el-input
          v-model="queryParams.floor"
          placeholder="请输入楼层"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="打料时间" prop="uptime">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.uptime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择打料时间"
        >
        </el-date-picker>
      </el-form-item>

      <!-- <el-form-item label="网络" prop="network">
        <el-input
          v-model="queryParams.network"
          placeholder="请输入网络"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网关id" prop="switchid">
        <el-input
          v-model="queryParams.switchid"
          placeholder="请输入网关id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> 
      <el-form-item label="备注" prop="text">
        <el-input
          v-model="queryParams.text"
          placeholder="请输入备注"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:louceng:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:louceng:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:louceng:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:louceng:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="loucengList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="网关id" align="center" prop="id" />
      <el-table-column label="楼层" align="center" prop="floor" />
      <el-table-column
        label="打料时间"
        align="center"
        prop="uptime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.uptime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="保育" align="center" prop="baoyu" />
      <el-table-column label="育肥" align="center" prop="yufei" />
      <el-table-column label="分娩" align="center" prop="fenmian" />
      <el-table-column label="妊娠" align="center" prop="rensheng" />
      <!-- <el-table-column label="妊娠" align="center" prop="double1" />
      <el-table-column label="妊娠" align="center" prop="double2" />
      <el-table-column label="妊娠" align="center" prop="double3" />
      <el-table-column label="妊娠" align="center" prop="double4" /> -->
      <el-table-column label="网络" align="center" prop="network" />
      <el-table-column label="网关id" align="center" prop="switchid" />
      <el-table-column label="备注" align="center" prop="text" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:louceng:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:louceng:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【楼层用料数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="楼层" prop="floor">
          <el-input v-model="form.floor" placeholder="请输入楼层" />
        </el-form-item>
        <el-form-item label="打料时间" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.uptime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择打料时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保育" prop="baoyu">
          <el-input v-model="form.baoyu" placeholder="请输入保育" />
        </el-form-item>
        <el-form-item label="育肥" prop="yufei">
          <el-input v-model="form.yufei" placeholder="请输入育肥" />
        </el-form-item>
        <el-form-item label="分娩" prop="fenmian">
          <el-input v-model="form.fenmian" placeholder="请输入分娩" />
        </el-form-item>
        <el-form-item label="妊娠" prop="rensheng">
          <el-input v-model="form.rensheng" placeholder="请输入妊娠" />
        </el-form-item>
        <!-- <el-form-item label="妊娠" prop="double1">
          <el-input v-model="form.double1" placeholder="请输入妊娠" />
        </el-form-item>
        <el-form-item label="妊娠" prop="double2">
          <el-input v-model="form.double2" placeholder="请输入妊娠" />
        </el-form-item>
        <el-form-item label="妊娠" prop="double3">
          <el-input v-model="form.double3" placeholder="请输入妊娠" />
        </el-form-item>
        <el-form-item label="妊娠" prop="double4">
          <el-input v-model="form.double4" placeholder="请输入妊娠" />
        </el-form-item> -->
        <el-form-item label="网络" prop="network">
          <el-input v-model="form.network" placeholder="请输入网络" />
        </el-form-item>
        <el-form-item label="网关id" prop="switchid">
          <el-input v-model="form.switchid" placeholder="请输入网关id" />
        </el-form-item>
        <el-form-item label="备注" prop="text">
          <el-input v-model="form.text" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listLouceng,
  getLouceng,
  delLouceng,
  addLouceng,
  updateLouceng,
  exportLouceng,
} from "@/api/liaota/floorTableData";

export default {
  name: "Louceng",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【楼层用料数据】表格数据
      loucengList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        floor: null,
        uptime: null,
        baoyu: null,
        yufei: null,
        fenmian: null,
        rensheng: null,
        double1: null,
        double2: null,
        double3: null,
        double4: null,
        network: null,
        switchid: null,
        text: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【楼层用料数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      listLouceng(this.queryParams).then((response) => {
        this.loucengList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        floor: null,
        uptime: null,
        baoyu: null,
        yufei: null,
        fenmian: null,
        rensheng: null,
        double1: null,
        double2: null,
        double3: null,
        double4: null,
        network: null,
        switchid: null,
        text: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【楼层用料数据】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getLouceng(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【楼层用料数据】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateLouceng(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.mfactory = this.$store.state.settings.nowPigFarm;
            addLouceng(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【楼层用料数据】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delLouceng(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【楼层用料数据】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportLouceng(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>