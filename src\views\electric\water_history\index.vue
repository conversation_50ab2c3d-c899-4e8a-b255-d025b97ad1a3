<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('huanKong.pigHouseUnit')" prop="value">
        <el-cascader
          :required="true"
          v-model="queryParams.value"
          :options="options"
          size="small"
          clearable
          @change="handleNodeClick"
          :placeholder="$t('huanKong.choosePigHouse')"
        ></el-cascader>
      </el-form-item>
      <!-- <el-form-item label="环控id" prop="slaveid">
          <el-input
            v-model="queryParams.slaveid"
            placeholder="请输入环控id"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="环控名" prop="slavename">
          <el-input
            v-model="queryParams.slavename"
            placeholder="请输入环控名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
      <el-form-item :label="$t('huanKong.gatewayId')" prop="switchid">
        <el-input
          v-model="queryParams.switchid"
          :placeholder="$t('huanKong.chooseGatewayId')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('huanKong.selectDate')" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('huanKong.startDate')"
          :end-placeholder="$t('huanKong.endDate')"
        ></el-date-picker>
      </el-form-item>

      <!-- <el-form-item :label="$t('common.uploadTime')" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="queryParams.uptime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.chooseUploadTime')"
          >
          </el-date-picker>
        </el-form-item> -->
      <el-form-item :label="$t('huanKong.envWorkStatus')" prop="slavenetwork">
        <el-select
          v-model="queryParams.slavenetwork"
          :placeholder="$t('huanKong.chooseEnvWorkStatus')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="dict in slavenetworkList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['huanKong:historyData:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['huanKong:historyData:edit']"
          >{{ $t("common.update") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['huanKong:historyData:remove']"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['huanKong:historyData:export']"
          >{{ $t("common.export") }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="historyList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('common.serialNumber')"
        align="center"
        prop="id"
      />
      <el-table-column
        :label="$t('huanKong.envControlId')"
        align="center"
        prop="slaveid"
      />
      <el-table-column
        :label="$t('huanKong.envControlName')"
        align="center"
        prop="slavename"
        width="180"
      />
      <el-table-column
        :label="$t('huanKong.gatewayId')"
        align="center"
        prop="switchid"
      />
      <el-table-column
        :label="$t('huanKong.pigHouseID')"
        align="center"
        prop="homeid"
      />
      <el-table-column
        :label="$t('common.uploadTime')"
        align="center"
        prop="uptime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.uptime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('huanKong.envWorkStatus')"
        align="center"
        prop="slavenetwork"
        width="100"
      >
        <template slot-scope="scope">
          <el-tag
            effect="dark"
            :type="scope.row.slavenetwork === 'ok' ? 'success' : 'danger'"
            disable-transitions
            >{{
              scope.row.slavenetwork === "ok"
                ? $t("liaota.online")
                : $t("liaota.offline")
            }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('huanKong.waterLevel')"
        align="center"
        prop="water"
      />
      <el-table-column
        :label="$t('huanKong.alarmInformation')"
        align="center"
        prop="alarmmsg"
        show-overflow-tooltip
      />

      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
        width="100"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['huanKong:historyData:edit']"
          ></el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['huanKong:historyData:remove']"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【历史数据】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item :label="$t('huanKong.envControlId')" prop="slaveid">
          <el-input
            v-model="form.slaveid"
            :placeholder="$t('huanKong.chooseEnvControlId')"
          />
        </el-form-item>
        <el-form-item :label="$t('huanKong.envControlName')" prop="slavename">
          <el-input
            v-model="form.slavename"
            :placeholder="$t('huanKong.chooseEnvControlName')"
          />
        </el-form-item>
        <el-form-item :label="$t('huanKong.gatewayId')" prop="switchid">
          <el-input
            v-model="form.switchid"
            :placeholder="$t('huanKong.chooseGatewayId')"
          />
        </el-form-item>
        <el-form-item :label="$t('huanKong.pigHouseName')" prop="homeid">
          <el-select
            v-model="form.homeid"
            :placeholder="$t('huanKong.choosePigHouseName')"
            clearable
            size="small"
          >
            <el-option
              v-for="item in homeList"
              :key="item.id"
              :label="item.homename"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item :label="$t('huanKong.pigHouseID')" prop="homeid">
            <el-input
              v-model="form.homeid"
              :placeholder="$t('huanKong.choosePigHouseID')"
            />
          </el-form-item> -->
        <el-form-item :label="$t('common.uploadTime')" prop="uptime">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.uptime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('common.chooseUploadTime')"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('huanKong.envWorkStatus')" prop="slavenetwork">
          <el-select
            v-model="form.slavenetwork"
            :placeholder="$t('huanKong.chooseEnvWorkStatus')"
          >
            <el-option
              v-for="dict in slavenetworkList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('huanKong.water')" prop="water">
          <el-input
            v-model="form.water"
            :placeholder="$t('huanKong.enterWater')"
          />
        </el-form-item>
        <el-form-item :label="$t('huanKong.alarmInformation')" prop="alarmmsg">
          <el-input
            v-model="form.alarmmsg"
            :placeholder="$t('huanKong.enterAlarmInformation')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("common.determine")
        }}</el-button>
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import {
  listHistory,
  getHistory,
  delHistory,
  addHistory,
  updateHistory,
  exportHistory,
} from "@/api/huanKong/history";
import { treeselectEnv } from "@/api/huanKong/slave";
import { listHome } from "@/api/huanKong/home";

export default {
  name: "History",
  components: {},
  data() {
    return {
      homeList: [],
      options: null,
      //设备状态
      slavenetworkList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【历史数据】表格数据
      historyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //定频风机开
      dpfanurlOn: require("@/assets/icons/environment_svg/fengji2.png"),
      //定频风机1关
      dpfanurlOff: require("@/assets/icons/environment_svg/fengji2_off.png"),

      //变频风机1图片url
      bpfanurlOn: require("@/assets/icons/environment_svg/fengsgif.gif"),
      //变频风机1图片url
      bpfanurlOff: require("@/assets/icons/environment_svg/fengsoffgif.gif"),

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        slavename: null,
        switchid: null,
        homeid: null,
        uptime: null,
        slavenetwork: null,
        temperature: null,
        humidity: null,
        water: null,
        curtain1: null,
        curtain2: null,
        curtain3: null,
        curtain4: null,
        dpfan1: null,
        dpfan2: null,
        dpfan3: null,
        dpfan4: null,
        dpfan5: null,
        dpfan6: null,
        dpfan7: null,
        dpfan8: null,
        dpfan9: null,
        dpfan10: null,
        dpfan11: null,
        dpfan12: null,
        dpfan13: null,
        dpfan14: null,
        dpfan15: null,
        dpfan16: null,
        bpfan1: null,
        bpfan2: null,
        bpfan3: null,
        bpfan4: null,
        alarmmsg: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        slaveid: [
          {
            required: true,
            message: this.$t("huanKong.envControlIdNotNull"),
            trigger: "blur",
          },
        ],
        slavename: [
          {
            required: true,
            message: this.$t("huanKong.envControlNameNotNull"),
            trigger: "blur",
          },
        ],
        switchid: [
          {
            required: true,
            message: this.$t("huanKong.gatewayIdNotNull"),
            trigger: "blur",
          },
        ],
        homeid: [
          {
            required: true,
            message: this.$t("huanKong.pigHouseIDNotNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  mounted() {
    this.getTree();
    this.getFacidList();
  },
  created() {
    this.getList();
    this.getDicts("hk_work_status").then((response) => {
      this.slavenetworkList = response.data;
    });
  },
  methods: {
    getFacidList() {
      // this.queryParams.facid = this.$store.state.settings.nowPigFarm;
      listHome({ facid: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.homeList = response.rows;
        }
      );
    },
    /** 查询【请填写功能名称】列表 */
    getTree() {
      treeselectEnv({ facid: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.options = response.data;
          // this.value = [this.options[0].value, this.options[0].children[0].value];
          // this.queryParams.slaveid = this.options[0].children[0].value;
          // this.queryParams.homeid = this.options[0].value;
        }
      );
    },
    // 节点单击事件
    handleNodeClick(data) {
      if (this.queryParams) {
        this.queryParams.slaveid = data[1];
        this.queryParams.homeid = data[0];

        this.handleQuery();
      }
    },
    /** 查询【历史数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.facid = this.$store.state.settings.nowPigFarm;
      listHistory(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        this.historyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        slaveid: null,
        slavename: null,
        switchid: null,
        homeid: null,
        uptime: null,
        slavenetwork: null,
        temperature: null,
        humidity: null,
        water: null,
        curtain1: null,
        curtain2: null,
        curtain3: null,
        curtain4: null,
        dpfan1: null,
        dpfan2: null,
        dpfan3: null,
        dpfan4: null,
        dpfan5: null,
        dpfan6: null,
        dpfan7: null,
        dpfan8: null,
        dpfan9: null,
        dpfan10: null,
        dpfan11: null,
        dpfan12: null,
        dpfan13: null,
        dpfan14: null,
        dpfan15: null,
        dpfan16: null,
        bpfan1: null,
        bpfan2: null,
        bpfan3: null,
        bpfan4: null,
        alarmmsg: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.slaveid = null;
      this.queryParams.homeid = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("huanKong.addHistoryData");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getHistory(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("huanKong.modHistoryData");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateHistory(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.facid = this.$store.state.settings.nowPigFarm;
            addHistory(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.$t("huanKong.sureCancelHistoryData") +
          `"` +
          ids +
          `"` +
          this.$t("huanKong.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delHistory(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$confirm(
        this.$t("huanKong.sureExportHistoryData"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          return exportHistory(
            this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
          );
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>
  <style scoped>
.gif {
  /* -webkit-transform: rotate (360deg); */
  animation: rotation 1s linear infinite;
  -moz-animation: rotation 1s linear infinite;
  -webkit-animation: rotation 1s linear infinite;
  -o-animation: rotation 1s linear infinite;
}
@keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

@-webkit-keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}
</style>
  