<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="选择日期" prop="datetime">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.datetime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择当前日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:liaotadaytable:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="loucengList">
      <!-- <el-table-column type="selection" width="55" align="center" />        @selection-change="handleSelectionChange"-->
      <el-table-column label="料塔名称" align="center" prop="slavename" />
      <el-table-column label="单位" align="center" prop="danwei">
          <span>吨</span>
      </el-table-column>
      <el-table-column label="日期" align="center" prop="datetime">
        <template slot-scope="scope">
          <!-- {{ scope.row.datetime | formatUTC }} -->
          <span>{{ parseTime(scope.row.datetime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="饲料类型" align="center" prop="feedtype" />
      <el-table-column label="昨日库存" align="center" prop="yesdayweight" />
      <el-table-column
        label="前日库存"
        align="center"
        prop="beforeyesdayweight"
      />
      <el-table-column label="昨日购入" align="center" prop="yesdayweightin" />
      <el-table-column label="昨日消耗" align="center" prop="yesdayweightout" />
    </el-table>
  </div>
</template>
    
    <script>
import {
  listLiaotaDayTable,
  exportLiaotaDayTable,
} from "@/api/liaota/liaotadaytable";
import { formatDay } from "@/utils";

export default {
  name: "Dayreport",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【楼层用料数据】表格数据
      loucengList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: null,
        pageSize: null,
        datetime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    var preDate = formatDay(new Date().getTime());
    this.queryParams.datetime = preDate;
    this.getList();
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
  },
  methods: {
    /** 查询【楼层用料数据】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;

      listLiaotaDayTable(this.queryParams).then((response) => {
        this.loucengList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 多选框选中数据
    //   handleSelectionChange(selection) {
    //     this.ids = selection.map((item) => item.id);
    //     this.single = selection.length !== 1;
    //     this.multiple = !selection.length;
    //   },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【料塔库存日报】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportLiaotaDayTable(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>