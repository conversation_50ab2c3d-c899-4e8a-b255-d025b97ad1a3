import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listRecharge(query) {
  return request({
    url: '/system/recharge/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getRecharge(id) {
  return request({
    url: '/system/recharge/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addRecharge(data) {
  return request({
    url: '/system/recharge',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateRecharge(data) {
  return request({
    url: '/system/recharge',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delRecharge(id) {
  return request({
    url: '/system/recharge/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportRecharge(query) {
  return request({
    url: '/system/recharge/export',
    method: 'get',
    params: query
  })
}


export function addFactoryRecharge(data) {
  return request({
    url: '/system/factory/charge',
    method: 'post',
    data: data
  })
}
