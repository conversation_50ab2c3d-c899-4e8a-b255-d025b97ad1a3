import request from '@/utils/request'


// /system/electricalarmpermissions/listQuery
export function listAlarmSimple(query) {
  return request({
    url: '/system/electricalarm/simpleList',
    method: 'get',
    params: query
  })
}

// 查询监控节点分配列表
export function listDistribution(query) {
  return request({
    url: '/system/electricalarmpermissions/listQuery',
    method: 'get',
    params: query
  })
}
// // 查询监控节点分配详细
// export function getDistribution(id) {
//   return request({
//     url: '/system/electricalarmpermissions' + id,
//     method: 'get'
//   })
// }

// 新增监控节点分配
// export function addDistribution(data) {
//   return request({
//     url: '/system/electricalarmpermissions',
//     method: 'post',
//     data: data
//   })
// }

// 修改监控节点分配
export function updateDistribution(data) {
  return request({
    url: '/system/electricalarmpermissions/batchEdit',
    method: 'put',
    data: data
  })
}
// 删除监控节点分配
export function delDistribution(data) {
  return request({
    url: '/system/electricalarmpermissions/batchRemove',
    method: 'delete',
    data: data
  })
}
// // 删除监控节点分配
// export function delDistribution(id) {
//   return request({
//     url: '/system/electricalarmpermissions/' + id,
//     method: 'delete'
//   })
// }

// // 导出监控节点分配
// export function exportDistribution(query) {
//   return request({
//     url: '/system/electricalarmpermissions/export',
//     method: 'get',
//     params: query
//   })
// }
