//分栏猪管理
import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listPigdatadaily(query) {
  return request({
    url: '/system/pigdatadaily/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getPigdatadaily(id) {
  return request({
    url: '/system/pigdatadaily/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addPigdatadaily(data) {
  return request({
    url: '/system/pigdatadaily',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updatePigdatadaily(data) {
  return request({
    url: '/system/pigdatadaily',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delPigdatadaily(id) {
  return request({
    url: '/system/pigdatadaily/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportPigdatadaily(query) {
  return request({
    url: '/system/pigdatadaily/export',
    method: 'get',
    params: query
  })
}


export function outLan(data) {
  return request({
    url: '/system/pigdatadaily/chulan',
    method: 'put',
    data: data
  })
}
