import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listLiaotaDayTable(query) {
  return request({
    url: '/system/tongji/liaotadaytable',
    method: 'get',
    params: query
  })
}
// 路太硬:
// 导出：  /system/tongji/liaotatableexport    参数：mfactory，datetime

// 路太硬:
// /system/tongji/liaotadaytable


// 导出【请填写功能名称】  /system/tongji/daytableexport
export function exportLiaotaDayTable(query) {
  return request({
    url: '/system/tongji/liaotadaytableexport',
    method: 'get',
    params: query
  })
}