import request from '@/utils/request'

export function checkTime(data) {
  return request({
    url: '/system/boarcontrolmqtt/checktime',
    method: 'post',
    data: data
  })
}

//单个下发参数保存

export function boarControlMqtt(data) {
  return request({
    url: '/system/boarcontrolmqtt',
    method: 'put',
    data: data
  })
}

// 查询【请填写功能名称】列表
export function listControl(query) {
  return request({
    url: '/system/control/list',
    method: 'get',
    params: query
  })
}
//猪场控制器目录下拉树
export function treeselect(query) {
    return request({
        url: '/system/control/treeselect',
        method: 'get',
        params: query
    })
}
// 查询【请填写功能名称】详细
export function getControl(indexn) {
  return request({
    url: '/system/control/' + indexn,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addControl(data) {
  return request({
    url: '/system/control',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateControl(data) {
  return request({
    url: '/system/control',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delControl(indexn) {
  return request({
    url: '/system/control/' + indexn,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportControl(query) {
  return request({
    url: '/system/control/export',
    method: 'get',
    params: query
  })
}