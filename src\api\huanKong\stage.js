import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listParam(query) {
  return request({
    url: '/system/jieduanparam/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getParam(id) {
  return request({
    url: '/system/jieduanparam/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addParam(data) {
  return request({
    url: '/system/jieduanparam',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateParam(data) {
  return request({
    url: '/system/jieduanparam',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delParam(id) {
  return request({
    url: '/system/jieduanparam/' + id,
    method: 'delete'
  })
}

// 导出【请填写功能名称】
export function exportParam(query) {
  return request({
    url: '/system/jieduanparam/export',
    method: 'get',
    params: query
  })
}