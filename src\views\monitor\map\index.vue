<template>
  <div class="app-container">
    <!-- <router-link target="_blank" to="/screen"> 大屏 </router-link> -->
    <!-- <baidu-map id="allmap"
       class="Map" /> -->
    <baidu-map id="allmap" @ready="mapReady" :scroll-wheel-zoom="true">
      <!-- bm-marker 就是标注点 定位在point的经纬度上 跳动的动画 animation="BMAP_ANIMATION_BOUNCE" -->
      <!-- <bm-marker
        :position="point"
       
        :icon="{
          url: require('@/assets/images/blue.png'),
          size: { width: 300, height: 100 },
        }"
      >
      </bm-marker> -->
      <!-- <div v-for="(point, index) in resultData" :key="index">
        <bm-marker
          :position="{ lng: point.lng, lat: point.lat }"
          :dragging="false"
          @mouseover="mouseOver(point)"
        >
          <bm-label
            :content="point.facname"
            :labelStyle="{ color: 'red', fontSize: '10px' }"
            :offset="{ width: -35, height: 30 }"
          />
          <bm-info-window :show="show" @close="infoWindowClose" @open="infoWindowOpen">我爱北京天安门</bm-info-window>
        </bm-marker>
      </div> -->

      <!--比例尺控件-->
      <bm-scale anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-scale>
      <!--缩放控件-->
      <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-navigation>
      <!-- <bm-geolocation
        anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
        :showAddressBar="true"
        :autoLocation="true"
      ></bm-geolocation> -->
      <bm-control anchor="BMAP_ANCHOR_BOTTOM_RIGHT" style="padding-right: 20px">
        <ul style="list-style: none">
          <!-- <el-image
                      :src="
                        require(`@/assets/images/${this.iconType(point)}`)
                      "
                    ></el-image>  -->
          <li v-for="(point, index) in listData" :key="index">
            <img :src="bindIcon(point)" align="top" />
            <!-- <el-image :src="require('@/assets/images/blue.png')"></el-image> -->

            <span
              style="position: relative; top: 5px; /* 根据需要调整偏移量 */"
              v-html="drawHtml(point)"
            ></span>
            <!-- <img :src="`@/assets/images/${this.iconType(point)}`" />  -->
            <!-- <span>bindIcon(point)</span> -->
          </li>
        </ul>
      </bm-control>
    </baidu-map>
  </div>
</template>

<script>
import { listFactory, listFactoryByUser } from "@/api/system/factory";
import blue from "@/assets/images/blue.png";
import Cookies from "js-cookie";
import store from "@/store";
export default {
  name: "Map",
  data() {
    return {
      user: store.getters && store.getters.user,
      blue,
      facWorkStatus: [],
      facTypeList: [],
      point: "",
      map: null,
      BMap: null,
      show: false,
      resultData: [],
      // // <!-- 1种猪，2母猪，3分栏4环控5料塔，6精准饲喂 -->
      // listData: [
      //   {
      //     type: "1",
      //   },
      //   {
      //     type: "2",
      //   },
      //   {
      //     type: "3",
      //   },
      //   {
      //     type: "4",
      //   },
      //   {
      //     type: "5",
      //   },
      //   {
      //     type: "6",
      //   },
      //   {
      //     type: "7",
      //   },
      // ],
      // <!-- 1在线，0离线 -->
      listData: [
        {
          type: "1",
          status: "1",
        },
        {
          type: "0",
          status: "0",
        },
      ],
    };
  },
  //   mounted: function () {
  //     this.$nextTick(() => {
  //       var map = new BMap.Map("allmap");   //初始化map, 绑定id=allmap
  //       var point = new BMap.Point(121.48789949, 31.24916171);   // 初始化point, 给定一个默认x,y值
  //       map.centerAndZoom(point, 10);        // 将point点放入map中，展示在页面中心展示，10=缩放程度
  //       map.enableScrollWheelZoom();         // 开启滚动鼠标滑轮

  //       // 如有多个point去展示，可根据后端接口传入为主
  //       let data = [
  //         { x: 116.297047, y: 39.979542, name: '张三' },
  //         { x: 116.321768, y: 39.88748, name: '李四' },
  //         { x: 116.494243, y: 39.756539, name: '王五' }
  //       ]

  //       data.forEach((e, i) => {
  //         // 创建point, 将x,y值传入
  //         let pointNumber = new BMap.Point(e.x, e.y)

  //         // 创建信息窗口对象
  //         let infoWindow = new BMap.InfoWindow("World", {
  //           width: 150,     // 信息窗口宽度
  //           height: 100,     // 信息窗口高度
  //           title: "Hello" + i  // 信息窗口标题
  //         });
  //         // 将data中的name加入地图中
  //         var label = new BMap.Label(e.name, {
  //           offset: new BMap.Size(25, 5)
  //         });
  //         markerFun(pointNumber, infoWindow, label)
  //       })

  //       function markerFun(points, infoWindows, label) {
  //         let markers = new BMap.Marker(points);
  //         map.addOverlay(markers);  // 将标注添加到地图中
  //         markers.setLabel(label);  // 将data中的name添加到地图中
  //         // 标注的点击事件
  //         markers.addEventListener("click", function (event) {
  //           map.openInfoWindow(infoWindows, points);//参数：窗口、点  根据点击的点出现对应的窗口
  //         });
  //       }

  //       // 获取当前地理位置
  //       var geolocation = new BMap.Geolocation();
  //       geolocation.getCurrentPosition(function (r) {
  //         if (this.getStatus() == BMAP_STATUS_SUCCESS) {
  //           var mk = new BMap.Marker(r.point);
  //           map.addOverlay(mk);
  //           map.panTo(r.point);
  //           // alert('您的位置：' + r.point.lng + ',' + r.point.lat);
  //         } else {
  //           // alert('failed' + this.getStatus());
  //         }
  //       });

  //     })
  //   },

  created() {
    listFactoryByUser({ userId: this.user.userId }).then((response) => {
      this.resultData = response.rows;
    });
    this.getDicts("fac_work_status").then((response) => {
      this.facWorkStatus = response.data;
    });
    this.getDicts("fac_type").then((response) => {
      this.facTypeList = response.data;
    });
  },
  methods: {
    drawHtml(point) {
      var template = `
        <span>${this.statusFormat(point)}</span> 
      `;
      return template;
    },
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.facWorkStatus, row.status);
    },
    typeFormat(row, column) {
      return this.selectDictLabel(this.facTypeList, row.type);
    },
    bindIcon(point) {
      var type = Number(point.status);
      // var status = point.status;
      // if (status == 0) {
      //   type = 7;
      // }
      var icon = null;
      switch (type) {
        case 1 || "1":
          icon = "online.png";
          break; //停止执行，跳出switch
        default:
          //上述条件都不满足时，默认执行的代码
          icon = "offline.png";
        //   console.log("游客");
      }
      // switch (type) {
      //   case 1:
      //     icon = "blue.png";
      //     break; //停止执行，跳出switch
      //   case 2:
      //     icon = "purple.png";
      //     break; //停止执行，跳出switch
      //   case 3:
      //     icon = "yellow.png";
      //     break; //停止执行，跳出switch
      //   case 4:
      //     icon = "green.png";
      //     break; //停止执行，跳出switch
      //   case 5:
      //     icon = "blue2.png";
      //     break; //停止执行，跳出switch
      //   case 6:
      //     icon = "grey.png";
      //     break; //停止执行，跳出switch
      //   default:
      //     //上述条件都不满足时，默认执行的代码
      //     icon = "grey2.png";
      //   //   console.log("游客");
      // }

      return require("@/assets/images/" + icon);
    },
    // iconType(point) {
    //   var type = point.type;
    //   var status = point.status;
    //   if (status == 0) {
    //     type = 7;
    //   }
    //   var icon = null;
    //   switch (type) {
    //     case 1:
    //       icon = "blue.png";
    //       break; //停止执行，跳出switch
    //     case 2:
    //       icon = "purple.png";
    //       break; //停止执行，跳出switch
    //     case 3:
    //       icon = "yellow.png";
    //     case 4:
    //       icon = "green.png";
    //       break; //停止执行，跳出switch
    //     case 5:
    //       icon = "blue2.png";
    //       break; //停止执行，跳出switch
    //     case 6:
    //       icon = "grey.png";
    //       break; //停止执行，跳出switch
    //     default:
    //       //上述条件都不满足时，默认执行的代码
    //       icon = "grey2.png";
    //     //   console.log("游客");
    //   }

    //   return icon;
    // },
    async mapReady({ BMap, map }) {
      this.map = map; //把map赋值给变量map
      this.BMap = BMap; //把map赋值给变量map
      // 115.7°—117.4°，北纬39.4°
      this.point = new BMap.Point(116, 39.4); // 选择一个经纬度作为中心点
      this.map.centerAndZoom(this.point, 5); //修改中心点，缩放比例
      //   this.map.enableScrollWheelZoom(true)//添加鼠标滚动，地图缩放组件
      // 如有多个point去展示，可根据后端接口传入为主
      //   let data = [
      //     { x: 116.297047, y: 39.979542, name: '张三' },
      //     { x: 116.321768, y: 39.88748, name: '李四' },
      //     { x: 116.494243, y: 39.756539, name: '王五' }
      //   ]
      await listFactoryByUser({ userId: this.user.userId }).then((response) => {
        this.resultData = response.rows;
      });
      await this.getDicts("fac_work_status").then((response) => {
        this.facWorkStatus = response.data;
      });
      await this.getDicts("fac_type").then((response) => {
        this.facTypeList = response.data;
      });

      this.resultData.forEach((e, i) => {
        // 创建point, 将x,y值传入
        let pointNumber = new BMap.Point(e.lng, e.lat);
        // 创建小车图标
        let myIcon = new BMap.Icon(
          //   require(`@/assets/images/${this.iconType(e)}`),
          `${this.bindIcon(e)}`,
          new BMap.Size(50, 50),
          {
            // 指定定位位置。
            // 当标注显示在地图上时，其所指向的地理位置距离图标左上
            // 角各偏移10像素和25像素。您可以看到在本例中该位置即是
            // 图标中央下端的尖角位置。
            anchor: new BMap.Size(15, 20),
            // 设置图片偏移。
            // 当您需要从一幅较大的图片中截取某部分作为标注图标时，您
            // 需要指定大图的偏移位置，此做法与css sprites技术类似。
            // imageOffset: new BMap.Size(50, 20)   // 设置图片偏移
          }
        );

        // 创建信息窗口对象
        let infoWindow = new BMap.InfoWindow(
          "地址:" +
            e.text +
            "<br>规模:" +
            e.scale +
            "<br>状态:" +
            this.statusFormat(e) +
            "<br>类型:" +
            this.typeFormat(e),
          {
            width: 200, // 信息窗口宽度
            height: 140, // 信息窗口高度
            title: e.facname, // 信息窗口标题
          }
        );
        // 将data中的name加入地图中

        // var label = new BMap.Label(e.facname, {
        //   // offset: new BMap.Size(-10, 30), // 设置文本偏移量
        // });
        var label = new BMap.Label(e.facname, {
          offset: new BMap.Size(0 - 20, 30), // 设置文本偏移量
        });
        // 自定义文本标注样式
        label.setStyle({
          color: "black",
          borderRadius: "5px",
          borderColor: "#ccc",
          //   padding: "10px",
          fontSize: "16px",
          //   height: "30px",
          //   lineHeight: "30px",
          //   fontFamily: "微软雅黑",
        });
        this.markerFun(pointNumber, infoWindow, label, myIcon, e);
      });

      // 获取当前地理位置
      //   var geolocation = new BMap.Geolocation();
      //   geolocation.getCurrentPosition(function (r) {
      //     if (this.getStatus() == BMAP_STATUS_SUCCESS) {
      //       var mk = new BMap.Marker(r.point);
      //       map.addOverlay(mk);
      //       map.panTo(r.point);
      //       // alert('您的位置：' + r.point.lng + ',' + r.point.lat);
      //     } else {
      //       // alert('failed' + this.getStatus());
      //     }
      //   });
    },
    markerFun(points, infoWindows, label, myIcon, e) {
      let markers = new BMap.Marker(points, {
        icon: myIcon,
      });
      this.map.addOverlay(markers); // 将标注添加到地图中
      markers.setLabel(label); // 将data中的name添加到地图中
      // 标注的点击事件
      markers.addEventListener("mouseover", function (event) {
        this.map.openInfoWindow(infoWindows, points); //参数：窗口、点  根据点击的点出现对应的窗口
      });
      // 标注的点击事件
      markers.addEventListener("mouseout", function (event) {
        this.map.closeInfoWindow(infoWindows, points); //参数：窗口、点  根据点击的点出现对应的窗口
      });
      // 标注的点击事件
      let that = this;
      markers.addEventListener("click", function (event) {
        that.$router.push(e.routeurl);
        Cookies.set("nowPigFarm", e.id);
        that.$store.dispatch("settings/changeSwitching", {
          key: "nowPigFarm",
          value: e.id,
        });
      });

      // map.panBy(580, 150); //中心点偏移多少像素（width,height）为div 宽高的1/2;
    },
    // mouseOver({ type, target, point, pixel }) {
    //   console.log("type", type, target, point, pixel);
    // },
    // mouseOver(point) {
    //   this.show = true;
    // },
  },
};
</script>
<style scoped>
#allmap {
  overflow: hidden;
  /* width: 100%; */
  height: calc(100vh - 126px);
  margin: 0;
  font-family: Helvetica;
}
</style>