import request from '@/utils/request'
///system/dailyweightday/growreports
// export function listForecast(query) {
//   return request({
//     url: '/system/dailyweightday/forecast',
//     method: 'get',
//     params: query
//   })
// }


// 新增【请填写功能名称】
export function listForecast(data) {
  return request({
    url: '/system/dailyweightday/forecast',
    method: 'post',
    data: data
  })
}

export function zeroforecast(data) {
  return request({
    url: '/system/dailyweightday/zeroforecast',
    method: 'post',
    data: data
  })
}

export function zeroforecastbydate(data) {
  return request({
    url: '/system/dailyweightday/zeroforecastbydate',
    method: 'post',
    data: data
  })
}

