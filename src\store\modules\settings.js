import variables from '@/assets/styles/element-variables.scss'
import defaultSettings from '@/settings'
import Cookies from "js-cookie";

const {
  sideTheme,
  showSettings,
  tagsView,
  fixedHeader,
  sidebarLogo,
  showSwitching,
  nowPigFarm,
  hasNotification,
} = defaultSettings

const state = {
  theme: variables.theme,
  sideTheme: sideTheme,
  showSwitching: showSwitching,
  hasNotification: hasNotification,
  nowPigFarm: Number(Cookies.get("nowPigFarm")) || nowPigFarm,
  showSettings: showSettings,
  tagsView: tagsView,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo
}

const mutations = {
  CHANGE_SETTING: (state, {
    key,
    value
  }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  }
}

const actions = {
  changeSetting({
    commit
  }, data) {
    commit('CHANGE_SETTING', data)
  },
  changeSwitching({
    commit
  }, data) {
    commit('CHANGE_SETTING', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
