<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item label="猪舍号" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          placeholder="请输入猪舍号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="选择日期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          @keyup.enter.native="handleQuery"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="栏号" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          placeholder="请输入栏号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- <el-form-item label="胎次" prop="nparity">
        <el-input
          v-model="queryParams.nparity"
          placeholder="请输入胎次"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:estrousday:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:estrousday:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:estrousday:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:estrousday:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="estrousdayList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="猪场号" align="center" prop="mfactory" />
      <el-table-column label="耳缺号" align="center" prop="mid" />
      <el-table-column label="电子耳牌" align="center" prop="mrfid" />
      <el-table-column label="猪舍号" align="center" prop="mdorm" />
      <el-table-column label="栏号" align="center" prop="nindex" />
      <el-table-column label="设备地址" align="center" prop="naddress" />
      <el-table-column label="胎次" align="center" prop="nparity" />
      <el-table-column label="日期" align="center" prop="ndate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ndate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="次数" align="center" prop="ntimes" />
      <el-table-column label="探测时长" align="center" prop="ndurationall" />
      <el-table-column label="发情指数" align="center" prop="nestrousindex" />
      <el-table-column label="备注" align="center" prop="tag" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:estrousday:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:estrousday:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【发情类总】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="猪场号" prop="mfactory">
          <el-input v-model="form.mfactory" placeholder="请输入猪场号" />
        </el-form-item>
        <el-form-item label="耳缺号" prop="mid">
          <el-input v-model="form.mid" placeholder="请输入耳缺号" />
        </el-form-item>
        <el-form-item label="电子耳牌" prop="mrfid">
          <el-input v-model="form.mrfid" placeholder="请输入电子耳牌" />
        </el-form-item>
        <el-form-item label="猪舍号" prop="mdorm">
          <el-input v-model="form.mdorm" placeholder="请输入猪舍号" />
        </el-form-item>
        <el-form-item label="栏号" prop="nindex">
          <el-input v-model="form.nindex" placeholder="请输入栏号" />
        </el-form-item>
        <el-form-item label="设备地址" prop="naddress">
          <el-input v-model="form.naddress" placeholder="请输入设备地址" />
        </el-form-item>
        <el-form-item label="胎次" prop="nparity">
          <el-input v-model="form.nparity" placeholder="请输入胎次" />
        </el-form-item>
        <el-form-item label="日期" prop="ndate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ndate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="次数" prop="ntimes">
          <el-input v-model="form.ntimes" placeholder="请输入次数" />
        </el-form-item>
        <el-form-item label="探测时长" prop="ndurationall">
          <el-input v-model="form.ndurationall" placeholder="请输入探测时长" />
        </el-form-item>
        <el-form-item label="发情指数" prop="nestrousindex">
          <el-input v-model="form.nestrousindex" placeholder="请输入发情指数" />
        </el-form-item>
        <el-form-item label="备注" prop="tag">
          <el-input v-model="form.tag" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import {
  listEstrousday,
  getEstrousday,
  delEstrousday,
  addEstrousday,
  updateEstrousday,
  exportEstrousday,
} from "@/api/faqing/estrousday";

export default {
  name: "Estrousday",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【发情类总】表格数据
      estrousdayList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        naddress: null,
        nparity: null,
        ndate: null,
        ntimes: null,
        ndurationall: null,
        nestrousindex: null,
        colStr1: null,
        colStr2: null,
        colStr3: null,
        tag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mfactory: [{ required: true, message: "不能为空", trigger: "blur" }],
        mrfid: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【发情类总】列表 */
    getList() {
      this.loading = true;
      listEstrousday(
        this.addDateRangeRe(this.queryParams, this.queryParams.dateRange)
      ).then((response) => {
        this.estrousdayList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mfactory: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        naddress: null,
        nparity: null,
        ndate: null,
        ntimes: null,
        ndurationall: null,
        nestrousindex: null,
        colStr1: null,
        colStr2: null,
        colStr3: null,
        tag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【发情类总】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEstrousday(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【发情类总】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEstrousday(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEstrousday(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除【发情类总】编号为"' + ids + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delEstrousday(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【发情类总】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportEstrousday(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>