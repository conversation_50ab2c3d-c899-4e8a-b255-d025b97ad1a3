<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
        label-width="68px"
    >
      <el-form-item label="耳缺号" prop="mid">
        <el-input
          v-model="queryParams.mid"
          placeholder="请输入耳缺号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电子耳牌" prop="mrfid">
        <el-input
          v-model="queryParams.mrfid"
          placeholder="请输入电子耳缺号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="出生日期" prop="dbirthdate">
        <el-date-picker
          clearable
          size="small"
          style="width: 200px"
          v-model="queryParams.dbirthdate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择$出生日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="猪舍号" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          placeholder="请输入猪舍号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="栏号" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          placeholder="请输入栏号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="${comment}" prop="mname">
        <el-input
          v-model="queryParams.mname"
          placeholder="请输入${comment}"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="naddress">
        <el-input
          v-model="queryParams.naddress"
          placeholder="请输入${comment}"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="dstartdate">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.dstartdate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择${comment}">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="${comment}" prop="denddate">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.denddate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择${comment}">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="${comment}" prop="nweight1">
        <el-input
          v-model="queryParams.nweight1"
          placeholder="请输入${comment}"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="nweight2">
        <el-input
          v-model="queryParams.nweight2"
          placeholder="请输入${comment}"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="ddate">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.ddate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择${comment}">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="${comment}" prop="nweight">
        <el-input
          v-model="queryParams.nweight"
          placeholder="请输入${comment}"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="ntype">
        <el-select v-model="queryParams.ntype" placeholder="请选择${comment}" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>-->
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:pigdata:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:pigdata:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:pigdata:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:pigdata:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="pigdataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="indexn" />
      <el-table-column label="耳缺号" align="center" prop="mid" />
      <el-table-column
        label="电子耳牌"
        width="200rem"
        align="center"
        prop="mrfid"
      />
      <el-table-column
        label="出生日期"
        align="center"
        prop="dbirthdate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dbirthdate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="猪舍号" align="center" prop="mdorm" />
      <el-table-column label="栏号" align="center" prop="nindex" />
      <el-table-column label="测定站" align="center" prop="mname" />
      <el-table-column label="设备地址" align="center" prop="naddress" />
      <el-table-column
        label="测定开始日期"
        align="center"
        prop="dstartdate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dstartdate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="测定结束日期"
        align="center"
        prop="denddate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.denddate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始测定体重" align="center" prop="nweight1" />
      <el-table-column label="结束测定体重" align="center" prop="nweight2" />
      <el-table-column label="测定日期" align="center" prop="ddate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ddate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="测定猪体重" align="center" prop="nweight" />
      <el-table-column label="测定状态" align="center" prop="ntype" />
      <el-table-column
        label="操作"
        align="center"
        width="150rem"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:pigdata:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:pigdata:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【种猪信息】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="${comment}" prop="mid">
          <el-input v-model="form.mid" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="mrfid">
          <el-input v-model="form.mrfid" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="dbirthdate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.dbirthdate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择${comment}"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="${comment}" prop="mdorm">
          <el-input v-model="form.mdorm" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="nindex">
          <el-input v-model="form.nindex" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="mname">
          <el-input v-model="form.mname" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="naddress">
          <el-input v-model="form.naddress" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="dstartdate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.dstartdate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择${comment}"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="${comment}" prop="denddate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.denddate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择${comment}"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="${comment}" prop="nweight1">
          <el-input v-model="form.nweight1" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="nweight2">
          <el-input v-model="form.nweight2" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="ddate">
          <el-date-picker
            clearable
            size="small"
            style="width: 200px"
            v-model="form.ddate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择${comment}"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="${comment}" prop="nweight">
          <el-input v-model="form.nweight" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="ntype">
          <el-select v-model="form.ntype" placeholder="请选择${comment}">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPigdata,
  getPigdata,
  delPigdata,
  addPigdata,
  updatePigdata,
  exportPigdata,
} from "@/api/system/pigdata";

export default {
  name: "Pigdata",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【种猪信息】表格数据
      pigdataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mid: null,
        mrfid: null,
        dbirthdate: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        ddate: null,
        nweight: null,
        ntype: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mid: [{ required: true, message: "$comment不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【种猪信息】列表 */
    getList() {
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      this.loading = true;
      listPigdata(this.queryParams).then((response) => {
        this.pigdataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        mid: null,
        mrfid: null,
        dbirthdate: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        ddate: null,
        nweight: null,
        ntype: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【种猪信息】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.indexn || this.ids;
      getPigdata(indexn).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【种猪信息】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.indexn != null) {
            updatePigdata(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPigdata(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        '是否确认删除【种猪信息】编号为"' + indexns + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delPigdata(indexns);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有【种猪信息】数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportPigdata(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>
