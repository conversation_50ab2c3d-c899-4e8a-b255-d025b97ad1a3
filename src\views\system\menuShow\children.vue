<template>
  <div class="menu-container">
    <el-row :gutter="20">
      <el-col
        :span="4"
        :xs="24"
        :sm="24"
        :md="4"
        :lg="4"
        v-for="(item, index) in sidebarRouters"
        :key="index"
      >
        <el-card class="menu-content">
          <div class="icon-container" @click="handleItemClick(item.path)">
            <svg-icon
              class="icon"
              :style="item.hidden ? 'fill:#909399 !important' : ''"
              :icon-class="item.meta && item.meta.icon"
            />
          </div>
          <!-- <div class="menu"> -->
          <div class="menu-text" :style="item.hidden ? 'color:#909399' : ''">
            {{ $t(item.meta.title) }}
          </div>
          <!-- </div> -->
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
  
<script>
import { isExternal } from "@/utils/validate";
import path from "path";
export default {
  name: "module",
  data() {
    return {
      query: {},
    };
  },
  created() {
    // 访问查询参数
    this.query = this.$route.query;
    const flattenedArray = this.flattenArray(
      this.$store.getters.sidebarRouters[this.query.id].children
    );
  },
  computed: {
    sidebarRouters() {
      return this.flattenArray(
        this.$store.getters.sidebarRouters[this.query.id].children
      );
    },
  },
  methods: {
    handleItemClick(routePath) {
      if (isExternal(routePath)) {
        return this.$router.replace(routePath);
      }
      return this.$router.replace(path.resolve(this.query.path, routePath));
    },

    flattenArray(arr, alwaysShow = false, parentPath = "") {
      return arr.reduce((flatArray, item) => {
        // 如果当前项有 children 数组，则递归调用 flattenArray 函数将其平铺
        // 计算当前项的完整路径
        const fullPath = alwaysShow
          ? `${parentPath}/${item.path}`
          : `${item.path}`;
        // 合并外层的 path 到 children 中的每个项的 path 中
        const newItem = { ...item, path: fullPath };
        if (Array.isArray(item.children)) {
          flatArray.push(
            ...this.flattenArray(item.children, item.alwaysShow, item.path)
          );
        }
        if (item.alwaysShow) {
          return flatArray;
        }
        flatArray.push(newItem);
        return flatArray;
      }, []);
    },
  },
};
</script>
  
<style scoped>
.menu-container {
  padding: 20px;
}

.el-card {
  margin-bottom: 20px;
  width: 100%; /* 设置宽度为容器的 100% */
}

.icon:hover {
  width: 80%; /* 设置宽度为容器的 100% */
  height: auto; /* 设置高度自适应 */
  fill: #00833e !important; /* 使用当前文本颜色填充 SVG 图标 */
}
.menu-text {
  font-size: 20px;
  /* text-align: center; 将内联元素居中 */
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  height: 30px;
  /* overflow: visible;
  overflow-wrap: break-word; */
  /* width: 90%; 设置宽度为容器的 100% */
}

.icon-container {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  width: 100%; /* 设置宽度为容器的 100% */
  height: auto; /* 设置高度自适应 */
}

.icon {
  width: 60%; /* 设置宽度为容器的 100% */
  height: auto; /* 设置高度自适应 */
  fill: #4bd7ad !important; /* 使用当前文本颜色填充 SVG 图标 */
}
</style>